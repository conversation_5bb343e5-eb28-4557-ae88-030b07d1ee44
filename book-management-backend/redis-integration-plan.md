# Redis集成方案设计文档

## 方案概述

### 🎯 **集成目标**
1. **会话管理优化**：使用Redis存储用户会话信息
2. **Token黑名单机制**：支持token撤销和安全登出
3. **分布式会话支持**：为系统扩展做准备
4. **性能优化**：缓存用户信息，减少数据库查询

### 📋 **技术架构**
- **当前方案**：纯JWT无状态认证
- **目标方案**：Redis+JWT混合方案
- **迁移策略**：渐进式集成，保持向后兼容

## 技术实现方案

### 1. **后端Redis集成**

#### **依赖配置 (pom.xml)**
```xml
<!-- Redis依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- Redis连接池 -->
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-pool2</artifactId>
</dependency>
```

#### **Redis配置 (application.properties)**
```properties
# Redis配置
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
spring.redis.database=0
spring.redis.timeout=2000ms

# Redis连接池配置
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.pool.max-wait=-1ms

# 会话管理配置
session.redis.key-prefix=book_mgmt:session:
session.redis.expire-time=86400
session.blacklist.key-prefix=book_mgmt:blacklist:
```

#### **Redis配置类**
```java
@Configuration
@EnableCaching
public class RedisConfig {
    
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        
        // 设置序列化器
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LazyLoadingEnabled.EAGER, ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(mapper);
        
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(serializer);
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);
        
        template.afterPropertiesSet();
        return template;
    }
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory factory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(factory)
                .cacheDefaults(config)
                .build();
    }
}
```

### 2. **会话管理服务**

#### **会话管理接口**
```java
public interface SessionService {
    
    /**
     * 创建用户会话
     */
    void createSession(String token, Long userId, String username, String role);
    
    /**
     * 获取会话信息
     */
    SessionInfo getSession(String token);
    
    /**
     * 更新会话活跃时间
     */
    void updateSessionActivity(String token);
    
    /**
     * 删除会话
     */
    void removeSession(String token);
    
    /**
     * 添加token到黑名单
     */
    void addToBlacklist(String token);
    
    /**
     * 检查token是否在黑名单中
     */
    boolean isTokenBlacklisted(String token);
    
    /**
     * 获取用户所有活跃会话
     */
    List<SessionInfo> getUserActiveSessions(Long userId);
    
    /**
     * 强制用户下线（删除所有会话）
     */
    void forceUserOffline(Long userId);
}
```

#### **会话信息实体**
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SessionInfo {
    private String token;
    private Long userId;
    private String username;
    private String role;
    private LocalDateTime loginTime;
    private LocalDateTime lastActivity;
    private String ipAddress;
    private String userAgent;
    private Map<String, Object> attributes;
}
```

#### **会话管理实现**
```java
@Service
@RequiredArgsConstructor
@Slf4j
public class SessionServiceImpl implements SessionService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    @Value("${session.redis.key-prefix:book_mgmt:session:}")
    private String sessionKeyPrefix;
    
    @Value("${session.redis.expire-time:86400}")
    private long sessionExpireTime;
    
    @Value("${session.blacklist.key-prefix:book_mgmt:blacklist:}")
    private String blacklistKeyPrefix;
    
    @Override
    public void createSession(String token, Long userId, String username, String role) {
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setToken(token);
        sessionInfo.setUserId(userId);
        sessionInfo.setUsername(username);
        sessionInfo.setRole(role);
        sessionInfo.setLoginTime(LocalDateTime.now());
        sessionInfo.setLastActivity(LocalDateTime.now());
        
        // 获取请求信息
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            sessionInfo.setIpAddress(getClientIp(request));
            sessionInfo.setUserAgent(request.getHeader("User-Agent"));
        }
        
        String sessionKey = sessionKeyPrefix + token;
        redisTemplate.opsForValue().set(sessionKey, sessionInfo, Duration.ofSeconds(sessionExpireTime));
        
        // 维护用户会话列表
        String userSessionsKey = "user_sessions:" + userId;
        redisTemplate.opsForSet().add(userSessionsKey, token);
        redisTemplate.expire(userSessionsKey, Duration.ofSeconds(sessionExpireTime));
        
        log.info("创建用户会话 - 用户ID: {}, Token: {}", userId, token.substring(0, 20) + "...");
    }
    
    @Override
    public SessionInfo getSession(String token) {
        String sessionKey = sessionKeyPrefix + token;
        return (SessionInfo) redisTemplate.opsForValue().get(sessionKey);
    }
    
    @Override
    public void updateSessionActivity(String token) {
        SessionInfo session = getSession(token);
        if (session != null) {
            session.setLastActivity(LocalDateTime.now());
            String sessionKey = sessionKeyPrefix + token;
            redisTemplate.opsForValue().set(sessionKey, session, Duration.ofSeconds(sessionExpireTime));
        }
    }
    
    @Override
    public void removeSession(String token) {
        SessionInfo session = getSession(token);
        if (session != null) {
            // 从会话存储中删除
            String sessionKey = sessionKeyPrefix + token;
            redisTemplate.delete(sessionKey);
            
            // 从用户会话列表中删除
            String userSessionsKey = "user_sessions:" + session.getUserId();
            redisTemplate.opsForSet().remove(userSessionsKey, token);
            
            log.info("删除用户会话 - 用户ID: {}, Token: {}", session.getUserId(), token.substring(0, 20) + "...");
        }
    }
    
    @Override
    public void addToBlacklist(String token) {
        String blacklistKey = blacklistKeyPrefix + token;
        // 黑名单过期时间设置为token的剩余有效期
        long expireTime = getTokenRemainingTime(token);
        if (expireTime > 0) {
            redisTemplate.opsForValue().set(blacklistKey, true, Duration.ofSeconds(expireTime));
            log.info("Token已加入黑名单: {}", token.substring(0, 20) + "...");
        }
    }
    
    @Override
    public boolean isTokenBlacklisted(String token) {
        String blacklistKey = blacklistKeyPrefix + token;
        return Boolean.TRUE.equals(redisTemplate.hasKey(blacklistKey));
    }
    
    @Override
    public List<SessionInfo> getUserActiveSessions(Long userId) {
        String userSessionsKey = "user_sessions:" + userId;
        Set<Object> tokens = redisTemplate.opsForSet().members(userSessionsKey);
        
        if (tokens == null || tokens.isEmpty()) {
            return Collections.emptyList();
        }
        
        return tokens.stream()
                .map(token -> getSession((String) token))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    @Override
    public void forceUserOffline(Long userId) {
        List<SessionInfo> sessions = getUserActiveSessions(userId);
        for (SessionInfo session : sessions) {
            addToBlacklist(session.getToken());
            removeSession(session.getToken());
        }
        
        // 清除用户会话列表
        String userSessionsKey = "user_sessions:" + userId;
        redisTemplate.delete(userSessionsKey);
        
        log.info("强制用户下线 - 用户ID: {}, 会话数量: {}", userId, sessions.size());
    }
    
    private long getTokenRemainingTime(String token) {
        try {
            // 解析JWT获取过期时间
            Claims claims = Jwts.parser()
                    .setSigningKey(jwtSecret.getBytes())
                    .parseClaimsJws(token)
                    .getBody();
            
            Date expiration = claims.getExpiration();
            long remaining = (expiration.getTime() - System.currentTimeMillis()) / 1000;
            return Math.max(remaining, 0);
        } catch (Exception e) {
            log.warn("解析token过期时间失败: {}", e.getMessage());
            return 0;
        }
    }
    
    private HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            return attributes.getRequest();
        } catch (Exception e) {
            return null;
        }
    }
    
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
```

### 3. **增强JWT认证过滤器**

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class EnhancedJwtAuthenticationFilter extends OncePerRequestFilter {
    
    private final JwtUtil jwtUtil;
    private final SessionService sessionService;
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String requestURI = request.getRequestURI();
        log.debug("JWT过滤器处理请求: {}", requestURI);
        
        String token = getTokenFromRequest(request);
        
        if (token != null) {
            try {
                // 1. 检查token是否在黑名单中
                if (sessionService.isTokenBlacklisted(token)) {
                    log.warn("Token在黑名单中: {}", requestURI);
                    filterChain.doFilter(request, response);
                    return;
                }
                
                // 2. 验证JWT token
                if (jwtUtil.validateToken(token)) {
                    // 3. 检查Redis会话
                    SessionInfo session = sessionService.getSession(token);
                    if (session != null) {
                        // 4. 更新会话活跃时间
                        sessionService.updateSessionActivity(token);
                        
                        // 5. 设置认证信息
                        UsernamePasswordAuthenticationToken authentication =
                            new UsernamePasswordAuthenticationToken(
                                session.getUsername(),
                                null,
                                Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + session.getRole()))
                            );
                        
                        authentication.setDetails(session.getUserId());
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        
                        log.debug("JWT验证成功 - 用户: {}, 角色: {}, ID: {}", 
                                session.getUsername(), session.getRole(), session.getUserId());
                    } else {
                        log.warn("Redis中未找到会话信息: {}", requestURI);
                    }
                } else {
                    log.warn("JWT token验证失败: {}", requestURI);
                }
            } catch (Exception e) {
                log.error("JWT token解析异常: {}, 错误: {}", requestURI, e.getMessage());
            }
        } else if (!requestURI.startsWith("/api/auth/")) {
            log.debug("请求缺少JWT token: {}", requestURI);
        }
        
        filterChain.doFilter(request, response);
    }
    
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
```

### 4. **增强认证控制器**

```java
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Slf4j
public class EnhancedAuthController {
    
    private final UserService userService;
    private final SessionService sessionService;
    private final JwtUtil jwtUtil;
    
    @PostMapping("/login")
    public Result<LoginVO> login(@Validated @RequestBody UserLoginDTO loginDTO, 
                               HttpServletRequest request) {
        try {
            LoginVO loginVO = userService.login(loginDTO);
            
            // 创建Redis会话
            sessionService.createSession(
                loginVO.getToken(),
                loginVO.getUser().getId(),
                loginVO.getUser().getUsername(),
                loginVO.getUser().getRole()
            );
            
            log.info("用户登录成功 - 用户名: {}, 用户ID: {}", 
                    loginDTO.getUsername(), loginVO.getUser().getId());
            
            return Result.success("登录成功", loginVO);
        } catch (Exception e) {
            log.warn("用户登录失败 - 用户名: {}, 错误: {}", loginDTO.getUsername(), e.getMessage());
            throw e;
        }
    }
    
    @PostMapping("/logout")
    public Result<String> logout(HttpServletRequest request) {
        String token = jwtUtil.getTokenFromRequest(request);
        
        if (token != null) {
            // 添加到黑名单
            sessionService.addToBlacklist(token);
            // 删除会话
            sessionService.removeSession(token);
            
            log.info("用户登出成功 - Token: {}", token.substring(0, 20) + "...");
        }
        
        return Result.success("登出成功");
    }
    
    @PostMapping("/refresh")
    public Result<LoginVO> refreshToken(HttpServletRequest request) {
        String token = jwtUtil.getTokenFromRequest(request);
        
        if (token == null || sessionService.isTokenBlacklisted(token)) {
            throw new BusinessException("无效的token");
        }
        
        SessionInfo session = sessionService.getSession(token);
        if (session == null) {
            throw new BusinessException("会话已过期");
        }
        
        // 生成新token
        String newToken = jwtUtil.generateToken(session.getUsername(), session.getRole(), session.getUserId());
        
        // 将旧token加入黑名单
        sessionService.addToBlacklist(token);
        sessionService.removeSession(token);
        
        // 创建新会话
        sessionService.createSession(newToken, session.getUserId(), session.getUsername(), session.getRole());
        
        // 构建返回数据
        UserVO userVO = new UserVO();
        userVO.setId(session.getUserId());
        userVO.setUsername(session.getUsername());
        userVO.setRole(session.getRole());
        
        LoginVO loginVO = new LoginVO(newToken, userVO);
        
        log.info("Token刷新成功 - 用户ID: {}", session.getUserId());
        return Result.success("Token刷新成功", loginVO);
    }
    
    @GetMapping("/sessions")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<List<SessionInfo>> getUserSessions(Authentication authentication) {
        Long userId = (Long) authentication.getDetails();
        List<SessionInfo> sessions = sessionService.getUserActiveSessions(userId);
        
        // 隐藏敏感信息
        sessions.forEach(session -> session.setToken(session.getToken().substring(0, 20) + "..."));
        
        return Result.success(sessions);
    }
    
    @PostMapping("/force-offline/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<String> forceUserOffline(@PathVariable Long userId) {
        sessionService.forceUserOffline(userId);
        return Result.success("用户已强制下线");
    }
}
```

## 前端Token自动刷新机制

### Token刷新服务
```typescript
// frontend/src/utils/tokenRefresh.ts
export class TokenRefreshService {
  private refreshTimer: NodeJS.Timeout | null = null;
  private isRefreshing = false;
  private refreshPromise: Promise<string> | null = null;

  // 启动自动刷新
  startAutoRefresh() {
    this.scheduleRefresh();
  }

  // 停止自动刷新
  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  // 计划下次刷新
  private scheduleRefresh() {
    const token = getToken();
    if (!token) return;

    const payload = parseJWT(token);
    if (!payload || !payload.exp) return;

    const expireTime = payload.exp * 1000;
    const currentTime = Date.now();
    const timeUntilExpire = expireTime - currentTime;
    
    // 在过期前5分钟刷新
    const refreshTime = Math.max(timeUntilExpire - 5 * 60 * 1000, 60 * 1000);

    this.refreshTimer = setTimeout(() => {
      this.refreshToken();
    }, refreshTime);
  }

  // 刷新Token
  private async refreshToken(): Promise<string> {
    if (this.isRefreshing) {
      return this.refreshPromise!;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.doRefreshToken();

    try {
      const newToken = await this.refreshPromise;
      this.scheduleRefresh(); // 计划下次刷新
      return newToken;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  private async doRefreshToken(): Promise<string> {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      if (data.code === 200) {
        const newToken = data.data.token;
        setToken(newToken);
        return newToken;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Token刷新失败:', error);
      // 刷新失败，清除认证状态
      removeToken();
      window.location.href = '/login';
      throw error;
    }
  }
}

export const tokenRefreshService = new TokenRefreshService();
```

## 迁移策略

### 阶段1：修复当前问题 ✅
- 修复token_expire字段缺失
- 优化前端token管理逻辑
- 确保认证持久化稳定

### 阶段2：Redis基础集成
- 添加Redis依赖和配置
- 实现基础会话管理
- 保持与现有JWT方案兼容

### 阶段3：增强功能
- 实现token黑名单机制
- 添加会话管理API
- 实现token自动刷新

### 阶段4：完整迁移
- 替换JWT认证过滤器
- 实现分布式会话管理
- 添加管理员会话控制功能

## 总结

当前推荐的解决方案：
1. **立即修复**：token_expire字段缺失问题
2. **渐进集成**：根据需要逐步引入Redis功能
3. **保持兼容**：确保现有功能不受影响
4. **性能优化**：通过Redis缓存提升系统性能

这个方案既解决了当前的问题，又为未来的扩展提供了基础。
