package com.kyx.bookmanagementbackend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.entity.BorrowRecord;
import com.kyx.bookmanagementbackend.vo.BorrowRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 借阅记录Mapper接口
 */
@Mapper
public interface BorrowRecordMapper extends BaseMapper<BorrowRecord> {
    
    /**
     * 分页查询借阅记录列表（包含用户和图书信息）
     */
    Page<BorrowRecordVO> selectBorrowRecordPage(Page<BorrowRecordVO> page,
                                               @Param("userId") Long userId,
                                               @Param("bookId") Long bookId,
                                               @Param("status") String status,
                                               @Param("keyword") String keyword);
    
    /**
     * 根据ID查询借阅记录详情（包含用户和图书信息）
     */
    BorrowRecordVO selectBorrowRecordById(@Param("id") Long id);
    
    /**
     * 查询用户当前借阅的图书数量
     */
    int countUserCurrentBorrows(@Param("userId") Long userId);
    
    /**
     * 查询用户是否有逾期图书
     */
    int countUserOverdueBooks(@Param("userId") Long userId);
    
    /**
     * 查询逾期的借阅记录
     */
    List<BorrowRecord> selectOverdueRecords();
}
