package com.kyx.bookmanagementbackend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.entity.Book;
import com.kyx.bookmanagementbackend.vo.BookVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 图书Mapper接口
 */
@Mapper
public interface BookMapper extends BaseMapper<Book> {
    
    /**
     * 分页查询图书列表（包含分类名称）
     */
    Page<BookVO> selectBookPage(Page<BookVO> page, 
                               @Param("keyword") String keyword,
                               @Param("categoryId") Long categoryId,
                               @Param("status") Integer status);
    
    /**
     * 根据ID查询图书详情（包含分类名称）
     */
    BookVO selectBookById(@Param("id") Long id);
}
