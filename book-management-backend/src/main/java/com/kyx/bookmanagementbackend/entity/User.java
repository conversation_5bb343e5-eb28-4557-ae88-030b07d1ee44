package com.kyx.bookmanagementbackend.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.kyx.bookmanagementbackend.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class User extends BaseEntity {
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码（加密）
     */
    private String password;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 角色：SUPER_ADMIN-超级管理员，ADMIN-管理员，USER-普通用户
     */
    private String role;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 头像URL
     */
    private String avatar;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
}
