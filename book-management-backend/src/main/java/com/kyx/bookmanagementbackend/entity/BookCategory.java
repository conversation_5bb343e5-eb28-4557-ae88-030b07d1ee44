package com.kyx.bookmanagementbackend.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.kyx.bookmanagementbackend.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 图书分类实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("book_category")
public class BookCategory extends BaseEntity {
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 父分类ID，0表示顶级分类
     */
    private Long parentId;
    
    /**
     * 分类描述
     */
    private String description;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
}
