package com.kyx.bookmanagementbackend.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.kyx.bookmanagementbackend.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 图书实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("book")
public class Book extends BaseEntity {
    
    /**
     * ISBN号
     */
    private String isbn;
    
    /**
     * 书名
     */
    private String title;
    
    /**
     * 作者
     */
    private String author;
    
    /**
     * 出版社
     */
    private String publisher;
    
    /**
     * 出版日期
     */
    private LocalDate publishDate;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 图书描述
     */
    private String description;
    
    /**
     * 封面图片URL
     */
    private String coverUrl;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 总数量
     */
    private Integer totalQuantity;
    
    /**
     * 可借数量
     */
    private Integer availableQuantity;
    
    /**
     * 状态：0-下架，1-上架
     */
    private Integer status;
}
