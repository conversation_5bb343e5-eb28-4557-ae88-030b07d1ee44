package com.kyx.bookmanagementbackend.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.kyx.bookmanagementbackend.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 借阅记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("borrow_record")
public class BorrowRecord extends BaseEntity {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 图书ID
     */
    private Long bookId;
    
    /**
     * 借阅时间
     */
    private LocalDateTime borrowTime;
    
    /**
     * 应还时间
     */
    private LocalDateTime dueTime;
    
    /**
     * 实际还时间
     */
    private LocalDateTime returnTime;
    
    /**
     * 状态：BORROWED-已借出，RETURNED-已归还，OVERDUE-逾期
     */
    private String status;
    
    /**
     * 逾期天数
     */
    private Integer overdueDays;
    
    /**
     * 罚金金额
     */
    private BigDecimal fineAmount;
    
    /**
     * 备注
     */
    private String remarks;
}
