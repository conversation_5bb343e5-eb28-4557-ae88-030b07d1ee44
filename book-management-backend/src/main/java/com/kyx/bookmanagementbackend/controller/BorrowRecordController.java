package com.kyx.bookmanagementbackend.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.common.Result;
import com.kyx.bookmanagementbackend.dto.BorrowDTO;
import com.kyx.bookmanagementbackend.service.BorrowRecordService;
import com.kyx.bookmanagementbackend.vo.BorrowRecordVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 借阅记录管理控制器
 */
@Tag(name = "借阅管理", description = "借阅管理相关接口")
@RestController
@RequestMapping("/api/borrow")
@RequiredArgsConstructor
@Slf4j
public class BorrowRecordController {
    
    private final BorrowRecordService borrowRecordService;
    
    @Operation(summary = "借书")
    @PostMapping("/borrow")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<String> borrowBook(@Validated @RequestBody BorrowDTO borrowDTO, Authentication authentication) {
        Long userId = (Long) authentication.getDetails();
        borrowRecordService.borrowBook(userId, borrowDTO);
        return Result.success("借书成功");
    }
    
    @Operation(summary = "还书")
    @PostMapping("/return/{recordId}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<String> returnBook(@PathVariable Long recordId,
                                  @RequestParam(required = false) String remarks) {
        borrowRecordService.returnBook(recordId, remarks);
        return Result.success("还书成功");
    }
    
    @Operation(summary = "续借")
    @PostMapping("/renew/{recordId}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<String> renewBook(@PathVariable Long recordId) {
        borrowRecordService.renewBook(recordId);
        return Result.success("续借成功");
    }

    @Operation(summary = "检查续借条件")
    @GetMapping("/check-renew/{recordId}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Map<String, Object>> checkRenewEligibility(@PathVariable Long recordId) {
        Map<String, Object> result = borrowRecordService.checkRenewEligibility(recordId);
        return Result.success(result);
    }
    
    @Operation(summary = "分页查询借阅记录")
    @GetMapping("/records")
    public Result<Page<BorrowRecordVO>> getBorrowRecordList(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") int current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "图书ID") @RequestParam(required = false) Long bookId,
            @Parameter(description = "状态筛选") @RequestParam(required = false) String status,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            Authentication authentication) {

        log.info("借阅记录查询请求 - 页码: {}, 大小: {}, 用户ID: {}, 图书ID: {}, 状态: {}, 关键词: {}",
                current, size, userId, bookId, status, keyword);

        // 普通用户只能查看自己的借阅记录
        String role = authentication.getAuthorities().iterator().next().getAuthority();
        Long originalUserId = userId;
        if ("ROLE_USER".equals(role)) {
            userId = (Long) authentication.getDetails();
            log.info("普通用户查询，限制为自己的记录 - 用户ID: {}", userId);
        }

        log.info("最终查询参数 - 页码: {}, 大小: {}, 用户ID: {}, 图书ID: {}, 状态: {}, 关键词: {}, 角色: {}",
                current, size, userId, bookId, status, keyword, role);

        Page<BorrowRecordVO> recordPage = borrowRecordService.getBorrowRecordList(
            current, size, userId, bookId, status, keyword);

        log.info("借阅记录查询结果 - 总记录数: {}, 当前页记录数: {}",
                recordPage.getTotal(), recordPage.getRecords().size());

        if (recordPage.getRecords().size() > 0) {
            log.info("第一条记录详情: {}", recordPage.getRecords().get(0));
        }

        return Result.success(recordPage);
    }
    
    @Operation(summary = "根据ID查询借阅记录详情")
    @GetMapping("/records/{id}")
    public Result<BorrowRecordVO> getBorrowRecordById(@PathVariable Long id) {
        BorrowRecordVO borrowRecordVO = borrowRecordService.getBorrowRecordById(id);
        return Result.success(borrowRecordVO);
    }
    
    @Operation(summary = "查询用户当前借阅数量")
    @GetMapping("/current-count")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Integer> getCurrentBorrowCount(Authentication authentication) {
        Long userId = (Long) authentication.getDetails();
        int count = borrowRecordService.getUserCurrentBorrowCount(userId);
        return Result.success(count);
    }
    
    @Operation(summary = "检查用户是否有逾期图书")
    @GetMapping("/has-overdue")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Boolean> hasOverdueBooks(Authentication authentication) {
        Long userId = (Long) authentication.getDetails();
        boolean hasOverdue = borrowRecordService.hasOverdueBooks(userId);
        return Result.success(hasOverdue);
    }
    
    @Operation(summary = "处理逾期图书（管理员功能）")
    @PostMapping("/process-overdue")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<String> processOverdueBooks() {
        borrowRecordService.processOverdueBooks();
        return Result.success("逾期处理完成");
    }

    @Operation(summary = "检查用户借阅历史")
    @GetMapping("/check-history/{bookId}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Map<String, Object>> checkUserBookHistory(@PathVariable Long bookId, Authentication authentication) {
        Long userId = (Long) authentication.getDetails();

        // 检查用户是否当前正在借阅该图书
        boolean hasCurrentlyBorrowed = borrowRecordService.hasCurrentlyBorrowedBook(userId, bookId);

        // 检查用户是否历史上借过该图书
        boolean hasHistoryBorrowed = borrowRecordService.hasHistoryBorrowedBook(userId, bookId);

        // 获取最后借阅日期
        String lastBorrowDate = borrowRecordService.getLastBorrowDate(userId, bookId);

        // 只有当前正在借阅时才不能再借
        boolean canBorrowAgain = !hasCurrentlyBorrowed;
        String reason = null;

        if (hasCurrentlyBorrowed) {
            reason = "您当前正在借阅这本图书，无法重复借阅";
        }

        Map<String, Object> result = new HashMap<>();
        result.put("hasCurrentlyBorrowed", hasCurrentlyBorrowed);
        result.put("hasHistoryBorrowed", hasHistoryBorrowed);
        result.put("lastBorrowDate", lastBorrowDate);
        result.put("canBorrowAgain", canBorrowAgain);
        result.put("reason", reason);

        return Result.success(result);
    }
    
    @Operation(summary = "计算逾期罚金")
    @PostMapping("/calculate-fine/{recordId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<String> calculateOverdueFine(@PathVariable Long recordId) {
        borrowRecordService.calculateOverdueFine(recordId);
        return Result.success("罚金计算完成");
    }
}
