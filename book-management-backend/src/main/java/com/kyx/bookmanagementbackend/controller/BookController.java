package com.kyx.bookmanagementbackend.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.common.Result;
import com.kyx.bookmanagementbackend.dto.BookDTO;
import com.kyx.bookmanagementbackend.service.BookService;
import com.kyx.bookmanagementbackend.vo.BookVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 图书管理控制器
 */
@Tag(name = "图书管理", description = "图书管理相关接口")
@RestController
@RequestMapping("/api/books")
@RequiredArgsConstructor
public class BookController {
    
    private final BookService bookService;
    
    @Operation(summary = "分页查询图书列表")
    @GetMapping
    public Result<Page<BookVO>> getBookList(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") int current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "状态筛选") @RequestParam(required = false) Integer status) {
        
        Page<BookVO> bookPage = bookService.getBookList(current, size, keyword, categoryId, status);
        return Result.success(bookPage);
    }
    
    @Operation(summary = "根据ID查询图书详情")
    @GetMapping("/{id}")
    public Result<BookVO> getBookById(@PathVariable Long id) {
        BookVO bookVO = bookService.getBookById(id);
        return Result.success(bookVO);
    }
    
    @Operation(summary = "创建图书")
    @PostMapping
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<String> createBook(@Validated @RequestBody BookDTO bookDTO) {
        bookService.createBook(bookDTO);
        return Result.success("图书创建成功");
    }
    
    @Operation(summary = "更新图书信息")
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<String> updateBook(@PathVariable Long id, @Validated @RequestBody BookDTO bookDTO) {
        bookService.updateBook(id, bookDTO);
        return Result.success("图书信息更新成功");
    }
    
    @Operation(summary = "删除图书")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<String> deleteBook(@PathVariable Long id) {
        bookService.deleteBook(id);
        return Result.success("图书删除成功");
    }
    
    @Operation(summary = "上架/下架图书")
    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<String> toggleBookStatus(@PathVariable Long id) {
        bookService.toggleBookStatus(id);
        return Result.success("图书状态更新成功");
    }
    
    @Operation(summary = "更新图书库存")
    @PutMapping("/{id}/quantity")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<String> updateBookQuantity(@PathVariable Long id,
                                          @RequestParam Integer totalQuantity,
                                          @RequestParam Integer availableQuantity) {
        bookService.updateBookQuantity(id, totalQuantity, availableQuantity);
        return Result.success("图书库存更新成功");
    }
}
