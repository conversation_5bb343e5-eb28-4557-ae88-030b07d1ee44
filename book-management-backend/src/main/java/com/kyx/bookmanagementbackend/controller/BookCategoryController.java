package com.kyx.bookmanagementbackend.controller;

import com.kyx.bookmanagementbackend.common.Result;
import com.kyx.bookmanagementbackend.dto.BookCategoryDTO;
import com.kyx.bookmanagementbackend.service.BookCategoryService;
import com.kyx.bookmanagementbackend.vo.BookCategoryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 图书分类管理控制器
 */
@Tag(name = "图书分类管理", description = "图书分类管理相关接口")
@RestController
@RequestMapping("/api/categories")
@RequiredArgsConstructor
public class BookCategoryController {
    
    private final BookCategoryService categoryService;
    
    @Operation(summary = "获取分类树形结构")
    @GetMapping("/tree")
    public Result<List<BookCategoryVO>> getCategoryTree() {
        List<BookCategoryVO> categoryTree = categoryService.getCategoryTree();
        return Result.success(categoryTree);
    }
    
    @Operation(summary = "获取所有分类列表")
    @GetMapping
    public Result<List<BookCategoryVO>> getAllCategories() {
        List<BookCategoryVO> categories = categoryService.getAllCategories();
        return Result.success(categories);
    }
    
    @Operation(summary = "根据ID查询分类详情")
    @GetMapping("/{id}")
    public Result<BookCategoryVO> getCategoryById(@PathVariable Long id) {
        BookCategoryVO categoryVO = categoryService.getCategoryById(id);
        return Result.success(categoryVO);
    }
    
    @Operation(summary = "创建分类")
    @PostMapping
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<String> createCategory(@Validated @RequestBody BookCategoryDTO categoryDTO) {
        categoryService.createCategory(categoryDTO);
        return Result.success("分类创建成功");
    }
    
    @Operation(summary = "更新分类信息")
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<String> updateCategory(@PathVariable Long id, @Validated @RequestBody BookCategoryDTO categoryDTO) {
        categoryService.updateCategory(id, categoryDTO);
        return Result.success("分类信息更新成功");
    }
    
    @Operation(summary = "删除分类")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<String> deleteCategory(@PathVariable Long id) {
        categoryService.deleteCategory(id);
        return Result.success("分类删除成功");
    }
    
    @Operation(summary = "启用/禁用分类")
    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<String> toggleCategoryStatus(@PathVariable Long id) {
        categoryService.toggleCategoryStatus(id);
        return Result.success("分类状态更新成功");
    }
}
