package com.kyx.bookmanagementbackend.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件访问控制器
 */
@Tag(name = "文件管理", description = "文件上传下载相关接口")
@Slf4j
@RestController
@RequestMapping("/api/files")
public class FileController {
    
    private static final String AVATAR_UPLOAD_PATH = "uploads/avatars/";
    
    @Operation(summary = "获取头像文件", description = "根据文件名获取用户头像文件")
    @GetMapping("/avatars/{filename}")
    public ResponseEntity<Resource> getAvatar(
            @Parameter(description = "头像文件名") @PathVariable String filename) {
        try {
            // 安全检查：防止路径遍历攻击
            if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
                log.warn("检测到不安全的文件名: {}", filename);
                return ResponseEntity.badRequest().build();
            }

            Path filePath = Paths.get(AVATAR_UPLOAD_PATH).resolve(filename).normalize();
            Resource resource = new UrlResource(filePath.toUri());

            if (!resource.exists() || !resource.isReadable()) {
                log.warn("头像文件不存在或不可读: {}", filename);
                return ResponseEntity.notFound().build();
            }

            // 确定文件类型
            String contentType = null;
            try {
                contentType = Files.probeContentType(filePath);
            } catch (IOException e) {
                log.warn("无法确定文件类型: {}", filename);
            }

            // 如果无法确定类型，根据文件扩展名设置
            if (contentType == null) {
                String extension = filename.toLowerCase();
                if (extension.endsWith(".jpg") || extension.endsWith(".jpeg")) {
                    contentType = "image/jpeg";
                } else if (extension.endsWith(".png")) {
                    contentType = "image/png";
                } else if (extension.endsWith(".gif")) {
                    contentType = "image/gif";
                } else {
                    contentType = "application/octet-stream";
                }
            }

            log.debug("返回头像文件: {}, 类型: {}", filename, contentType);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filename + "\"")
                    .header(HttpHeaders.CACHE_CONTROL, "public, max-age=3600") // 缓存1小时
                    .body(resource);

        } catch (MalformedURLException e) {
            log.error("获取头像文件失败: {}", filename, e);
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("获取头像文件时发生未知错误: {}", filename, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
