package com.kyx.bookmanagementbackend.controller;

import com.kyx.bookmanagementbackend.common.Result;
import com.kyx.bookmanagementbackend.dto.ChangePasswordDTO;
import com.kyx.bookmanagementbackend.dto.UserProfileUpdateDTO;
import com.kyx.bookmanagementbackend.service.ProfileService;
import com.kyx.bookmanagementbackend.vo.UserProfileVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 个人信息控制器
 */
@Tag(name = "个人信息管理", description = "用户个人信息相关接口")
@Slf4j
@RestController
@RequestMapping("/api/profile")
@RequiredArgsConstructor
public class ProfileController {
    
    private final ProfileService profileService;
    
    @Operation(summary = "获取当前用户个人信息", description = "获取当前登录用户的详细个人信息")
    @GetMapping
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<UserProfileVO> getCurrentUserProfile(Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}获取个人信息", userId);
            
            UserProfileVO profile = profileService.getCurrentUserProfile(userId);
            return Result.success(profile);
        } catch (Exception e) {
            log.error("获取个人信息失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    @Operation(summary = "更新个人信息", description = "更新当前用户的个人信息")
    @PutMapping
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<UserProfileVO> updateProfile(
            @Validated @RequestBody UserProfileUpdateDTO updateDTO,
            Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}更新个人信息", userId);
            
            UserProfileVO profile = profileService.updateProfile(userId, updateDTO);
            return Result.success("个人信息更新成功", profile);
        } catch (Exception e) {
            log.error("更新个人信息失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    @Operation(summary = "修改密码", description = "修改当前用户的登录密码")
    @PutMapping("/password")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<String> changePassword(
            @Validated @RequestBody ChangePasswordDTO changePasswordDTO,
            Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}修改密码", userId);
            
            profileService.changePassword(userId, changePasswordDTO);
            return Result.success("密码修改成功");
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    @Operation(summary = "上传头像", description = "上传并更新用户头像")
    @PostMapping("/avatar")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Map<String, String>> uploadAvatar(
            @Parameter(description = "头像文件") @RequestParam("file") MultipartFile file,
            Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}上传头像", userId);
            
            String avatarUrl = profileService.uploadAvatar(userId, file);
            return Result.success("头像上传成功", Map.of("url", avatarUrl));
        } catch (Exception e) {
            log.error("上传头像失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    @Operation(summary = "获取用户统计信息", description = "获取当前用户的借阅、收藏等统计信息")
    @GetMapping("/stats")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<UserProfileVO.BorrowStats> getUserStats(Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.debug("用户{}获取统计信息", userId);
            
            UserProfileVO.BorrowStats stats = profileService.getUserStats(userId);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取用户统计信息失败", e);
            return Result.error(e.getMessage());
        }
    }
}
