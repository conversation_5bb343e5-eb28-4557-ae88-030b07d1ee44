package com.kyx.bookmanagementbackend.controller;

import com.kyx.bookmanagementbackend.common.Result;
import com.kyx.bookmanagementbackend.dto.UserLoginDTO;
import com.kyx.bookmanagementbackend.dto.UserRegisterDTO;
import com.kyx.bookmanagementbackend.service.UserService;
import com.kyx.bookmanagementbackend.vo.LoginVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 认证控制器
 */
@Tag(name = "认证管理", description = "用户登录、注册相关接口")
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final UserService userService;
    
    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<LoginVO> login(@Validated @RequestBody UserLoginDTO loginDTO) {
        long startTime = System.currentTimeMillis();
        log.info("用户登录请求 - 用户名: {}, IP: {}", loginDTO.getUsername(), getClientIp());

        try {
            LoginVO loginVO = userService.login(loginDTO);
            long duration = System.currentTimeMillis() - startTime;
            log.info("用户登录成功 - 用户名: {}, 用户ID: {}, 耗时: {}ms",
                    loginDTO.getUsername(), loginVO.getUser().getId(), duration);
            return Result.success("登录成功", loginVO);
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.warn("用户登录失败 - 用户名: {}, 错误: {}, 耗时: {}ms",
                    loginDTO.getUsername(), e.getMessage(), duration);
            throw e;
        }
    }
    
    @Operation(summary = "用户注册")
    @PostMapping("/register")
    public Result<String> register(@Validated @RequestBody UserRegisterDTO registerDTO) {
        long startTime = System.currentTimeMillis();
        log.info("用户注册请求 - 用户名: {}, 邮箱: {}, IP: {}",
                registerDTO.getUsername(), registerDTO.getEmail(), getClientIp());

        try {
            userService.register(registerDTO);
            long duration = System.currentTimeMillis() - startTime;
            log.info("用户注册成功 - 用户名: {}, 邮箱: {}, 耗时: {}ms",
                    registerDTO.getUsername(), registerDTO.getEmail(), duration);
            return Result.success("注册成功");
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.warn("用户注册失败 - 用户名: {}, 邮箱: {}, 错误: {}, 耗时: {}ms",
                    registerDTO.getUsername(), registerDTO.getEmail(), e.getMessage(), duration);
            throw e;
        }
    }
    
    @Operation(summary = "用户登出")
    @PostMapping("/logout")
    public Result<String> logout() {
        log.info("用户登出请求 - IP: {}", getClientIp());
        // JWT是无状态的，客户端删除token即可
        return Result.success("登出成功");
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String xForwardedFor = request.getHeader("X-Forwarded-For");
                if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
                    return xForwardedFor.split(",")[0].trim();
                }
                String xRealIp = request.getHeader("X-Real-IP");
                if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
                    return xRealIp;
                }
                return request.getRemoteAddr();
            }
        } catch (Exception e) {
            log.debug("获取客户端IP失败", e);
        }
        return "unknown";
    }
}
