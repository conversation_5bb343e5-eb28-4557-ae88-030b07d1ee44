package com.kyx.bookmanagementbackend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kyx.bookmanagementbackend.common.Result;
import com.kyx.bookmanagementbackend.dto.FavoriteDTO;
import com.kyx.bookmanagementbackend.service.UserFavoriteService;
import com.kyx.bookmanagementbackend.vo.UserFavoriteVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 用户收藏控制器
 */
@Tag(name = "用户收藏管理", description = "用户收藏相关接口")
@Slf4j
@RestController
@RequestMapping("/api/user/favorites")
public class UserFavoriteController {

    @Autowired
    private UserFavoriteService userFavoriteService;

    @Operation(summary = "添加收藏", description = "用户添加图书到收藏列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "收藏成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权访问"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/add")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<String> addFavorite(
            @Parameter(description = "收藏操作数据", required = true) @Valid @RequestBody FavoriteDTO favoriteDTO,
            Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}请求添加收藏图书{}", userId, favoriteDTO.getBookId());

            userFavoriteService.addFavorite(userId, favoriteDTO.getBookId());
            return Result.success("收藏成功");
        } catch (Exception e) {
            log.error("添加收藏失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "取消收藏", description = "用户从收藏列表中移除图书")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "取消收藏成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权访问"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/remove")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<String> removeFavorite(
            @Parameter(description = "收藏操作数据", required = true) @Valid @RequestBody FavoriteDTO favoriteDTO,
            Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}请求取消收藏图书{}", userId, favoriteDTO.getBookId());

            userFavoriteService.removeFavorite(userId, favoriteDTO.getBookId());
            return Result.success("取消收藏成功");
        } catch (Exception e) {
            log.error("取消收藏失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "切换收藏状态", description = "智能切换图书收藏状态，已收藏则取消，未收藏则添加")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "切换成功，返回当前收藏状态"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权访问"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/toggle")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Map<String, Object>> toggleFavorite(
            @Parameter(description = "收藏操作数据", required = true) @Valid @RequestBody FavoriteDTO favoriteDTO,
            Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}请求切换收藏状态，图书{}", userId, favoriteDTO.getBookId());

            boolean isFavorited = userFavoriteService.toggleFavorite(userId, favoriteDTO.getBookId());

            Map<String, Object> result = Map.of(
                "isFavorited", isFavorited,
                "message", isFavorited ? "收藏成功" : "取消收藏成功"
            );

            return Result.success(result);
        } catch (Exception e) {
            log.error("切换收藏状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "检查收藏状态", description = "检查用户是否已收藏指定图书")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功，返回收藏状态"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权访问"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/check/{bookId}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Boolean> checkFavorite(
            @Parameter(description = "图书ID", required = true) @PathVariable Long bookId,
            Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.debug("用户{}检查图书{}收藏状态", userId, bookId);

            boolean isFavorited = userFavoriteService.isFavorited(userId, bookId);
            return Result.success(isFavorited);
        } catch (Exception e) {
            log.error("检查收藏状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "分页查询用户收藏列表", description = "获取当前用户的收藏图书列表，支持分页")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功，返回收藏列表"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权访问"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/list")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<IPage<UserFavoriteVO>> getUserFavorites(
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Long size,
            Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}查询收藏列表，页码{}，大小{}", userId, current, size);

            IPage<UserFavoriteVO> favorites = userFavoriteService.getUserFavorites(userId, current, size);
            return Result.success(favorites);
        } catch (Exception e) {
            log.error("查询收藏列表失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "获取用户收藏的图书ID列表", description = "获取当前用户收藏的所有图书ID，用于前端快速判断收藏状态")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功，返回图书ID列表"),
            @ApiResponse(responseCode = "401", description = "未授权访问"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/book-ids")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<List<Long>> getUserFavoriteBookIds(Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.debug("用户{}查询收藏图书ID列表", userId);

            List<Long> bookIds = userFavoriteService.getUserFavoriteBookIds(userId);
            return Result.success(bookIds);
        } catch (Exception e) {
            log.error("查询收藏图书ID列表失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "批量查询图书收藏状态", description = "批量查询多个图书的收藏状态，返回图书ID与收藏状态的映射")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功，返回收藏状态映射"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权访问"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/batch-check")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Map<Long, Boolean>> batchCheckFavoriteStatus(
            @Parameter(description = "图书ID列表", required = true) @RequestBody List<Long> bookIds,
            Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.debug("用户{}批量查询图书收藏状态", userId);

            Map<Long, Boolean> favoriteStatus = userFavoriteService.batchCheckFavoriteStatus(userId, bookIds);
            return Result.success(favoriteStatus);
        } catch (Exception e) {
            log.error("批量查询收藏状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "获取用户收藏统计信息", description = "获取当前用户的收藏统计数据，包括总数、分类统计等")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功，返回统计信息"),
            @ApiResponse(responseCode = "401", description = "未授权访问"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/stats")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Map<String, Object>> getUserFavoriteStats(Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}查询收藏统计信息", userId);

            Map<String, Object> stats = userFavoriteService.getUserFavoriteStats(userId);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询收藏统计信息失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "获取图书收藏数量", description = "获取指定图书被收藏的总次数，无需登录即可访问")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功，返回收藏数量"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/book/{bookId}/count")
    public Result<Integer> getBookFavoriteCount(
            @Parameter(description = "图书ID", required = true) @PathVariable Long bookId) {
        try {
            log.debug("查询图书{}的收藏数量", bookId);

            int count = userFavoriteService.getBookFavoriteCount(bookId);
            return Result.success(count);
        } catch (Exception e) {
            log.error("查询图书收藏数量失败", e);
            return Result.error(e.getMessage());
        }
    }
}
