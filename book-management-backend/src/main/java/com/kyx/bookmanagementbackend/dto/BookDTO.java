package com.kyx.bookmanagementbackend.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.DecimalMin;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 图书DTO
 */
@Data
public class BookDTO {
    
    private String isbn;
    
    @NotBlank(message = "书名不能为空")
    private String title;
    
    @NotBlank(message = "作者不能为空")
    private String author;
    
    private String publisher;
    
    private LocalDate publishDate;
    
    @NotNull(message = "分类不能为空")
    private Long categoryId;
    
    private String description;
    
    private String coverUrl;
    
    @DecimalMin(value = "0.0", message = "价格不能为负数")
    private BigDecimal price;
    
    @NotNull(message = "总数量不能为空")
    @Min(value = 0, message = "总数量不能为负数")
    private Integer totalQuantity;
    
    @NotNull(message = "可借数量不能为空")
    @Min(value = 0, message = "可借数量不能为负数")
    private Integer availableQuantity;
    
    private Integer status = 1;
}
