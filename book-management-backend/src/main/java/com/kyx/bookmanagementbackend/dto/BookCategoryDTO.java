package com.kyx.bookmanagementbackend.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 图书分类DTO
 */
@Data
public class BookCategoryDTO {
    
    @NotBlank(message = "分类名称不能为空")
    private String name;
    
    @NotNull(message = "父分类ID不能为空")
    private Long parentId = 0L;
    
    private String description;
    
    private Integer sortOrder = 0;
    
    private Integer status = 1;
}
