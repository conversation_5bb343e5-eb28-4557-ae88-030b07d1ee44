package com.kyx.bookmanagementbackend.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;
import java.nio.file.Paths;

/**
 * Web MVC 配置类
 * 用于配置静态资源映射等
 */
@Slf4j
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * 配置静态资源映射
     * 将uploads目录映射为可访问的静态资源
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 获取uploads目录的绝对路径
        String uploadsPath = Paths.get("uploads").toAbsolutePath().toString();
        
        // 确保uploads目录存在
        File uploadsDir = new File(uploadsPath);
        if (!uploadsDir.exists()) {
            boolean created = uploadsDir.mkdirs();
            if (created) {
                log.info("创建uploads目录: {}", uploadsPath);
            } else {
                log.warn("无法创建uploads目录: {}", uploadsPath);
            }
        }
        
        // 映射/uploads/**到uploads目录
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:" + uploadsPath + File.separator)
                .setCachePeriod(3600); // 缓存1小时
        
        log.info("静态资源映射配置完成:");
        log.info("  URL路径: /uploads/**");
        log.info("  文件路径: {}", uploadsPath);
        
        // 调用父类方法，保持默认配置
        WebMvcConfigurer.super.addResourceHandlers(registry);
    }
}
