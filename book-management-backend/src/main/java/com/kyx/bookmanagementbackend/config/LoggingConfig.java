package com.kyx.bookmanagementbackend.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 日志配置验证类
 * 在应用启动时验证日志配置和目录
 */
@Slf4j
@Component
public class LoggingConfig implements CommandLineRunner {

    @Override
    public void run(String... args) throws Exception {
        // 验证日志目录
        validateLogDirectory();
        
        // 测试日志输出
        testLogOutput();
    }

    private void validateLogDirectory() {
        String logDir = "D:\\Code_study_project\\AugmentProjes\\UserCenterManagement\\BookMM\\book-management-backend\\logs";
        Path logPath = Paths.get(logDir);
        
        try {
            // 创建日志目录（如果不存在）
            if (!Files.exists(logPath)) {
                Files.createDirectories(logPath);
                log.info("创建日志目录: {}", logPath.toAbsolutePath());
            }
            
            // 检查目录权限
            File logDirFile = logPath.toFile();
            if (!logDirFile.canWrite()) {
                log.error("日志目录没有写入权限: {}", logPath.toAbsolutePath());
            } else {
                log.info("日志目录验证成功: {}", logPath.toAbsolutePath());
            }
            
        } catch (Exception e) {
            log.error("日志目录验证失败: {}", e.getMessage(), e);
        }
    }

    private void testLogOutput() {
        log.debug("这是一条DEBUG级别的测试日志");
        log.info("这是一条INFO级别的测试日志");
        log.warn("这是一条WARN级别的测试日志");
        log.error("这是一条ERROR级别的测试日志");
        
        log.info("日志配置验证完成 - 如果您看到这条消息，说明日志系统正常工作");
    }
}
