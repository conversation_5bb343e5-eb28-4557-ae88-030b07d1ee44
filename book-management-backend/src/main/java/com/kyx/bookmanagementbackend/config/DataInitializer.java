package com.kyx.bookmanagementbackend.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kyx.bookmanagementbackend.entity.User;
import com.kyx.bookmanagementbackend.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * 数据初始化器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {
    
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    
    @Override
    public void run(String... args) throws Exception {
        initSuperAdmin();
    }
    
    /**
     * 初始化超级管理员
     */
    private void initSuperAdmin() {
        // 检查是否已存在超级管理员
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUsername, "kyx666");
        User existingUser = userMapper.selectOne(wrapper);
        
        if (existingUser == null) {
            User superAdmin = new User();
            superAdmin.setUsername("kyx666");
            superAdmin.setPassword(passwordEncoder.encode("kyx200328"));
            superAdmin.setEmail("<EMAIL>");
            superAdmin.setRealName("超级管理员");
            superAdmin.setRole("SUPER_ADMIN");
            superAdmin.setStatus(1);
            
            userMapper.insert(superAdmin);
            log.info("超级管理员账户初始化完成: username=kyx666, password=kyx200328");
        } else {
            log.info("超级管理员账户已存在，跳过初始化");
        }
    }
}
