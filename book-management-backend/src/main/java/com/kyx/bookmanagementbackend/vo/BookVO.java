package com.kyx.bookmanagementbackend.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 图书VO
 */
@Data
public class BookVO {
    
    private Long id;
    private String isbn;
    private String title;
    private String author;
    private String publisher;
    private LocalDate publishDate;
    private Long categoryId;
    private String categoryName;
    private String description;
    private String coverUrl;
    private BigDecimal price;
    private Integer totalQuantity;
    private Integer availableQuantity;
    private Integer status;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
}
