package com.kyx.bookmanagementbackend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户个人信息VO
 */
@Data
@Schema(description = "用户个人信息响应")
public class UserProfileVO {
    
    @Schema(description = "用户ID", example = "1")
    private Long id;
    
    @Schema(description = "用户名", example = "admin")
    private String username;
    
    @Schema(description = "真实姓名", example = "张三")
    private String realName;
    
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;
    
    @Schema(description = "手机号码", example = "13800138000")
    private String phone;
    
    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;
    
    @Schema(description = "用户角色", example = "USER")
    private String role;
    
    @Schema(description = "账户状态", example = "1")
    private Integer status;
    
    @Schema(description = "注册时间", example = "2023-01-01T00:00:00")
    private LocalDateTime createdTime;
    
    @Schema(description = "最后登录时间", example = "2023-01-01T00:00:00")
    private LocalDateTime lastLoginTime;
    
    @Schema(description = "借阅统计信息")
    private BorrowStats borrowStats;
    
    @Data
    @Schema(description = "借阅统计信息")
    public static class BorrowStats {
        @Schema(description = "总借阅次数", example = "10")
        private Integer totalBorrows;
        
        @Schema(description = "当前借阅数量", example = "2")
        private Integer activeBorrows;
        
        @Schema(description = "逾期借阅数量", example = "0")
        private Integer overdueBorrows;
        
        @Schema(description = "总收藏数量", example = "5")
        private Integer totalFavorites;
    }
}
