package com.kyx.bookmanagementbackend.vo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 图书分类VO
 */
@Data
public class BookCategoryVO {
    
    private Long id;
    private String name;
    private Long parentId;
    private String parentName;
    private String description;
    private Integer sortOrder;
    private Integer status;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private List<BookCategoryVO> children;
}
