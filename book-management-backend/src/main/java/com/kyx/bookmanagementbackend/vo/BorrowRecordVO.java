package com.kyx.bookmanagementbackend.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 借阅记录VO
 */
@Data
public class BorrowRecordVO {
    
    private Long id;
    private Long userId;
    private String username;
    private String userRealName;
    private Long bookId;
    private String bookTitle;
    private String bookAuthor;
    private String bookIsbn;
    private String bookCoverUrl;
    private LocalDateTime borrowTime;
    private LocalDateTime dueTime;
    private LocalDateTime returnTime;
    private String status;
    private Integer overdueDays;
    private BigDecimal fineAmount;
    private String remarks;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
}
