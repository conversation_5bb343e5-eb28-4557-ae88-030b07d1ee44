package com.kyx.bookmanagementbackend.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 日志工具类
 * 提供统一的日志格式和常用日志方法
 */
@Slf4j
public class LogUtil {
    
    /**
     * 记录用户操作日志
     */
    public static void logUserOperation(String operation, Long userId, String username, Object... params) {
        String clientIp = getClientIp();
        log.info("用户操作 - 操作: {}, 用户ID: {}, 用户名: {}, IP: {}, 参数: {}", 
                operation, userId, username, clientIp, formatParams(params));
    }
    
    /**
     * 记录管理员操作日志
     */
    public static void logAdminOperation(String operation, Long adminId, String adminName, String target, Object... params) {
        String clientIp = getClientIp();
        log.info("管理员操作 - 操作: {}, 管理员ID: {}, 管理员: {}, 目标: {}, IP: {}, 参数: {}", 
                operation, adminId, adminName, target, clientIp, formatParams(params));
    }
    
    /**
     * 记录业务操作日志
     */
    public static void logBusinessOperation(String module, String operation, String result, Object... params) {
        log.info("业务操作 - 模块: {}, 操作: {}, 结果: {}, 参数: {}", 
                module, operation, result, formatParams(params));
    }
    
    /**
     * 记录API请求日志
     */
    public static void logApiRequest(String method, String uri, Object requestBody, long startTime) {
        String clientIp = getClientIp();
        log.info("API请求 - 方法: {}, URI: {}, IP: {}, 请求体: {}, 开始时间: {}", 
                method, uri, clientIp, requestBody, startTime);
    }
    
    /**
     * 记录API响应日志
     */
    public static void logApiResponse(String method, String uri, Object response, long duration) {
        log.info("API响应 - 方法: {}, URI: {}, 响应: {}, 耗时: {}ms", 
                method, uri, response, duration);
    }
    
    /**
     * 记录安全事件日志
     */
    public static void logSecurityEvent(String event, String username, String details) {
        String clientIp = getClientIp();
        log.warn("安全事件 - 事件: {}, 用户: {}, IP: {}, 详情: {}", 
                event, username, clientIp, details);
    }
    
    /**
     * 记录系统错误日志
     */
    public static void logSystemError(String module, String operation, String error, Exception e) {
        log.error("系统错误 - 模块: {}, 操作: {}, 错误: {}", module, operation, error, e);
    }
    
    /**
     * 记录性能日志
     */
    public static void logPerformance(String operation, long duration, Object... params) {
        if (duration > 1000) { // 超过1秒记录警告
            log.warn("性能警告 - 操作: {}, 耗时: {}ms, 参数: {}", operation, duration, formatParams(params));
        } else {
            log.debug("性能监控 - 操作: {}, 耗时: {}ms, 参数: {}", operation, duration, formatParams(params));
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    public static String getClientIp() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String xForwardedFor = request.getHeader("X-Forwarded-For");
                if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
                    return xForwardedFor.split(",")[0].trim();
                }
                String xRealIp = request.getHeader("X-Real-IP");
                if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
                    return xRealIp;
                }
                return request.getRemoteAddr();
            }
        } catch (Exception e) {
            log.debug("获取客户端IP失败", e);
        }
        return "unknown";
    }
    
    /**
     * 格式化参数
     */
    private static String formatParams(Object... params) {
        if (params == null || params.length == 0) {
            return "[]";
        }
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < params.length; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(params[i]);
        }
        sb.append("]");
        return sb.toString();
    }
}
