package com.kyx.bookmanagementbackend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.dto.UserLoginDTO;
import com.kyx.bookmanagementbackend.dto.UserRegisterDTO;
import com.kyx.bookmanagementbackend.entity.User;
import com.kyx.bookmanagementbackend.vo.LoginVO;
import com.kyx.bookmanagementbackend.vo.UserVO;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 用户登录
     */
    LoginVO login(UserLoginDTO loginDTO);
    
    /**
     * 用户注册
     */
    void register(UserRegisterDTO registerDTO);
    
    /**
     * 根据用户名查询用户
     */
    User findByUsername(String username);
    
    /**
     * 根据邮箱查询用户
     */
    User findByEmail(String email);
    
    /**
     * 分页查询用户列表
     */
    Page<UserVO> getUserList(int current, int size, String keyword, String role, Integer status);
    
    /**
     * 根据ID查询用户详情
     */
    UserVO getUserById(Long id);
    
    /**
     * 创建用户（管理员功能）
     */
    void createUser(UserRegisterDTO userDTO, String role);
    
    /**
     * 更新用户信息
     */
    void updateUser(Long id, UserVO userVO);
    
    /**
     * 删除用户
     */
    void deleteUser(Long id);
    
    /**
     * 启用/禁用用户
     */
    void toggleUserStatus(Long id);
    
    /**
     * 重置用户密码
     */
    void resetPassword(Long id, String newPassword);
    
    /**
     * 更新最后登录时间
     */
    void updateLastLoginTime(Long userId);

    /**
     * 获取用户总数
     */
    long getTotalUserCount();

    /**
     * 获取活跃用户数
     */
    long getActiveUserCount();

    /**
     * 获取今日新增用户数
     */
    long getNewUsersToday();

    /**
     * 获取本月新增用户数
     */
    long getNewUsersThisMonth();
}
