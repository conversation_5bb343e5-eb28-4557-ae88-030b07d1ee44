package com.kyx.bookmanagementbackend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.dto.BookDTO;
import com.kyx.bookmanagementbackend.vo.BookVO;

/**
 * 图书服务接口
 */
public interface BookService {
    
    /**
     * 分页查询图书列表
     */
    Page<BookVO> getBookList(int current, int size, String keyword, Long categoryId, Integer status);
    
    /**
     * 根据ID查询图书详情
     */
    BookVO getBookById(Long id);
    
    /**
     * 创建图书
     */
    void createBook(BookDTO bookDTO);
    
    /**
     * 更新图书信息
     */
    void updateBook(Long id, BookDTO bookDTO);
    
    /**
     * 删除图书
     */
    void deleteBook(Long id);
    
    /**
     * 上架/下架图书
     */
    void toggleBookStatus(Long id);
    
    /**
     * 更新图书库存
     */
    void updateBookQuantity(Long id, Integer totalQuantity, Integer availableQuantity);
    
    /**
     * 借书时减少可借数量
     */
    void decreaseAvailableQuantity(Long bookId, Integer quantity);
    
    /**
     * 还书时增加可借数量
     */
    void increaseAvailableQuantity(Long bookId, Integer quantity);

    /**
     * 获取图书总数
     */
    long getTotalBookCount();

    /**
     * 获取可借图书数
     */
    long getAvailableBookCount();

    /**
     * 获取今日新增图书数
     */
    long getNewBooksToday();

    /**
     * 获取本月新增图书数
     */
    long getNewBooksThisMonth();
}
