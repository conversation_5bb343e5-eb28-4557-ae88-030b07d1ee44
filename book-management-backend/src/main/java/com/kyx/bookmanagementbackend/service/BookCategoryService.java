package com.kyx.bookmanagementbackend.service;

import com.kyx.bookmanagementbackend.dto.BookCategoryDTO;
import com.kyx.bookmanagementbackend.vo.BookCategoryVO;

import java.util.List;

/**
 * 图书分类服务接口
 */
public interface BookCategoryService {
    
    /**
     * 获取分类树形结构
     */
    List<BookCategoryVO> getCategoryTree();
    
    /**
     * 获取所有分类列表
     */
    List<BookCategoryVO> getAllCategories();
    
    /**
     * 根据ID查询分类详情
     */
    BookCategoryVO getCategoryById(Long id);
    
    /**
     * 创建分类
     */
    void createCategory(BookCategoryDTO categoryDTO);
    
    /**
     * 更新分类
     */
    void updateCategory(Long id, BookCategoryDTO categoryDTO);
    
    /**
     * 删除分类
     */
    void deleteCategory(Long id);
    
    /**
     * 启用/禁用分类
     */
    void toggleCategoryStatus(Long id);
}
