package com.kyx.bookmanagementbackend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kyx.bookmanagementbackend.entity.Book;
import com.kyx.bookmanagementbackend.entity.User;
import com.kyx.bookmanagementbackend.entity.UserFavorite;
import com.kyx.bookmanagementbackend.mapper.BookMapper;
import com.kyx.bookmanagementbackend.mapper.UserFavoriteMapper;
import com.kyx.bookmanagementbackend.mapper.UserMapper;
import com.kyx.bookmanagementbackend.service.UserFavoriteService;
import com.kyx.bookmanagementbackend.utils.LogUtil;
import com.kyx.bookmanagementbackend.vo.UserFavoriteVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户收藏服务实现类
 */
@Slf4j
@Service
public class UserFavoriteServiceImpl extends ServiceImpl<UserFavoriteMapper, UserFavorite> implements UserFavoriteService {

    @Autowired
    private UserFavoriteMapper userFavoriteMapper;

    @Autowired
    private BookMapper bookMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addFavorite(Long userId, Long bookId) {
        log.info("用户{}添加收藏图书{}", userId, bookId);
        
        // 检查图书是否存在
        Book book = bookMapper.selectById(bookId);
        if (book == null || book.getDeleted() == 1) {
            log.warn("图书不存在或已删除: {}", bookId);
            throw new RuntimeException("图书不存在");
        }

        // 检查是否已收藏
        if (isFavorited(userId, bookId)) {
            log.warn("用户{}已收藏图书{}", userId, bookId);
            throw new RuntimeException("已收藏该图书");
        }

        // 创建收藏记录
        UserFavorite favorite = new UserFavorite();
        favorite.setUserId(userId);
        favorite.setBookId(bookId);
        favorite.setFavoriteTime(LocalDateTime.now());

        boolean result = save(favorite);
        if (result) {
            User user = userMapper.selectById(userId);
            String username = user != null ? user.getUsername() : "未知用户";
            LogUtil.logUserOperation("ADD_FAVORITE", userId, username, "添加收藏图书: " + book.getTitle());
            log.info("用户{}成功添加收藏图书{}", userId, bookId);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeFavorite(Long userId, Long bookId) {
        log.info("用户{}取消收藏图书{}", userId, bookId);

        LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFavorite::getUserId, userId)
               .eq(UserFavorite::getBookId, bookId);

        UserFavorite favorite = getOne(wrapper);
        if (favorite == null) {
            log.warn("用户{}未收藏图书{}", userId, bookId);
            throw new RuntimeException("未收藏该图书");
        }

        // 使用物理删除避免唯一约束冲突
        // 由于收藏记录不需要保留历史，直接物理删除
        LambdaQueryWrapper<UserFavorite> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(UserFavorite::getUserId, userId)
                    .eq(UserFavorite::getBookId, bookId);

        boolean result = userFavoriteMapper.delete(deleteWrapper) > 0;

        if (result) {
            Book book = bookMapper.selectById(bookId);
            User user = userMapper.selectById(userId);
            String username = user != null ? user.getUsername() : "未知用户";
            LogUtil.logUserOperation("REMOVE_FAVORITE", userId, username, "取消收藏图书: " + (book != null ? book.getTitle() : bookId));
            log.info("用户{}成功取消收藏图书{}", userId, bookId);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleFavorite(Long userId, Long bookId) {
        log.info("用户{}切换收藏状态，图书{}", userId, bookId);
        
        if (isFavorited(userId, bookId)) {
            removeFavorite(userId, bookId);
            return false; // 返回false表示已取消收藏
        } else {
            addFavorite(userId, bookId);
            return true; // 返回true表示已添加收藏
        }
    }

    @Override
    public boolean isFavorited(Long userId, Long bookId) {
        int count = userFavoriteMapper.checkUserFavorite(userId, bookId);
        return count > 0;
    }

    @Override
    public IPage<UserFavoriteVO> getUserFavorites(Long userId, Long current, Long size) {
        log.info("查询用户{}的收藏列表，页码{}，大小{}", userId, current, size);
        
        Page<UserFavoriteVO> page = new Page<>(current, size);
        IPage<UserFavoriteVO> result = userFavoriteMapper.selectUserFavoritesWithBookInfo(page, userId);
        
        log.info("用户{}收藏列表查询完成，总数{}", userId, result.getTotal());
        return result;
    }

    @Override
    public List<Long> getUserFavoriteBookIds(Long userId) {
        log.debug("查询用户{}的收藏图书ID列表", userId);
        return userFavoriteMapper.selectUserFavoriteBookIds(userId);
    }

    @Override
    public int getBookFavoriteCount(Long bookId) {
        log.debug("查询图书{}的收藏数量", bookId);
        return userFavoriteMapper.getBookFavoriteCount(bookId);
    }

    @Override
    public Map<Long, Boolean> batchCheckFavoriteStatus(Long userId, List<Long> bookIds) {
        log.debug("批量查询用户{}的图书收藏状态", userId);
        
        if (bookIds == null || bookIds.isEmpty()) {
            return new HashMap<>();
        }

        List<Long> favoritedBookIds = userFavoriteMapper.batchCheckFavoriteStatus(userId, bookIds);
        
        return bookIds.stream()
                .collect(Collectors.toMap(
                    bookId -> bookId,
                    bookId -> favoritedBookIds.contains(bookId)
                ));
    }

    @Override
    public Map<String, Object> getUserFavoriteStats(Long userId) {
        log.info("查询用户{}的收藏统计信息", userId);
        
        Map<String, Object> stats = new HashMap<>();
        
        // 总收藏数
        LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFavorite::getUserId, userId);
        long totalCount = count(wrapper);
        
        stats.put("totalCount", totalCount);
        stats.put("userId", userId);
        
        log.info("用户{}收藏统计: 总数{}", userId, totalCount);
        return stats;
    }
}
