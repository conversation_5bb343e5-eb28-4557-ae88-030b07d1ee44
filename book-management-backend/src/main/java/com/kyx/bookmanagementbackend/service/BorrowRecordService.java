package com.kyx.bookmanagementbackend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.dto.BorrowDTO;
import com.kyx.bookmanagementbackend.vo.BorrowRecordVO;

import java.util.Map;

/**
 * 借阅记录服务接口
 */
public interface BorrowRecordService {
    
    /**
     * 借书
     */
    void borrowBook(Long userId, BorrowDTO borrowDTO);
    
    /**
     * 还书
     */
    void returnBook(Long recordId, String remarks);
    
    /**
     * 续借
     */
    void renewBook(Long recordId);

    /**
     * 检查续借条件
     */
    Map<String, Object> checkRenewEligibility(Long recordId);
    
    /**
     * 分页查询借阅记录
     */
    Page<BorrowRecordVO> getBorrowRecordList(int current, int size, Long userId, Long bookId, 
                                           String status, String keyword);
    
    /**
     * 根据ID查询借阅记录详情
     */
    BorrowRecordVO getBorrowRecordById(Long id);
    
    /**
     * 查询用户当前借阅的图书数量
     */
    int getUserCurrentBorrowCount(Long userId);
    
    /**
     * 检查用户是否有逾期图书
     */
    boolean hasOverdueBooks(Long userId);
    
    /**
     * 处理逾期图书（定时任务）
     */
    void processOverdueBooks();
    
    /**
     * 计算逾期罚金
     */
    void calculateOverdueFine(Long recordId);

    /**
     * 获取借阅总数
     */
    long getTotalBorrowCount();

    /**
     * 获取当前借阅数
     */
    long getActiveBorrowCount();

    /**
     * 获取逾期数量
     */
    long getOverdueCount();

    /**
     * 获取今日归还数
     */
    long getReturnedToday();

    /**
     * 获取今日借阅数
     */
    long getBorrowedToday();

    /**
     * 检查用户是否当前正在借阅指定图书
     */
    boolean hasCurrentlyBorrowedBook(Long userId, Long bookId);

    /**
     * 检查用户是否历史上借过指定图书
     */
    boolean hasHistoryBorrowedBook(Long userId, Long bookId);

    /**
     * 获取用户最后借阅指定图书的日期
     */
    String getLastBorrowDate(Long userId, Long bookId);
}
