package com.kyx.bookmanagementbackend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.dto.BookDTO;
import com.kyx.bookmanagementbackend.entity.Book;
import com.kyx.bookmanagementbackend.exception.BusinessException;
import com.kyx.bookmanagementbackend.mapper.BookMapper;
import com.kyx.bookmanagementbackend.service.BookService;
import com.kyx.bookmanagementbackend.vo.BookVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.kyx.bookmanagementbackend.utils.LogUtil;

import java.time.LocalDateTime;

/**
 * 图书服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BookServiceImpl implements BookService {

    private final BookMapper bookMapper;
    
    @Override
    public Page<BookVO> getBookList(int current, int size, String keyword, Long categoryId, Integer status) {
        log.info("开始查询图书列表 - 页码: {}, 大小: {}, 关键词: {}, 分类ID: {}, 状态: {}",
                current, size, keyword, categoryId, status);

        Page<BookVO> page = new Page<>(current, size);
        Page<BookVO> result = bookMapper.selectBookPage(page, keyword, categoryId, status);

        log.info("图书列表查询完成 - 总记录数: {}, 当前页记录数: {}",
                result.getTotal(), result.getRecords().size());
        LogUtil.logBusinessOperation("图书管理", "查询图书列表", "成功",
                "总数:" + result.getTotal(), "页码:" + current);

        return result;
    }
    
    @Override
    public BookVO getBookById(Long id) {
        log.debug("查询图书详情 - ID: {}", id);

        BookVO bookVO = bookMapper.selectBookById(id);
        if (bookVO == null) {
            log.warn("图书不存在 - ID: {}", id);
            LogUtil.logBusinessOperation("图书管理", "查询图书详情", "失败", "图书不存在, ID:" + id);
            throw new BusinessException("图书不存在");
        }

        log.debug("图书详情查询成功 - ID: {}, 标题: {}", id, bookVO.getTitle());
        return bookVO;
    }
    
    @Override
    @Transactional
    public void createBook(BookDTO bookDTO) {
        log.info("开始创建图书 - 标题: {}, ISBN: {}, 作者: {}",
                bookDTO.getTitle(), bookDTO.getIsbn(), bookDTO.getAuthor());

        // 检查ISBN是否已存在
        if (StrUtil.isNotBlank(bookDTO.getIsbn())) {
            log.debug("检查ISBN是否已存在 - ISBN: {}", bookDTO.getIsbn());
            LambdaQueryWrapper<Book> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Book::getIsbn, bookDTO.getIsbn());
            if (bookMapper.selectOne(wrapper) != null) {
                log.warn("图书创建失败 - ISBN号已存在: {}", bookDTO.getIsbn());
                LogUtil.logBusinessOperation("图书管理", "创建图书", "失败", "ISBN已存在:" + bookDTO.getIsbn());
                throw new BusinessException("ISBN号已存在");
            }
        }

        // 验证可借数量不能大于总数量
        if (bookDTO.getAvailableQuantity() > bookDTO.getTotalQuantity()) {
            log.warn("图书创建失败 - 可借数量({})大于总数量({})",
                    bookDTO.getAvailableQuantity(), bookDTO.getTotalQuantity());
            LogUtil.logBusinessOperation("图书管理", "创建图书", "失败",
                    "数量验证失败", "可借:" + bookDTO.getAvailableQuantity(), "总数:" + bookDTO.getTotalQuantity());
            throw new BusinessException("可借数量不能大于总数量");
        }

        Book book = new Book();
        BeanUtil.copyProperties(bookDTO, book);
        bookMapper.insert(book);

        log.info("图书创建成功 - ID: {}, 标题: {}", book.getId(), book.getTitle());
        LogUtil.logBusinessOperation("图书管理", "创建图书", "成功",
                "ID:" + book.getId(), "标题:" + book.getTitle(), "ISBN:" + book.getIsbn());
    }
    
    @Override
    @Transactional
    public void updateBook(Long id, BookDTO bookDTO) {
        Book existingBook = bookMapper.selectById(id);
        if (existingBook == null) {
            throw new BusinessException("图书不存在");
        }
        
        // 检查ISBN是否被其他图书使用
        if (StrUtil.isNotBlank(bookDTO.getIsbn()) && 
            !bookDTO.getIsbn().equals(existingBook.getIsbn())) {
            LambdaQueryWrapper<Book> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Book::getIsbn, bookDTO.getIsbn())
                   .ne(Book::getId, id);
            if (bookMapper.selectOne(wrapper) != null) {
                throw new BusinessException("ISBN号已被其他图书使用");
            }
        }
        
        // 验证可借数量不能大于总数量
        if (bookDTO.getAvailableQuantity() > bookDTO.getTotalQuantity()) {
            throw new BusinessException("可借数量不能大于总数量");
        }
        
        Book book = new Book();
        BeanUtil.copyProperties(bookDTO, book);
        book.setId(id);
        bookMapper.updateById(book);
    }
    
    @Override
    @Transactional
    public void deleteBook(Long id) {
        log.info("开始删除图书 - ID: {}", id);

        Book book = bookMapper.selectById(id);
        if (book == null) {
            log.warn("图书删除失败 - 图书不存在, ID: {}", id);
            LogUtil.logBusinessOperation("图书管理", "删除图书", "失败", "图书不存在, ID:" + id);
            throw new BusinessException("图书不存在");
        }

        // 检查是否有未归还的借阅记录
        // TODO: 实现借阅记录检查
        log.debug("检查图书借阅记录 - 图书ID: {}", id);

        bookMapper.deleteById(id);

        log.info("图书删除成功 - ID: {}, 标题: {}", id, book.getTitle());
        LogUtil.logBusinessOperation("图书管理", "删除图书", "成功",
                "ID:" + id, "标题:" + book.getTitle());
    }
    
    @Override
    @Transactional
    public void toggleBookStatus(Long id) {
        Book book = bookMapper.selectById(id);
        if (book == null) {
            throw new BusinessException("图书不存在");
        }
        
        book.setStatus(book.getStatus() == 1 ? 0 : 1);
        bookMapper.updateById(book);
    }
    
    @Override
    @Transactional
    public void updateBookQuantity(Long id, Integer totalQuantity, Integer availableQuantity) {
        Book book = bookMapper.selectById(id);
        if (book == null) {
            throw new BusinessException("图书不存在");
        }
        
        if (availableQuantity > totalQuantity) {
            throw new BusinessException("可借数量不能大于总数量");
        }
        
        book.setTotalQuantity(totalQuantity);
        book.setAvailableQuantity(availableQuantity);
        bookMapper.updateById(book);
    }
    
    @Override
    @Transactional
    public void decreaseAvailableQuantity(Long bookId, Integer quantity) {
        Book book = bookMapper.selectById(bookId);
        if (book == null) {
            throw new BusinessException("图书不存在");
        }
        
        if (book.getAvailableQuantity() < quantity) {
            throw new BusinessException("图书库存不足");
        }
        
        book.setAvailableQuantity(book.getAvailableQuantity() - quantity);
        bookMapper.updateById(book);
    }
    
    @Override
    @Transactional
    public void increaseAvailableQuantity(Long bookId, Integer quantity) {
        Book book = bookMapper.selectById(bookId);
        if (book == null) {
            throw new BusinessException("图书不存在");
        }
        
        int newAvailableQuantity = book.getAvailableQuantity() + quantity;
        if (newAvailableQuantity > book.getTotalQuantity()) {
            throw new BusinessException("可借数量不能超过总数量");
        }
        
        book.setAvailableQuantity(newAvailableQuantity);
        bookMapper.updateById(book);
    }

    @Override
    public long getTotalBookCount() {
        return bookMapper.selectCount(null);
    }

    @Override
    public long getAvailableBookCount() {
        LambdaQueryWrapper<Book> wrapper = new LambdaQueryWrapper<>();
        wrapper.gt(Book::getAvailableQuantity, 0)
               .eq(Book::getStatus, 1);
        return bookMapper.selectCount(wrapper);
    }



    @Override
    public long getNewBooksToday() {
        LambdaQueryWrapper<Book> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        wrapper.ge(Book::getCreatedTime, startOfDay);
        return bookMapper.selectCount(wrapper);
    }

    @Override
    public long getNewBooksThisMonth() {
        LambdaQueryWrapper<Book> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        wrapper.ge(Book::getCreatedTime, startOfMonth);
        return bookMapper.selectCount(wrapper);
    }
}
