package com.kyx.bookmanagementbackend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kyx.bookmanagementbackend.dto.BookCategoryDTO;
import com.kyx.bookmanagementbackend.entity.BookCategory;
import com.kyx.bookmanagementbackend.exception.BusinessException;
import com.kyx.bookmanagementbackend.mapper.BookCategoryMapper;
import com.kyx.bookmanagementbackend.service.BookCategoryService;
import com.kyx.bookmanagementbackend.vo.BookCategoryVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 图书分类服务实现类
 */
@Service
@RequiredArgsConstructor
public class BookCategoryServiceImpl implements BookCategoryService {
    
    private final BookCategoryMapper categoryMapper;
    
    @Override
    public List<BookCategoryVO> getCategoryTree() {
        List<BookCategory> allCategories = categoryMapper.selectList(
            new LambdaQueryWrapper<BookCategory>()
                .eq(BookCategory::getStatus, 1)
                .orderByAsc(BookCategory::getSortOrder)
        );
        
        List<BookCategoryVO> categoryVOList = allCategories.stream()
                .map(category -> BeanUtil.copyProperties(category, BookCategoryVO.class))
                .collect(Collectors.toList());
        
        return buildCategoryTree(categoryVOList, 0L);
    }
    
    @Override
    public List<BookCategoryVO> getAllCategories() {
        List<BookCategory> categories = categoryMapper.selectList(
            new LambdaQueryWrapper<BookCategory>()
                .orderByAsc(BookCategory::getSortOrder)
        );
        
        return categories.stream()
                .map(category -> BeanUtil.copyProperties(category, BookCategoryVO.class))
                .collect(Collectors.toList());
    }
    
    @Override
    public BookCategoryVO getCategoryById(Long id) {
        BookCategory category = categoryMapper.selectById(id);
        if (category == null) {
            throw new BusinessException("分类不存在");
        }
        return BeanUtil.copyProperties(category, BookCategoryVO.class);
    }
    
    @Override
    public void createCategory(BookCategoryDTO categoryDTO) {
        // 检查分类名称是否已存在
        LambdaQueryWrapper<BookCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BookCategory::getName, categoryDTO.getName())
               .eq(BookCategory::getParentId, categoryDTO.getParentId());
        
        if (categoryMapper.selectOne(wrapper) != null) {
            throw new BusinessException("同级分类名称已存在");
        }
        
        BookCategory category = new BookCategory();
        BeanUtil.copyProperties(categoryDTO, category);
        categoryMapper.insert(category);
    }
    
    @Override
    public void updateCategory(Long id, BookCategoryDTO categoryDTO) {
        BookCategory existingCategory = categoryMapper.selectById(id);
        if (existingCategory == null) {
            throw new BusinessException("分类不存在");
        }
        
        // 检查分类名称是否已存在（排除自己）
        LambdaQueryWrapper<BookCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BookCategory::getName, categoryDTO.getName())
               .eq(BookCategory::getParentId, categoryDTO.getParentId())
               .ne(BookCategory::getId, id);
        
        if (categoryMapper.selectOne(wrapper) != null) {
            throw new BusinessException("同级分类名称已存在");
        }
        
        BookCategory category = new BookCategory();
        BeanUtil.copyProperties(categoryDTO, category);
        category.setId(id);
        categoryMapper.updateById(category);
    }
    
    @Override
    public void deleteCategory(Long id) {
        BookCategory category = categoryMapper.selectById(id);
        if (category == null) {
            throw new BusinessException("分类不存在");
        }
        
        // 检查是否有子分类
        LambdaQueryWrapper<BookCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BookCategory::getParentId, id);
        if (categoryMapper.selectCount(wrapper) > 0) {
            throw new BusinessException("该分类下存在子分类，无法删除");
        }
        
        // TODO: 检查是否有图书使用该分类
        
        categoryMapper.deleteById(id);
    }
    
    @Override
    public void toggleCategoryStatus(Long id) {
        BookCategory category = categoryMapper.selectById(id);
        if (category == null) {
            throw new BusinessException("分类不存在");
        }
        
        category.setStatus(category.getStatus() == 1 ? 0 : 1);
        categoryMapper.updateById(category);
    }
    
    /**
     * 构建分类树形结构
     */
    private List<BookCategoryVO> buildCategoryTree(List<BookCategoryVO> categories, Long parentId) {
        List<BookCategoryVO> tree = new ArrayList<>();
        
        Map<Long, List<BookCategoryVO>> categoryMap = categories.stream()
                .collect(Collectors.groupingBy(BookCategoryVO::getParentId));
        
        List<BookCategoryVO> rootCategories = categoryMap.get(parentId);
        if (rootCategories != null) {
            for (BookCategoryVO category : rootCategories) {
                List<BookCategoryVO> children = buildCategoryTree(categories, category.getId());
                category.setChildren(children);
                tree.add(category);
            }
        }
        
        return tree;
    }
}
