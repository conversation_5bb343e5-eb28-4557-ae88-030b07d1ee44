package com.kyx.bookmanagementbackend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kyx.bookmanagementbackend.dto.ChangePasswordDTO;
import com.kyx.bookmanagementbackend.dto.UserProfileUpdateDTO;
import com.kyx.bookmanagementbackend.entity.BorrowRecord;
import com.kyx.bookmanagementbackend.entity.User;
import com.kyx.bookmanagementbackend.entity.UserFavorite;
import com.kyx.bookmanagementbackend.exception.BusinessException;
import com.kyx.bookmanagementbackend.mapper.BorrowRecordMapper;
import com.kyx.bookmanagementbackend.mapper.UserFavoriteMapper;
import com.kyx.bookmanagementbackend.mapper.UserMapper;
import com.kyx.bookmanagementbackend.service.ProfileService;
import com.kyx.bookmanagementbackend.vo.UserProfileVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * 个人信息服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProfileServiceImpl implements ProfileService {
    
    private final UserMapper userMapper;
    private final BorrowRecordMapper borrowRecordMapper;
    private final UserFavoriteMapper userFavoriteMapper;
    private final PasswordEncoder passwordEncoder;
    
    // 头像上传路径配置
    private static final String AVATAR_UPLOAD_PATH = "uploads/avatars/";
    private static final String AVATAR_URL_PREFIX = "/api/files/avatars/";
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    private static final String[] ALLOWED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif"};
    
    @Override
    public UserProfileVO getCurrentUserProfile(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        UserProfileVO profileVO = new UserProfileVO();
        BeanUtils.copyProperties(user, profileVO);
        
        // 获取统计信息
        UserProfileVO.BorrowStats stats = getUserStats(userId);
        profileVO.setBorrowStats(stats);
        
        log.info("获取用户{}个人信息成功", userId);
        return profileVO;
    }
    
    @Override
    @Transactional
    public UserProfileVO updateProfile(Long userId, UserProfileUpdateDTO updateDTO) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 检查邮箱是否已被其他用户使用
        if (!Objects.equals(user.getEmail(), updateDTO.getEmail())) {
            LambdaQueryWrapper<User> emailQuery = new LambdaQueryWrapper<>();
            emailQuery.eq(User::getEmail, updateDTO.getEmail())
                     .ne(User::getId, userId);
            if (userMapper.selectCount(emailQuery) > 0) {
                throw new BusinessException("邮箱已被其他用户使用");
            }
        }
        
        // 检查手机号是否已被其他用户使用
        if (updateDTO.getPhone() != null && !Objects.equals(user.getPhone(), updateDTO.getPhone())) {
            LambdaQueryWrapper<User> phoneQuery = new LambdaQueryWrapper<>();
            phoneQuery.eq(User::getPhone, updateDTO.getPhone())
                      .ne(User::getId, userId);
            if (userMapper.selectCount(phoneQuery) > 0) {
                throw new BusinessException("手机号已被其他用户使用");
            }
        }
        
        // 更新用户信息
        user.setRealName(updateDTO.getRealName());
        user.setEmail(updateDTO.getEmail());
        user.setPhone(updateDTO.getPhone());
        if (updateDTO.getAvatar() != null) {
            user.setAvatar(updateDTO.getAvatar());
        }
        user.setUpdatedTime(LocalDateTime.now());
        
        int result = userMapper.updateById(user);
        if (result <= 0) {
            throw new BusinessException("更新个人信息失败");
        }
        
        log.info("用户{}更新个人信息成功", userId);
        return getCurrentUserProfile(userId);
    }
    
    @Override
    @Transactional
    public void changePassword(Long userId, ChangePasswordDTO changePasswordDTO) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 验证原密码
        if (!passwordEncoder.matches(changePasswordDTO.getOldPassword(), user.getPassword())) {
            throw new BusinessException("原密码不正确");
        }
        
        // 验证新密码和确认密码是否一致
        if (!Objects.equals(changePasswordDTO.getNewPassword(), changePasswordDTO.getConfirmPassword())) {
            throw new BusinessException("新密码和确认密码不一致");
        }
        
        // 验证新密码不能与原密码相同
        if (passwordEncoder.matches(changePasswordDTO.getNewPassword(), user.getPassword())) {
            throw new BusinessException("新密码不能与原密码相同");
        }
        
        // 更新密码
        user.setPassword(passwordEncoder.encode(changePasswordDTO.getNewPassword()));
        user.setUpdatedTime(LocalDateTime.now());
        
        int result = userMapper.updateById(user);
        if (result <= 0) {
            throw new BusinessException("修改密码失败");
        }
        
        log.info("用户{}修改密码成功", userId);
    }
    
    @Override
    @Transactional
    public String uploadAvatar(Long userId, MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("请选择要上传的头像文件");
        }
        
        // 验证文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BusinessException("头像文件大小不能超过5MB");
        }
        
        // 验证文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new BusinessException("文件名不能为空");
        }
        
        String extension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
        boolean isValidExtension = false;
        for (String allowedExt : ALLOWED_EXTENSIONS) {
            if (extension.equals(allowedExt)) {
                isValidExtension = true;
                break;
            }
        }
        
        if (!isValidExtension) {
            throw new BusinessException("只支持jpg、jpeg、png、gif格式的图片文件");
        }
        
        try {
            // 创建上传目录
            Path uploadDir = Paths.get(AVATAR_UPLOAD_PATH);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }
            
            // 生成唯一文件名
            String filename = userId + "_" + UUID.randomUUID().toString() + extension;
            Path filePath = uploadDir.resolve(filename);
            
            // 保存文件
            Files.copy(file.getInputStream(), filePath);
            
            // 生成访问URL
            String avatarUrl = AVATAR_URL_PREFIX + filename;
            
            // 更新用户头像URL
            User user = userMapper.selectById(userId);
            if (user != null) {
                // 删除旧头像文件（如果存在）
                if (user.getAvatar() != null && user.getAvatar().startsWith(AVATAR_URL_PREFIX)) {
                    String oldFilename = user.getAvatar().substring(AVATAR_URL_PREFIX.length());
                    Path oldFilePath = Paths.get(AVATAR_UPLOAD_PATH, oldFilename);
                    try {
                        Files.deleteIfExists(oldFilePath);
                    } catch (IOException e) {
                        log.warn("删除旧头像文件失败: {}", oldFilePath, e);
                    }
                }
                
                user.setAvatar(avatarUrl);
                user.setUpdatedTime(LocalDateTime.now());
                userMapper.updateById(user);
            }
            
            log.info("用户{}上传头像成功: {}", userId, avatarUrl);
            return avatarUrl;
            
        } catch (IOException e) {
            log.error("上传头像文件失败", e);
            throw new BusinessException("上传头像失败: " + e.getMessage());
        }
    }
    
    @Override
    public UserProfileVO.BorrowStats getUserStats(Long userId) {
        UserProfileVO.BorrowStats stats = new UserProfileVO.BorrowStats();
        
        // 总借阅次数
        LambdaQueryWrapper<BorrowRecord> totalQuery = new LambdaQueryWrapper<>();
        totalQuery.eq(BorrowRecord::getUserId, userId);
        stats.setTotalBorrows(Math.toIntExact(borrowRecordMapper.selectCount(totalQuery)));
        
        // 当前借阅数量
        LambdaQueryWrapper<BorrowRecord> activeQuery = new LambdaQueryWrapper<>();
        activeQuery.eq(BorrowRecord::getUserId, userId)
                   .eq(BorrowRecord::getStatus, "BORROWED");
        stats.setActiveBorrows(Math.toIntExact(borrowRecordMapper.selectCount(activeQuery)));
        
        // 逾期借阅数量
        LambdaQueryWrapper<BorrowRecord> overdueQuery = new LambdaQueryWrapper<>();
        overdueQuery.eq(BorrowRecord::getUserId, userId)
                    .eq(BorrowRecord::getStatus, "BORROWED")
                    .lt(BorrowRecord::getDueTime, LocalDateTime.now());
        stats.setOverdueBorrows(Math.toIntExact(borrowRecordMapper.selectCount(overdueQuery)));
        
        // 总收藏数量
        LambdaQueryWrapper<UserFavorite> favoriteQuery = new LambdaQueryWrapper<>();
        favoriteQuery.eq(UserFavorite::getUserId, userId);
        stats.setTotalFavorites(Math.toIntExact(userFavoriteMapper.selectCount(favoriteQuery)));
        
        return stats;
    }
}
