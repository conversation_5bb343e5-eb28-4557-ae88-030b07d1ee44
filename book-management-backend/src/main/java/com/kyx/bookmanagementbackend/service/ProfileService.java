package com.kyx.bookmanagementbackend.service;

import com.kyx.bookmanagementbackend.dto.ChangePasswordDTO;
import com.kyx.bookmanagementbackend.dto.UserProfileUpdateDTO;
import com.kyx.bookmanagementbackend.vo.UserProfileVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 个人信息服务接口
 */
public interface ProfileService {
    
    /**
     * 获取当前用户个人信息
     * @param userId 用户ID
     * @return 用户个人信息
     */
    UserProfileVO getCurrentUserProfile(Long userId);
    
    /**
     * 更新个人信息
     * @param userId 用户ID
     * @param updateDTO 更新信息
     * @return 更新后的用户信息
     */
    UserProfileVO updateProfile(Long userId, UserProfileUpdateDTO updateDTO);
    
    /**
     * 修改密码
     * @param userId 用户ID
     * @param changePasswordDTO 密码修改信息
     */
    void changePassword(Long userId, ChangePasswordDTO changePasswordDTO);
    
    /**
     * 上传头像
     * @param userId 用户ID
     * @param file 头像文件
     * @return 头像URL
     */
    String uploadAvatar(Long userId, MultipartFile file);
    
    /**
     * 获取用户统计信息
     * @param userId 用户ID
     * @return 统计信息
     */
    UserProfileVO.BorrowStats getUserStats(Long userId);
}
