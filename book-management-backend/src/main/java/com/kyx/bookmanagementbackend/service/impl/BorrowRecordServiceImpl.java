package com.kyx.bookmanagementbackend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.dto.BorrowDTO;
import com.kyx.bookmanagementbackend.entity.BorrowRecord;
import com.kyx.bookmanagementbackend.exception.BusinessException;
import com.kyx.bookmanagementbackend.mapper.BorrowRecordMapper;
import com.kyx.bookmanagementbackend.service.BookService;
import com.kyx.bookmanagementbackend.service.BorrowRecordService;
import com.kyx.bookmanagementbackend.vo.BorrowRecordVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.kyx.bookmanagementbackend.utils.LogUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 借阅记录服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BorrowRecordServiceImpl implements BorrowRecordService {
    
    private final BorrowRecordMapper borrowRecordMapper;
    private final BookService bookService;
    
    @Value("${borrow.max.books:5}")
    private int maxBorrowBooks;
    
    @Value("${borrow.max.days:30}")
    private int maxBorrowDays;
    
    @Value("${fine.per.day:1.00}")
    private BigDecimal finePerDay;
    
    @Override
    @Transactional
    public void borrowBook(Long userId, BorrowDTO borrowDTO) {
        log.info("用户开始借阅图书 - 用户ID: {}, 图书ID: {}", userId, borrowDTO.getBookId());

        // 检查用户当前借阅数量
        int currentBorrowCount = getUserCurrentBorrowCount(userId);
        log.debug("用户当前借阅数量: {}, 最大允许: {}", currentBorrowCount, maxBorrowBooks);

        if (currentBorrowCount >= maxBorrowBooks) {
            log.warn("用户借阅失败 - 借阅数量已达上限, 用户ID: {}, 当前借阅: {}", userId, currentBorrowCount);
            LogUtil.logBusinessOperation("借阅管理", "借阅图书", "失败",
                    "借阅数量已达上限", "用户ID:" + userId, "当前借阅:" + currentBorrowCount);
            throw new BusinessException("借阅数量已达上限，最多可借阅" + maxBorrowBooks + "本图书");
        }

        // 检查用户是否有逾期图书
        if (hasOverdueBooks(userId)) {
            log.warn("用户借阅失败 - 有逾期图书未归还, 用户ID: {}", userId);
            LogUtil.logBusinessOperation("借阅管理", "借阅图书", "失败",
                    "有逾期图书未归还", "用户ID:" + userId);
            throw new BusinessException("您有逾期图书未归还，请先归还逾期图书");
        }

        // 检查用户是否当前正在借阅该图书（防止重复借阅）
        if (hasCurrentlyBorrowedBook(userId, borrowDTO.getBookId())) {
            log.warn("用户借阅失败 - 当前正在借阅该图书, 用户ID: {}, 图书ID: {}", userId, borrowDTO.getBookId());
            LogUtil.logBusinessOperation("借阅管理", "借阅图书", "失败",
                    "当前正在借阅该图书", "用户ID:" + userId, "图书ID:" + borrowDTO.getBookId());
            throw new BusinessException("您当前正在借阅这本图书，无法重复借阅");
        }

        // 检查图书是否可借
        log.debug("减少图书可借数量 - 图书ID: {}", borrowDTO.getBookId());
        bookService.decreaseAvailableQuantity(borrowDTO.getBookId(), 1);

        // 创建借阅记录
        BorrowRecord borrowRecord = new BorrowRecord();
        borrowRecord.setUserId(userId);
        borrowRecord.setBookId(borrowDTO.getBookId());
        borrowRecord.setBorrowTime(LocalDateTime.now());
        borrowRecord.setDueTime(LocalDateTime.now().plusDays(maxBorrowDays));
        borrowRecord.setStatus("BORROWED");
        borrowRecord.setOverdueDays(0);
        borrowRecord.setFineAmount(BigDecimal.ZERO);
        borrowRecord.setRemarks(borrowDTO.getRemarks());

        borrowRecordMapper.insert(borrowRecord);

        log.info("图书借阅成功 - 用户ID: {}, 图书ID: {}, 借阅记录ID: {}, 应还日期: {}",
                userId, borrowDTO.getBookId(), borrowRecord.getId(), borrowRecord.getDueTime());
        LogUtil.logBusinessOperation("借阅管理", "借阅图书", "成功",
                "用户ID:" + userId, "图书ID:" + borrowDTO.getBookId(), "记录ID:" + borrowRecord.getId());
    }
    
    @Override
    @Transactional
    public void returnBook(Long recordId, String remarks) {
        log.info("开始归还图书 - 借阅记录ID: {}", recordId);

        BorrowRecord borrowRecord = borrowRecordMapper.selectById(recordId);
        if (borrowRecord == null) {
            log.warn("图书归还失败 - 借阅记录不存在, 记录ID: {}", recordId);
            LogUtil.logBusinessOperation("借阅管理", "归还图书", "失败",
                    "借阅记录不存在", "记录ID:" + recordId);
            throw new BusinessException("借阅记录不存在");
        }

        if (!"BORROWED".equals(borrowRecord.getStatus()) && !"OVERDUE".equals(borrowRecord.getStatus())) {
            log.warn("图书归还失败 - 图书已归还, 记录ID: {}, 当前状态: {}", recordId, borrowRecord.getStatus());
            LogUtil.logBusinessOperation("借阅管理", "归还图书", "失败",
                    "图书已归还", "记录ID:" + recordId, "状态:" + borrowRecord.getStatus());
            throw new BusinessException("该图书已归还");
        }

        // 计算逾期天数和罚金
        LocalDateTime now = LocalDateTime.now();
        boolean isOverdue = false;
        if (now.isAfter(borrowRecord.getDueTime())) {
            long overdueDays = ChronoUnit.DAYS.between(borrowRecord.getDueTime(), now);
            borrowRecord.setOverdueDays((int) overdueDays);
            borrowRecord.setFineAmount(finePerDay.multiply(BigDecimal.valueOf(overdueDays)));
            isOverdue = true;
            log.warn("图书逾期归还 - 记录ID: {}, 逾期天数: {}, 罚金: {}",
                    recordId, overdueDays, borrowRecord.getFineAmount());
        }

        // 更新借阅记录
        borrowRecord.setReturnTime(now);
        borrowRecord.setStatus("RETURNED");
        borrowRecord.setRemarks(remarks);
        borrowRecordMapper.updateById(borrowRecord);

        // 增加图书可借数量
        log.debug("增加图书可借数量 - 图书ID: {}", borrowRecord.getBookId());
        bookService.increaseAvailableQuantity(borrowRecord.getBookId(), 1);

        log.info("图书归还成功 - 记录ID: {}, 用户ID: {}, 图书ID: {}, 是否逾期: {}",
                recordId, borrowRecord.getUserId(), borrowRecord.getBookId(), isOverdue);
        LogUtil.logBusinessOperation("借阅管理", "归还图书", "成功",
                "记录ID:" + recordId, "用户ID:" + borrowRecord.getUserId(),
                "图书ID:" + borrowRecord.getBookId(), "逾期:" + isOverdue);
    }
    
    @Override
    @Transactional
    public void renewBook(Long recordId) {
        BorrowRecord borrowRecord = borrowRecordMapper.selectById(recordId);
        if (borrowRecord == null) {
            throw new BusinessException("借阅记录不存在");
        }

        if (!"BORROWED".equals(borrowRecord.getStatus())) {
            throw new BusinessException("只能续借已借出的图书");
        }

        LocalDateTime now = LocalDateTime.now();

        // 检查是否已逾期
        if (now.isAfter(borrowRecord.getDueTime())) {
            throw new BusinessException("图书已逾期，无法续借");
        }

        // 检查是否在续借时间窗口内（距离到期日5天内）
        long daysUntilDue = ChronoUnit.DAYS.between(now.toLocalDate(), borrowRecord.getDueTime().toLocalDate());
        if (daysUntilDue > 5) {
            throw new BusinessException("只能在距离到期日5天内申请续借，还需等待" + (daysUntilDue - 5) + "天");
        }

        // 检查续借次数限制（这里需要添加续借次数字段到数据库，暂时跳过）
        // 检查总借阅时间是否超过3个月
        long monthsBorrowed = ChronoUnit.MONTHS.between(borrowRecord.getBorrowTime().toLocalDate(), now.toLocalDate());
        if (monthsBorrowed >= 3) {
            throw new BusinessException("总借阅时间已超过3个月限制，无法续借");
        }

        log.info("续借检查通过 - 记录ID: {}, 距离到期: {}天, 已借阅: {}个月",
                recordId, daysUntilDue, monthsBorrowed);

        // 续借30天
        borrowRecord.setDueTime(borrowRecord.getDueTime().plusDays(maxBorrowDays));
        borrowRecordMapper.updateById(borrowRecord);

        log.info("续借成功 - 记录ID: {}, 新到期日: {}", recordId, borrowRecord.getDueTime());
    }

    @Override
    public Map<String, Object> checkRenewEligibility(Long recordId) {
        Map<String, Object> result = new HashMap<>();

        BorrowRecord borrowRecord = borrowRecordMapper.selectById(recordId);
        if (borrowRecord == null) {
            result.put("canRenew", false);
            result.put("reason", "借阅记录不存在");
            return result;
        }

        if (!"BORROWED".equals(borrowRecord.getStatus())) {
            result.put("canRenew", false);
            result.put("reason", "只能续借已借出的图书");
            return result;
        }

        LocalDateTime now = LocalDateTime.now();

        // 检查是否已逾期
        if (now.isAfter(borrowRecord.getDueTime())) {
            result.put("canRenew", false);
            result.put("reason", "图书已逾期，无法续借");
            return result;
        }

        // 检查是否在续借时间窗口内（距离到期日5天内）
        long daysUntilDue = ChronoUnit.DAYS.between(now.toLocalDate(), borrowRecord.getDueTime().toLocalDate());
        boolean withinTimeWindow = daysUntilDue <= 5 && daysUntilDue >= 0;

        // 检查总借阅时间是否超过3个月
        long monthsBorrowed = ChronoUnit.MONTHS.between(borrowRecord.getBorrowTime().toLocalDate(), now.toLocalDate());
        boolean withinDurationLimit = monthsBorrowed < 3;

        // 检查续借次数（暂时设为可续借，实际需要数据库字段支持）
        boolean withinRenewLimit = true; // 假设未超过续借次数限制

        boolean canRenew = withinTimeWindow && withinDurationLimit && withinRenewLimit;

        result.put("canRenew", canRenew);
        result.put("daysUntilDue", daysUntilDue);
        result.put("monthsBorrowed", monthsBorrowed);
        result.put("withinTimeWindow", withinTimeWindow);
        result.put("withinDurationLimit", withinDurationLimit);
        result.put("withinRenewLimit", withinRenewLimit);

        if (!canRenew) {
            if (!withinTimeWindow) {
                if (daysUntilDue > 5) {
                    result.put("reason", "只能在距离到期日5天内申请续借，还需等待" + (daysUntilDue - 5) + "天");
                } else {
                    result.put("reason", "图书已逾期，无法续借");
                }
            } else if (!withinDurationLimit) {
                result.put("reason", "总借阅时间已超过3个月限制，无法续借");
            } else if (!withinRenewLimit) {
                result.put("reason", "已达到最大续借次数限制");
            }
        } else {
            result.put("reason", "可以续借");
        }

        log.info("续借条件检查 - 记录ID: {}, 可续借: {}, 距离到期: {}天, 已借阅: {}个月",
                recordId, canRenew, daysUntilDue, monthsBorrowed);

        return result;
    }
    
    @Override
    public Page<BorrowRecordVO> getBorrowRecordList(int current, int size, Long userId, Long bookId, 
                                                   String status, String keyword) {
        Page<BorrowRecordVO> page = new Page<>(current, size);
        return borrowRecordMapper.selectBorrowRecordPage(page, userId, bookId, status, keyword);
    }
    
    @Override
    public BorrowRecordVO getBorrowRecordById(Long id) {
        BorrowRecordVO borrowRecordVO = borrowRecordMapper.selectBorrowRecordById(id);
        if (borrowRecordVO == null) {
            throw new BusinessException("借阅记录不存在");
        }
        return borrowRecordVO;
    }
    
    @Override
    public int getUserCurrentBorrowCount(Long userId) {
        return borrowRecordMapper.countUserCurrentBorrows(userId);
    }
    
    @Override
    public boolean hasOverdueBooks(Long userId) {
        return borrowRecordMapper.countUserOverdueBooks(userId) > 0;
    }
    
    @Override
    @Transactional
    public void processOverdueBooks() {
        List<BorrowRecord> overdueRecords = borrowRecordMapper.selectOverdueRecords();
        
        for (BorrowRecord record : overdueRecords) {
            // 更新状态为逾期
            record.setStatus("OVERDUE");
            
            // 计算逾期天数和罚金
            LocalDateTime now = LocalDateTime.now();
            long overdueDays = ChronoUnit.DAYS.between(record.getDueTime(), now);
            record.setOverdueDays((int) overdueDays);
            record.setFineAmount(finePerDay.multiply(BigDecimal.valueOf(overdueDays)));
            
            borrowRecordMapper.updateById(record);
        }
    }
    
    @Override
    @Transactional
    public void calculateOverdueFine(Long recordId) {
        BorrowRecord borrowRecord = borrowRecordMapper.selectById(recordId);
        if (borrowRecord == null) {
            throw new BusinessException("借阅记录不存在");
        }
        
        if ("OVERDUE".equals(borrowRecord.getStatus())) {
            LocalDateTime now = LocalDateTime.now();
            long overdueDays = ChronoUnit.DAYS.between(borrowRecord.getDueTime(), now);
            borrowRecord.setOverdueDays((int) overdueDays);
            borrowRecord.setFineAmount(finePerDay.multiply(BigDecimal.valueOf(overdueDays)));
            borrowRecordMapper.updateById(borrowRecord);
        }
    }

    @Override
    public long getTotalBorrowCount() {
        return borrowRecordMapper.selectCount(null);
    }

    @Override
    public long getActiveBorrowCount() {
        LambdaQueryWrapper<BorrowRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BorrowRecord::getStatus, "BORROWED");
        return borrowRecordMapper.selectCount(wrapper);
    }

    @Override
    public long getOverdueCount() {
        LambdaQueryWrapper<BorrowRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BorrowRecord::getStatus, "OVERDUE");
        return borrowRecordMapper.selectCount(wrapper);
    }

    @Override
    public long getReturnedToday() {
        LambdaQueryWrapper<BorrowRecord> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        wrapper.eq(BorrowRecord::getStatus, "RETURNED")
               .ge(BorrowRecord::getReturnTime, startOfDay);
        return borrowRecordMapper.selectCount(wrapper);
    }

    @Override
    public long getBorrowedToday() {
        LambdaQueryWrapper<BorrowRecord> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        wrapper.ge(BorrowRecord::getBorrowTime, startOfDay);
        return borrowRecordMapper.selectCount(wrapper);
    }

    @Override
    public boolean hasCurrentlyBorrowedBook(Long userId, Long bookId) {
        LambdaQueryWrapper<BorrowRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BorrowRecord::getUserId, userId)
               .eq(BorrowRecord::getBookId, bookId)
               .eq(BorrowRecord::getStatus, "BORROWED")
               .eq(BorrowRecord::getDeleted, 0);
        return borrowRecordMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean hasHistoryBorrowedBook(Long userId, Long bookId) {
        LambdaQueryWrapper<BorrowRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BorrowRecord::getUserId, userId)
               .eq(BorrowRecord::getBookId, bookId)
               .eq(BorrowRecord::getDeleted, 0);
        return borrowRecordMapper.selectCount(wrapper) > 0;
    }

    @Override
    public String getLastBorrowDate(Long userId, Long bookId) {
        LambdaQueryWrapper<BorrowRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BorrowRecord::getUserId, userId)
               .eq(BorrowRecord::getBookId, bookId)
               .eq(BorrowRecord::getDeleted, 0)
               .orderByDesc(BorrowRecord::getBorrowTime)
               .last("LIMIT 1");

        BorrowRecord record = borrowRecordMapper.selectOne(wrapper);
        if (record != null && record.getBorrowTime() != null) {
            return record.getBorrowTime().toLocalDate().toString();
        }
        return null;
    }
}
