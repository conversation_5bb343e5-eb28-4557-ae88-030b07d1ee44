<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyx.bookmanagementbackend.mapper.BookMapper">

    <resultMap id="BookVOMap" type="com.kyx.bookmanagementbackend.vo.BookVO">
        <id column="id" property="id"/>
        <result column="isbn" property="isbn"/>
        <result column="title" property="title"/>
        <result column="author" property="author"/>
        <result column="publisher" property="publisher"/>
        <result column="publish_date" property="publishDate"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="description" property="description"/>
        <result column="cover_url" property="coverUrl"/>
        <result column="price" property="price"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="available_quantity" property="availableQuantity"/>
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <select id="selectBookPage" resultMap="BookVOMap">
        SELECT 
            b.id,
            b.isbn,
            b.title,
            b.author,
            b.publisher,
            b.publish_date,
            b.category_id,
            bc.name as category_name,
            b.description,
            b.cover_url,
            b.price,
            b.total_quantity,
            b.available_quantity,
            b.status,
            b.created_time,
            b.updated_time
        FROM book b
        LEFT JOIN book_category bc ON b.category_id = bc.id
        WHERE b.deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (b.title LIKE CONCAT('%', #{keyword}, '%') 
                 OR b.author LIKE CONCAT('%', #{keyword}, '%')
                 OR b.isbn LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="categoryId != null">
            AND b.category_id = #{categoryId}
        </if>
        <if test="status != null">
            AND b.status = #{status}
        </if>
        ORDER BY b.created_time DESC
    </select>

    <select id="selectBookById" resultMap="BookVOMap">
        SELECT 
            b.id,
            b.isbn,
            b.title,
            b.author,
            b.publisher,
            b.publish_date,
            b.category_id,
            bc.name as category_name,
            b.description,
            b.cover_url,
            b.price,
            b.total_quantity,
            b.available_quantity,
            b.status,
            b.created_time,
            b.updated_time
        FROM book b
        LEFT JOIN book_category bc ON b.category_id = bc.id
        WHERE b.id = #{id} AND b.deleted = 0
    </select>

</mapper>
