<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyx.bookmanagementbackend.mapper.UserFavoriteMapper">

    <!-- 用户收藏VO结果映射 -->
    <resultMap id="UserFavoriteVOMap" type="com.kyx.bookmanagementbackend.vo.UserFavoriteVO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="real_name" property="userRealName"/>
        <result column="book_id" property="bookId"/>
        <result column="book_title" property="bookTitle"/>
        <result column="book_author" property="bookAuthor"/>
        <result column="book_isbn" property="bookIsbn"/>
        <result column="book_category" property="bookCategory"/>
        <result column="book_cover_url" property="bookCoverUrl"/>
        <result column="book_status" property="bookStatus"/>
        <result column="book_available_quantity" property="bookAvailableQuantity"/>
        <result column="favorite_time" property="favoriteTime"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>

    <!-- 分页查询用户收藏列表（带图书信息） -->
    <select id="selectUserFavoritesWithBookInfo" resultMap="UserFavoriteVOMap">
        SELECT 
            uf.id,
            uf.user_id,
            u.username,
            u.real_name,
            uf.book_id,
            b.title as book_title,
            b.author as book_author,
            b.isbn as book_isbn,
            bc.name as book_category,
            b.cover_url as book_cover_url,
            b.status as book_status,
            b.available_quantity as book_available_quantity,
            uf.favorite_time,
            uf.created_time
        FROM user_favorite uf
        LEFT JOIN sys_user u ON uf.user_id = u.id
        LEFT JOIN book b ON uf.book_id = b.id
        LEFT JOIN book_category bc ON b.category_id = bc.id
        WHERE uf.deleted = 0 
        AND uf.user_id = #{userId}
        AND b.deleted = 0
        ORDER BY uf.created_time DESC
    </select>

    <!-- 查询用户收藏的图书ID列表 -->
    <select id="selectUserFavoriteBookIds" resultType="java.lang.Long">
        SELECT book_id 
        FROM user_favorite 
        WHERE deleted = 0 
        AND user_id = #{userId}
    </select>

    <!-- 检查用户是否收藏了指定图书 -->
    <select id="checkUserFavorite" resultType="int">
        SELECT COUNT(1) 
        FROM user_favorite 
        WHERE deleted = 0 
        AND user_id = #{userId} 
        AND book_id = #{bookId}
    </select>

    <!-- 获取图书的收藏数量 -->
    <select id="getBookFavoriteCount" resultType="int">
        SELECT COUNT(1) 
        FROM user_favorite 
        WHERE deleted = 0 
        AND book_id = #{bookId}
    </select>

    <!-- 批量查询图书收藏状态 -->
    <select id="batchCheckFavoriteStatus" resultType="java.lang.Long">
        SELECT book_id 
        FROM user_favorite 
        WHERE deleted = 0 
        AND user_id = #{userId}
        AND book_id IN
        <foreach collection="bookIds" item="bookId" open="(" separator="," close=")">
            #{bookId}
        </foreach>
    </select>

</mapper>
