<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyx.bookmanagementbackend.mapper.BorrowRecordMapper">

    <resultMap id="BorrowRecordVOMap" type="com.kyx.bookmanagementbackend.vo.BorrowRecordVO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="user_real_name" property="userRealName"/>
        <result column="book_id" property="bookId"/>
        <result column="book_title" property="bookTitle"/>
        <result column="book_author" property="bookAuthor"/>
        <result column="book_isbn" property="bookIsbn"/>
        <result column="book_cover_url" property="bookCoverUrl"/>
        <result column="borrow_time" property="borrowTime"/>
        <result column="due_time" property="dueTime"/>
        <result column="return_time" property="returnTime"/>
        <result column="status" property="status"/>
        <result column="overdue_days" property="overdueDays"/>
        <result column="fine_amount" property="fineAmount"/>
        <result column="remarks" property="remarks"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <select id="selectBorrowRecordPage" resultMap="BorrowRecordVOMap">
        SELECT 
            br.id,
            br.user_id,
            u.username,
            u.real_name as user_real_name,
            br.book_id,
            b.title as book_title,
            b.author as book_author,
            b.isbn as book_isbn,
            b.cover_url as book_cover_url,
            br.borrow_time,
            br.due_time,
            br.return_time,
            br.status,
            br.overdue_days,
            br.fine_amount,
            br.remarks,
            br.created_time,
            br.updated_time
        FROM borrow_record br
        LEFT JOIN sys_user u ON br.user_id = u.id
        LEFT JOIN book b ON br.book_id = b.id
        WHERE br.deleted = 0
        <if test="userId != null">
            AND br.user_id = #{userId}
        </if>
        <if test="bookId != null">
            AND br.book_id = #{bookId}
        </if>
        <if test="status != null and status != ''">
            AND br.status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (u.username LIKE CONCAT('%', #{keyword}, '%') 
                 OR u.real_name LIKE CONCAT('%', #{keyword}, '%')
                 OR b.title LIKE CONCAT('%', #{keyword}, '%')
                 OR b.author LIKE CONCAT('%', #{keyword}, '%')
                 OR b.isbn LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY br.created_time DESC
    </select>

    <select id="selectBorrowRecordById" resultMap="BorrowRecordVOMap">
        SELECT 
            br.id,
            br.user_id,
            u.username,
            u.real_name as user_real_name,
            br.book_id,
            b.title as book_title,
            b.author as book_author,
            b.isbn as book_isbn,
            b.cover_url as book_cover_url,
            br.borrow_time,
            br.due_time,
            br.return_time,
            br.status,
            br.overdue_days,
            br.fine_amount,
            br.remarks,
            br.created_time,
            br.updated_time
        FROM borrow_record br
        LEFT JOIN sys_user u ON br.user_id = u.id
        LEFT JOIN book b ON br.book_id = b.id
        WHERE br.id = #{id} AND br.deleted = 0
    </select>

    <select id="countUserCurrentBorrows" resultType="int">
        SELECT COUNT(*)
        FROM borrow_record
        WHERE user_id = #{userId} 
          AND status = 'BORROWED' 
          AND deleted = 0
    </select>

    <select id="countUserOverdueBooks" resultType="int">
        SELECT COUNT(*)
        FROM borrow_record
        WHERE user_id = #{userId} 
          AND status = 'OVERDUE' 
          AND deleted = 0
    </select>

    <select id="selectOverdueRecords" resultType="com.kyx.bookmanagementbackend.entity.BorrowRecord">
        SELECT *
        FROM borrow_record
        WHERE status = 'BORROWED' 
          AND due_time &lt; NOW() 
          AND deleted = 0
    </select>

</mapper>
