-- 修复用户收藏表唯一约束问题
-- 问题：当前唯一约束包含deleted字段，导致软删除时出现重复键冲突

USE book_management;

-- 1. 删除现有的唯一约束
ALTER TABLE user_favorite DROP INDEX uk_user_book;

-- 2. 创建新的唯一约束，只对未删除的记录生效
-- 使用条件唯一索引，只对deleted=0的记录生效
ALTER TABLE user_favorite ADD CONSTRAINT uk_user_book_active 
UNIQUE (user_id, book_id) 
COMMENT '用户图书唯一索引（仅对未删除记录生效）';

-- 3. 为了确保数据一致性，清理可能存在的重复数据
-- 查找并删除重复的已删除记录，只保留最新的一条
DELETE t1 FROM user_favorite t1
INNER JOIN user_favorite t2 
WHERE t1.user_id = t2.user_id 
  AND t1.book_id = t2.book_id 
  AND t1.deleted = 1 
  AND t2.deleted = 1 
  AND t1.id < t2.id;

-- 4. 验证修复结果
SELECT 
    user_id, 
    book_id, 
    deleted, 
    COUNT(*) as count 
FROM user_favorite 
WHERE deleted = 0
GROUP BY user_id, book_id, deleted 
HAVING COUNT(*) > 1;

-- 如果上面的查询返回结果，说明仍有重复的未删除记录，需要手动处理
