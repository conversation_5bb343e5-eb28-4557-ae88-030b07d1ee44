-- 图书管理系统初始化数据脚本

USE book_management;

-- 插入超级管理员用户（密码：kyx200328，使用BCrypt加密）
INSERT INTO sys_user (username, password, email, real_name, role, status) VALUES 
('kyx666', '$2a$10$N.zmdr9k7uOCQb97.AnUu.Zm32dvF.f5/bnZpff/5QrjQJFrHrBJ2', '<EMAIL>', '超级管理员', 'SUPER_ADMIN', 1);

-- 插入默认图书分类
INSERT INTO book_category (name, parent_id, description, sort_order, status) VALUES 
('文学', 0, '文学类图书', 1, 1),
('小说', 1, '小说类图书', 1, 1),
('散文', 1, '散文类图书', 2, 1),
('诗歌', 1, '诗歌类图书', 3, 1),
('科技', 0, '科技类图书', 2, 1),
('计算机', 5, '计算机相关图书', 1, 1),
('数学', 5, '数学相关图书', 2, 1),
('物理', 5, '物理相关图书', 3, 1),
('历史', 0, '历史类图书', 3, 1),
('中国历史', 9, '中国历史相关图书', 1, 1),
('世界历史', 9, '世界历史相关图书', 2, 1),
('艺术', 0, '艺术类图书', 4, 1),
('音乐', 12, '音乐相关图书', 1, 1),
('绘画', 12, '绘画相关图书', 2, 1),
('教育', 0, '教育类图书', 5, 1);

-- 插入示例图书数据
INSERT INTO book (isbn, title, author, publisher, publish_date, category_id, description, cover_url, price, total_quantity, available_quantity, status) VALUES
('9787020002207', '红楼梦', '曹雪芹', '人民文学出版社', '2008-06-01', 2, '中国古典文学四大名著之一', 'https://img3.doubanio.com/view/subject/l/public/s1070959.jpg', 45.00, 10, 10, 1),
('9787020008735', '西游记', '吴承恩', '人民文学出版社', '2009-03-01', 2, '中国古典文学四大名著之一', 'https://img9.doubanio.com/view/subject/l/public/s1058906.jpg', 42.00, 8, 8, 1),
('9787111213826', 'Java核心技术', 'Cay S. Horstmann', '机械工业出版社', '2020-01-01', 6, 'Java编程经典教材', 'https://img1.doubanio.com/view/subject/l/public/s29105625.jpg', 128.00, 15, 15, 1),
('9787302257646', '数据结构与算法分析', 'Mark Allen Weiss', '清华大学出版社', '2019-05-01', 6, '计算机科学经典教材', 'https://img1.doubanio.com/view/subject/l/public/s4610502.jpg', 89.00, 12, 12, 1),
('9787020033904', '平凡的世界', '路遥', '人民文学出版社', '2012-03-01', 2, '茅盾文学奖获奖作品', 'https://img3.doubanio.com/view/subject/l/public/s27279654.jpg', 75.00, 20, 20, 1),
('9787108018274', '万历十五年', '黄仁宇', '生活·读书·新知三联书店', '2006-07-01', 10, '中国历史研究经典', 'https://img1.doubanio.com/view/subject/l/public/s1023473.jpg', 36.00, 6, 6, 1),
('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 7, '高等数学教材', 'https://img3.doubanio.com/view/subject/l/public/s28392161.jpg', 56.00, 25, 25, 1),
('9787040183948', '大学物理', '马文蔚', '高等教育出版社', '2006-12-01', 8, '大学物理教材', 'https://img1.doubanio.com/view/subject/l/public/s2659047.jpg', 48.00, 18, 18, 1);

-- 插入测试用户
INSERT INTO sys_user (username, password, email, real_name, role, status) VALUES
('testuser', '$2a$10$N.zmdr9k7uOCQb97.AnUu.Zm32dvF.f5/bnZpff/5QrjQJFrHrBJ2', '<EMAIL>', '测试用户', 'USER', 1),
('admin', '$2a$10$N.zmdr9k7uOCQb97.AnUu.Zm32dvF.f5/bnZpff/5QrjQJFrHrBJ2', '<EMAIL>', '管理员', 'ADMIN', 1);

-- 插入测试借阅记录
INSERT INTO borrow_record (user_id, book_id, borrow_time, due_time, return_time, status, overdue_days, fine_amount, remarks) VALUES
(2, 1, '2025-06-01 10:00:00', '2025-07-01 23:59:59', NULL, 'BORROWED', 0, 0.00, '测试借阅记录1'),
(2, 3, '2025-06-05 14:30:00', '2025-07-05 23:59:59', NULL, 'BORROWED', 0, 0.00, '测试借阅记录2'),
(2, 5, '2025-05-15 09:15:00', '2025-06-14 23:59:59', '2025-06-10 16:20:00', 'RETURNED', 0, 0.00, '已归还的测试记录'),
(2, 2, '2025-05-01 11:00:00', '2025-05-31 23:59:59', NULL, 'OVERDUE', 10, 10.00, '逾期测试记录');

-- 更新图书可借数量（减去已借出的数量）
UPDATE book SET available_quantity = available_quantity - 1 WHERE id IN (1, 2, 3);

-- 插入系统配置
INSERT INTO sys_config (config_key, config_value, description, type) VALUES
('borrow.max.books', '5', '用户最大借阅图书数量', 'NUMBER'),
('borrow.max.days', '30', '图书借阅最大天数', 'NUMBER'),
('fine.per.day', '1.00', '每日逾期罚金（元）', 'NUMBER'),
('system.name', '图书管理系统', '系统名称', 'STRING'),
('system.version', '1.0.0', '系统版本', 'STRING'),
('email.enabled', 'false', '是否启用邮件通知', 'BOOLEAN'),
('upload.max.size', '10485760', '文件上传最大大小（字节）', 'NUMBER');
