import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },
    server: {
      port: parseInt(env.VITE_DEV_SERVER_PORT) || 3000,
      host: env.VITE_DEV_SERVER_HOST || 'localhost',
      open: true,
      proxy: {
        '/api': {
          target: 'http://localhost:8080',
          changeOrigin: true,
          // 不重写路径，保持/api前缀
          // rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
    },
    build: {
      // 生产环境构建配置
      sourcemap: env.VITE_BUILD_SOURCEMAP === 'true',
      minify: env.VITE_BUILD_MINIFY !== 'false',
      rollupOptions: {
        output: {
          // 分包策略
          manualChunks: {
            vendor: ['vue', 'vue-router', 'pinia'],
            vuetify: ['vuetify'],
            utils: ['axios', 'dayjs'],
          },
        },
      },
      // 构建目标
      target: 'es2015',
      // 资源内联阈值
      assetsInlineLimit: 4096,
    },
    optimizeDeps: {
      include: ['vue', 'vue-router', 'pinia', 'vuetify', 'axios'],
    },
    define: {
      // 全局常量定义
      __APP_VERSION__: JSON.stringify(env.VITE_APP_VERSION || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    },
  }
})