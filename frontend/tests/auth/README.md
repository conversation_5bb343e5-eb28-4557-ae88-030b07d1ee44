# 认证相关测试文件

本目录包含用户认证、登录、登出、token管理等相关的测试文件。

## 文件说明

### 修复总结文档
- `auth-401-error-fix-summary.md` - 401错误修复总结
- `auth-persistence-fix-summary.md` - 认证持久化修复总结
- `auth-solution-comparison.md` - 认证方案对比分析
- `auth-timeout-fix-summary.md` - 认证超时修复总结
- `logout-timeout-fix-summary.md` - 登出超时修复总结

### 测试工具
- `auth-debug-tool.html` - 认证调试工具
- `auth-persistence-test.html` - 认证持久化测试工具
- `auth-timeout-debug.html` - 认证超时调试工具
- `enhanced-auth-test.html` - 增强认证测试工具
- `logout-debug-tool.html` - 登出调试工具

## 使用方法

### 运行测试工具
在浏览器中直接打开HTML文件即可使用相应的测试工具。

### 查看修复总结
使用Markdown阅读器查看修复总结文档，了解问题的解决过程。

## 测试环境要求
- 确保后端服务运行在 `http://localhost:8080`
- 需要有效的用户账号进行测试
- 检查JWT token配置是否正确
