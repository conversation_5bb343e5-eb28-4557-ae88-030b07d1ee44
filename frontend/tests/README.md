# 前端测试文件目录

本目录包含前端项目的所有测试相关文件，按功能模块分类整理。

## 目录结构

```
frontend/tests/
├── README.md                    # 本说明文档
├── auth/                        # 认证相关测试
├── avatar/                      # 头像功能测试
├── profile/                     # 个人资料测试
├── api/                         # API接口测试
└── features/                    # 功能特性测试
```

## 各目录说明

### 🔐 auth/ - 认证相关测试
包含用户认证、登录、登出、token管理等相关的测试文件。

**文件列表：**
- `auth-401-error-fix-summary.md` - 401错误修复总结
- `auth-debug-tool.html` - 认证调试工具
- `auth-persistence-fix-summary.md` - 认证持久化修复总结
- `auth-persistence-test.html` - 认证持久化测试工具
- `auth-solution-comparison.md` - 认证方案对比
- `auth-timeout-debug.html` - 认证超时调试工具
- `auth-timeout-fix-summary.md` - 认证超时修复总结
- `enhanced-auth-test.html` - 增强认证测试工具
- `logout-debug-tool.html` - 登出调试工具
- `logout-timeout-fix-summary.md` - 登出超时修复总结

### 🖼️ avatar/ - 头像功能测试
包含用户头像显示、上传、URL处理等相关的测试文件。

**文件列表：**
- `avatar-display-debug.html` - 头像显示调试工具
- `avatar-display-fix-summary.md` - 头像显示修复总结
- `avatar-url-debug.html` - 头像URL调试工具
- `avatar-url-duplicate-fix-summary.md` - 头像URL重复路径修复总结

### 👤 profile/ - 个人资料测试
包含用户个人信息管理、资料编辑等相关的测试文件。

**文件列表：**
- `profile-api-fix-summary.md` - 个人资料API修复总结
- `profile-layout-optimization-summary.md` - 个人资料布局优化总结
- `profile-module-test-guide.md` - 个人资料模块测试指南
- `test-profile-api.html` - 个人资料API测试工具

### 🔌 api/ - API接口测试
包含各种API接口的测试工具和验证文件。

**文件列表：**
- `test-api-time.html` - API时间测试工具
- `test-favorite-api.html` - 收藏功能API测试工具

### ⚡ features/ - 功能特性测试
包含各种业务功能的测试文件和修复总结。

**文件列表：**
- `batch-check-fix-summary.md` - 批量检查修复总结
- `test-favorite-function.md` - 收藏功能测试文档
- `test-renew-function.md` - 续借功能测试文档
- `test-time-display.md` - 时间显示测试文档
- `test-batch-check-fix.html` - 批量检查修复测试工具
- `test-renew-verification.html` - 续借验证测试工具

## 使用说明

### 📋 测试文档类型

#### **修复总结文档 (.md)**
- 记录问题的发现、分析、修复过程
- 包含技术细节和解决方案
- 用于问题回顾和经验总结

#### **测试工具 (.html)**
- 独立的HTML测试页面
- 可直接在浏览器中打开使用
- 包含交互式测试功能

#### **测试指南 (.md)**
- 详细的测试步骤和方法
- 测试用例和预期结果
- 测试环境配置说明

### 🚀 快速开始

#### **运行HTML测试工具**
```bash
# 在浏览器中直接打开HTML文件
# 例如：
open frontend/tests/auth/auth-debug-tool.html
```

#### **查看修复总结**
```bash
# 使用Markdown阅读器查看
# 例如：
cat frontend/tests/auth/auth-401-error-fix-summary.md
```

### 🔧 测试环境要求

#### **基础要求**
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 本地开发服务器运行在 `http://localhost:3001`
- 后端API服务运行在 `http://localhost:8080`

#### **认证测试**
- 需要有效的用户账号
- 确保JWT token配置正确
- 检查localStorage访问权限

#### **API测试**
- 确保后端服务正常运行
- 检查CORS配置
- 验证API端点可访问

## 维护指南

### 📝 添加新测试文件

#### **选择合适的目录**
根据测试内容选择对应的功能目录：
- 认证相关 → `auth/`
- 头像功能 → `avatar/`
- 个人资料 → `profile/`
- API接口 → `api/`
- 业务功能 → `features/`

#### **文件命名规范**
- **修复总结**：`[功能名]-fix-summary.md`
- **测试工具**：`test-[功能名].html`
- **调试工具**：`[功能名]-debug.html`
- **测试指南**：`[功能名]-test-guide.md`

#### **更新说明文档**
添加新文件后，请更新对应目录的文件列表。

### 🧹 清理过期文件

#### **定期检查**
- 检查测试文件是否仍然有效
- 移除已过期的临时测试文件
- 更新文档中的文件列表

#### **归档策略**
- 重要的修复总结文档应长期保留
- 临时调试工具可在问题解决后移除
- 测试指南应保持最新状态

## 相关链接

- [前端项目主目录](../)
- [源代码目录](../src/)
- [项目文档](../README.md)
- [后端测试目录](../../book-management-backend/tests/)

## 联系方式

如有问题或建议，请联系开发团队。

---

**最后更新时间：** 2025年6月10日  
**维护者：** 前端开发团队
