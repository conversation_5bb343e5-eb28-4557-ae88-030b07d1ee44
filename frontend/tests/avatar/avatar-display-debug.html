<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>头像显示问题诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .section-title {
            color: #1976d2;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #1976d2;
            padding-bottom: 5px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border-left: 4px solid #1976d2;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border-left-color: #f44336;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left-color: #4caf50;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
            border-left-color: #ff9800;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border-left-color: #2196f3;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #1565c0;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .avatar-test {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }
        .avatar-img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #1976d2;
        }
        .avatar-info {
            flex: 1;
        }
        .avatar-url {
            font-size: 12px;
            color: #666;
            word-break: break-all;
            margin-top: 5px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #4caf50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        .status-info { background-color: #2196f3; }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>🖼️ 头像显示问题诊断</h1>
    <p>此工具用于诊断和测试用户头像显示功能，包括后端静态资源配置和前端URL构建逻辑。</p>

    <!-- 问题概述 -->
    <div class="container">
        <div class="section-title">📋 问题概述</div>
        <div class="result info">
头像显示问题分析:

1. 后端静态资源配置:
   - Spring Security需要允许/api/files/**和/uploads/**路径访问
   - WebMvcConfigurer需要配置静态资源映射
   - FileController需要正确处理文件访问

2. 前端URL构建:
   - 需要正确拼接完整的图片访问URL
   - 处理相对路径和绝对路径
   - 提供默认头像fallback机制

3. 跨域和权限:
   - 静态资源访问不需要认证
   - 确保CORS配置正确
   - 文件访问权限设置

已实施的修复:
✅ 添加Spring Security静态资源路径配置
✅ 创建WebMvcConfigurer静态资源映射
✅ 优化FileController文件访问逻辑
✅ 创建UserAvatar组件统一头像显示
✅ 添加默认头像fallback机制
        </div>
    </div>

    <div class="grid">
        <!-- 后端配置检查 -->
        <div class="container">
            <div class="section-title">🔧 后端配置检查</div>
            <button onclick="checkBackendConfig()">检查后端配置</button>
            <button onclick="testFileAccess()">测试文件访问</button>
            <div id="backend-config" class="result"></div>
        </div>

        <!-- 头像URL测试 -->
        <div class="container">
            <div class="section-title">🔗 头像URL测试</div>
            <input type="text" id="avatar-filename" placeholder="输入头像文件名 (如: avatar.jpg)" value="">
            <button onclick="testAvatarUrl()">测试头像URL</button>
            <button onclick="testDefaultAvatar()">测试默认头像</button>
            <div id="avatar-url-test" class="result"></div>
        </div>

        <!-- 用户头像检查 -->
        <div class="container">
            <div class="section-title">👤 用户头像检查</div>
            <button onclick="checkUserAvatar()">检查当前用户头像</button>
            <button onclick="testProfileApi()">测试个人信息API</button>
            <div id="user-avatar" class="result"></div>
        </div>

        <!-- 静态资源测试 -->
        <div class="container">
            <div class="section-title">📁 静态资源测试</div>
            <button onclick="testStaticResource()">测试静态资源访问</button>
            <button onclick="testCorsConfig()">测试CORS配置</button>
            <div id="static-resource" class="result"></div>
        </div>
    </div>

    <!-- 头像显示测试 -->
    <div class="container">
        <div class="section-title">🖼️ 头像显示测试</div>
        <div id="avatar-display-tests"></div>
        <button onclick="runAvatarDisplayTests()">运行头像显示测试</button>
    </div>

    <!-- 修复验证 -->
    <div class="container">
        <div class="section-title">✅ 修复验证</div>
        <div class="result info">
修复验证清单:

1. 后端配置:
   □ Spring Security允许静态资源访问
   □ WebMvcConfigurer配置静态资源映射
   □ FileController正确处理文件访问
   □ uploads目录存在且可访问

2. 前端配置:
   □ UserAvatar组件正确构建URL
   □ 默认头像fallback机制工作
   □ 头像加载错误处理正常
   □ 各页面正确使用UserAvatar组件

3. 功能测试:
   □ 头像文件可以通过URL访问
   □ 前端能正确显示用户头像
   □ 头像加载失败时显示默认头像
   □ 头像上传和显示功能完整
        </div>
    </div>

    <script>
        // 检查后端配置
        async function checkBackendConfig() {
            showResult('backend-config', '正在检查后端配置...', 'info');
            
            let result = '后端配置检查结果:\n\n';
            
            try {
                // 1. 检查静态资源路径访问
                result += '1. 静态资源路径检查:\n';
                
                const staticTests = [
                    { path: '/uploads/', desc: '静态资源映射' },
                    { path: '/api/files/avatars/', desc: 'FileController路径' }
                ];
                
                for (const test of staticTests) {
                    try {
                        const response = await fetch(`http://localhost:8080${test.path}test.jpg`);
                        if (response.status === 404) {
                            result += `   ✅ ${test.desc}: 路径可访问 (404正常，文件不存在)\n`;
                        } else if (response.status === 403) {
                            result += `   ❌ ${test.desc}: 访问被拒绝 (403错误)\n`;
                        } else {
                            result += `   ✅ ${test.desc}: 路径可访问 (状态码: ${response.status})\n`;
                        }
                    } catch (error) {
                        result += `   ❌ ${test.desc}: 连接失败 - ${error.message}\n`;
                    }
                }
                
                result += '\n2. 服务器连接检查:\n';
                try {
                    const response = await fetch('http://localhost:8080/api/auth/login', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ username: 'test', password: 'test' })
                    });
                    result += `   ✅ 后端服务正常运行 (状态码: ${response.status})\n`;
                } catch (error) {
                    result += `   ❌ 后端服务连接失败: ${error.message}\n`;
                }
                
                showResult('backend-config', result, 'success');
            } catch (error) {
                result += `\n❌ 检查过程中发生错误: ${error.message}`;
                showResult('backend-config', result, 'error');
            }
        }

        // 测试文件访问
        async function testFileAccess() {
            showResult('backend-config', '正在测试文件访问...', 'info');
            
            let result = '文件访问测试:\n\n';
            
            // 测试不同的文件访问路径
            const testPaths = [
                'http://localhost:8080/uploads/avatars/test.jpg',
                'http://localhost:8080/api/files/avatars/test.jpg'
            ];
            
            for (const path of testPaths) {
                try {
                    const response = await fetch(path);
                    result += `路径: ${path}\n`;
                    result += `状态码: ${response.status}\n`;
                    result += `状态文本: ${response.statusText}\n`;
                    
                    if (response.status === 404) {
                        result += `结果: ✅ 路径可访问 (文件不存在是正常的)\n\n`;
                    } else if (response.status === 403) {
                        result += `结果: ❌ 访问被拒绝，需要检查权限配置\n\n`;
                    } else if (response.status === 200) {
                        result += `结果: ✅ 文件访问成功\n\n`;
                    } else {
                        result += `结果: ⚠️ 未知状态码\n\n`;
                    }
                } catch (error) {
                    result += `路径: ${path}\n`;
                    result += `错误: ${error.message}\n`;
                    result += `结果: ❌ 连接失败\n\n`;
                }
            }
            
            showResult('backend-config', result, 'info');
        }

        // 测试头像URL
        function testAvatarUrl() {
            const filename = document.getElementById('avatar-filename').value.trim();
            
            if (!filename) {
                showResult('avatar-url-test', '请输入头像文件名', 'warning');
                return;
            }
            
            let result = `头像URL构建测试:\n\n`;
            result += `输入文件名: ${filename}\n\n`;
            
            // 构建不同的URL
            const baseUrl = 'http://localhost:8080';
            const urls = [
                `${baseUrl}/uploads/avatars/${filename}`,
                `${baseUrl}/api/files/avatars/${filename}`
            ];
            
            result += '构建的URL:\n';
            urls.forEach((url, index) => {
                result += `${index + 1}. ${url}\n`;
            });
            
            result += '\n点击下方按钮测试这些URL的可访问性';
            
            showResult('avatar-url-test', result, 'info');
            
            // 测试URL可访问性
            testUrlAccessibility(urls);
        }

        // 测试URL可访问性
        async function testUrlAccessibility(urls) {
            let result = '头像URL可访问性测试:\n\n';
            
            for (let i = 0; i < urls.length; i++) {
                const url = urls[i];
                try {
                    const response = await fetch(url);
                    result += `URL ${i + 1}: ${response.status === 404 ? '✅ 可访问' : response.status === 200 ? '✅ 文件存在' : '❌ 错误'} (${response.status})\n`;
                } catch (error) {
                    result += `URL ${i + 1}: ❌ 连接失败 - ${error.message}\n`;
                }
            }
            
            setTimeout(() => {
                showResult('avatar-url-test', result, 'info');
            }, 1000);
        }

        // 测试默认头像
        function testDefaultAvatar() {
            const name = 'Test User';
            const defaultUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&size=200&background=1976d2&color=ffffff&bold=true&format=png`;
            
            let result = `默认头像测试:\n\n`;
            result += `用户名: ${name}\n`;
            result += `默认头像URL: ${defaultUrl}\n\n`;
            
            // 创建图片测试
            const img = new Image();
            img.onload = () => {
                result += '✅ 默认头像加载成功\n';
                result += `图片尺寸: ${img.width}x${img.height}`;
                showResult('avatar-url-test', result, 'success');
            };
            img.onerror = () => {
                result += '❌ 默认头像加载失败';
                showResult('avatar-url-test', result, 'error');
            };
            img.src = defaultUrl;
            
            showResult('avatar-url-test', result + '正在测试默认头像加载...', 'info');
        }

        // 检查当前用户头像
        async function checkUserAvatar() {
            showResult('user-avatar', '正在检查当前用户头像...', 'info');
            
            const token = localStorage.getItem('token');
            if (!token) {
                showResult('user-avatar', '❌ 用户未登录，无法检查头像', 'error');
                return;
            }
            
            try {
                const response = await fetch('http://localhost:8080/api/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    let result = '✅ 用户信息获取成功\n\n';
                    result += `用户名: ${data.data.username}\n`;
                    result += `真实姓名: ${data.data.realName || '未设置'}\n`;
                    result += `头像字段: ${data.data.avatar || '未设置'}\n\n`;
                    
                    if (data.data.avatar) {
                        result += '头像URL构建:\n';
                        const baseUrl = 'http://localhost:8080';
                        const avatar = data.data.avatar;
                        
                        let avatarUrl;
                        if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
                            avatarUrl = avatar;
                        } else if (avatar.startsWith('/')) {
                            avatarUrl = `${baseUrl}${avatar}`;
                        } else {
                            avatarUrl = `${baseUrl}/uploads/avatars/${avatar}`;
                        }
                        
                        result += `完整URL: ${avatarUrl}\n`;
                        
                        // 测试头像URL
                        try {
                            const avatarResponse = await fetch(avatarUrl);
                            result += `头像访问: ${avatarResponse.status === 200 ? '✅ 成功' : avatarResponse.status === 404 ? '❌ 文件不存在' : '❌ 访问失败'} (${avatarResponse.status})`;
                        } catch (error) {
                            result += `头像访问: ❌ 连接失败 - ${error.message}`;
                        }
                    } else {
                        result += '⚠️ 用户未设置头像，将使用默认头像';
                    }
                    
                    showResult('user-avatar', result, 'success');
                } else {
                    showResult('user-avatar', `❌ 获取用户信息失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('user-avatar', `❌ 请求失败: ${error.message}`, 'error');
            }
        }

        // 测试个人信息API
        async function testProfileApi() {
            const token = localStorage.getItem('token');
            if (!token) {
                showResult('user-avatar', '❌ 用户未登录，无法测试API', 'error');
                return;
            }
            
            showResult('user-avatar', '正在测试个人信息API...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/api/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                let result = `个人信息API测试:\n\n`;
                result += `状态码: ${response.status}\n`;
                result += `响应: ${JSON.stringify(data, null, 2)}`;
                
                showResult('user-avatar', result, response.ok ? 'success' : 'error');
            } catch (error) {
                showResult('user-avatar', `❌ API测试失败: ${error.message}`, 'error');
            }
        }

        // 测试静态资源
        async function testStaticResource() {
            showResult('static-resource', '正在测试静态资源访问...', 'info');
            
            let result = '静态资源访问测试:\n\n';
            
            const testUrls = [
                'http://localhost:8080/uploads/',
                'http://localhost:8080/api/files/',
                'http://localhost:8080/uploads/avatars/',
                'http://localhost:8080/api/files/avatars/'
            ];
            
            for (const url of testUrls) {
                try {
                    const response = await fetch(url);
                    result += `${url}: ${response.status} ${response.statusText}\n`;
                } catch (error) {
                    result += `${url}: 连接失败 - ${error.message}\n`;
                }
            }
            
            showResult('static-resource', result, 'info');
        }

        // 测试CORS配置
        async function testCorsConfig() {
            showResult('static-resource', '正在测试CORS配置...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/api/files/avatars/test.jpg', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                let result = 'CORS配置测试:\n\n';
                result += `状态码: ${response.status}\n`;
                result += `CORS头信息:\n`;
                
                const corsHeaders = [
                    'Access-Control-Allow-Origin',
                    'Access-Control-Allow-Methods',
                    'Access-Control-Allow-Headers'
                ];
                
                corsHeaders.forEach(header => {
                    const value = response.headers.get(header);
                    result += `  ${header}: ${value || '未设置'}\n`;
                });
                
                result += '\n✅ CORS配置正常';
                
                showResult('static-resource', result, 'success');
            } catch (error) {
                showResult('static-resource', `❌ CORS测试失败: ${error.message}`, 'error');
            }
        }

        // 运行头像显示测试
        function runAvatarDisplayTests() {
            const testContainer = document.getElementById('avatar-display-tests');
            testContainer.innerHTML = '';
            
            const testCases = [
                {
                    name: '默认头像 (Test User)',
                    url: 'https://ui-avatars.com/api/?name=Test%20User&size=200&background=1976d2&color=ffffff&bold=true&format=png'
                },
                {
                    name: '静态资源路径测试',
                    url: 'http://localhost:8080/uploads/avatars/test.jpg'
                },
                {
                    name: 'FileController路径测试',
                    url: 'http://localhost:8080/api/files/avatars/test.jpg'
                }
            ];
            
            testCases.forEach(testCase => {
                const testDiv = document.createElement('div');
                testDiv.className = 'avatar-test';
                
                const img = document.createElement('img');
                img.className = 'avatar-img';
                img.src = testCase.url;
                img.alt = testCase.name;
                
                const info = document.createElement('div');
                info.className = 'avatar-info';
                info.innerHTML = `
                    <strong>${testCase.name}</strong>
                    <div class="avatar-url">${testCase.url}</div>
                `;
                
                const status = document.createElement('span');
                status.innerHTML = '<span class="status-indicator status-info"></span>加载中...';
                
                img.onload = () => {
                    status.innerHTML = '<span class="status-indicator status-success"></span>加载成功';
                };
                
                img.onerror = () => {
                    status.innerHTML = '<span class="status-indicator status-error"></span>加载失败';
                };
                
                testDiv.appendChild(img);
                testDiv.appendChild(info);
                testDiv.appendChild(status);
                testContainer.appendChild(testDiv);
            });
        }

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 页面加载时运行基础检查
        window.onload = function() {
            checkBackendConfig();
        };
    </script>
</body>
</html>
