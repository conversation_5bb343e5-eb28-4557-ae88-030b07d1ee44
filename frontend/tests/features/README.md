# 功能特性测试文件

本目录包含各种业务功能的测试文件和修复总结。

## 文件说明

### 修复总结文档
- `batch-check-fix-summary.md` - 批量检查修复总结

### 测试指南
- `test-favorite-function.md` - 收藏功能测试文档
- `test-renew-function.md` - 续借功能测试文档
- `test-time-display.md` - 时间显示测试文档

### 测试工具
- `test-batch-check-fix.html` - 批量检查修复测试工具
- `test-renew-verification.html` - 续借验证测试工具

## 使用方法

### 运行测试工具
在浏览器中直接打开HTML文件即可使用相应的测试工具。

### 查看修复总结
使用Markdown阅读器查看修复总结文档，了解功能问题的解决过程。

### 参考测试指南
按照测试指南进行系统性的功能测试。

## 测试环境要求
- 确保后端服务运行在 `http://localhost:8080`
- 需要登录用户账号
- 确保相关业务功能API正常工作
- 对于图书相关功能，需要有测试数据
