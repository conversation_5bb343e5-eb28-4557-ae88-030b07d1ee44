<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>续借功能修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            color: #1976d2;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
        .test-data {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .test-data h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .time-calc {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        .time-item {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }
        .time-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }
        .time-value {
            font-family: monospace;
            font-size: 14px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass {
            background-color: #4caf50;
        }
        .status-fail {
            background-color: #f44336;
        }
        .status-warning {
            background-color: #ff9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>续借功能修复验证测试</h1>
        <p>此页面用于验证借阅记录续借功能的时间计算修复效果。</p>

        <div class="test-data">
            <h4>测试数据</h4>
            <p><strong>借阅日期：</strong>2025年6月9日</p>
            <p><strong>应还日期：</strong>2025年6月11日</p>
            <p><strong>当前日期：</strong><span id="current-date"></span></p>
            <p><strong>预期结果：</strong>应该显示"可以续借"（距离到期2天，在5天内）</p>
        </div>

        <div class="test-section">
            <div class="test-title">1. 前端时间计算测试</div>
            <button onclick="testFrontendTimeCalculation()">测试前端时间计算</button>
            <div id="frontend-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 续借条件检查测试</div>
            <button onclick="testRenewConditions()">测试续借条件</button>
            <div id="conditions-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 边界条件测试</div>
            <button onclick="testBoundaryConditions()">测试边界条件</button>
            <div id="boundary-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 时间格式兼容性测试</div>
            <button onclick="testTimeFormatCompatibility()">测试时间格式</button>
            <div id="format-result" class="result"></div>
        </div>
    </div>

    <script>
        // 模拟修复后的前端时间处理函数
        function parseDateTime(dateString) {
            if (!dateString) return null;
            
            if (dateString.includes('T')) {
                return new Date(dateString);
            } else if (dateString.includes(' ')) {
                return new Date(dateString.replace(' ', 'T'));
            } else {
                return new Date(dateString);
            }
        }

        function calculateDaysDifference(dueDateStr, currentDateStr = null) {
            const dueDate = parseDateTime(dueDateStr);
            const currentDate = currentDateStr ? parseDateTime(currentDateStr) : new Date();
            
            if (!dueDate || !currentDate) return null;
            
            // 只比较日期部分
            const dueDateOnly = new Date(dueDate.getFullYear(), dueDate.getMonth(), dueDate.getDate());
            const currentDateOnly = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
            
            const diffDays = Math.ceil((dueDateOnly.getTime() - currentDateOnly.getTime()) / (1000 * 60 * 60 * 24));
            
            return {
                diffDays,
                dueDate: dueDateOnly,
                currentDate: currentDateOnly,
                canRenew: diffDays <= 5 && diffDays >= 0
            };
        }

        function testFrontendTimeCalculation() {
            const resultDiv = document.getElementById('frontend-result');
            
            // 测试数据
            const testCases = [
                {
                    name: '主要测试场景',
                    borrowDate: '2025-06-09 10:00:00',
                    dueDate: '2025-06-11 23:59:59',
                    currentDate: '2025-06-09 22:00:00',
                    expected: true
                },
                {
                    name: '当天到期',
                    borrowDate: '2025-06-09 10:00:00',
                    dueDate: '2025-06-09 23:59:59',
                    currentDate: '2025-06-09 22:00:00',
                    expected: true
                },
                {
                    name: '正好5天',
                    borrowDate: '2025-06-01 10:00:00',
                    dueDate: '2025-06-11 23:59:59',
                    currentDate: '2025-06-06 22:00:00',
                    expected: true
                },
                {
                    name: '超过5天',
                    borrowDate: '2025-06-01 10:00:00',
                    dueDate: '2025-06-11 23:59:59',
                    currentDate: '2025-06-05 22:00:00',
                    expected: false
                },
                {
                    name: '已逾期',
                    borrowDate: '2025-06-01 10:00:00',
                    dueDate: '2025-06-09 23:59:59',
                    currentDate: '2025-06-11 22:00:00',
                    expected: false
                }
            ];
            
            let result = '前端时间计算测试结果:\n\n';
            let allPassed = true;
            
            testCases.forEach((testCase, index) => {
                const calc = calculateDaysDifference(testCase.dueDate, testCase.currentDate);
                const passed = calc.canRenew === testCase.expected;
                allPassed = allPassed && passed;
                
                result += `${passed ? '✅' : '❌'} 测试 ${index + 1}: ${testCase.name}\n`;
                result += `  应还日期: ${testCase.dueDate}\n`;
                result += `  当前日期: ${testCase.currentDate}\n`;
                result += `  计算天数: ${calc.diffDays}天\n`;
                result += `  可续借: ${calc.canRenew} (预期: ${testCase.expected})\n`;
                result += `  结果: ${passed ? '通过' : '失败'}\n\n`;
            });
            
            result += `总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`;
            
            resultDiv.textContent = result;
            resultDiv.className = `result ${allPassed ? 'success' : 'error'}`;
        }

        function testRenewConditions() {
            const resultDiv = document.getElementById('conditions-result');
            
            // 模拟借阅记录数据
            const borrowRecord = {
                borrowTime: '2025-06-09 10:00:00',
                dueTime: '2025-06-11 23:59:59',
                renewCount: 0,
                status: 'BORROWED'
            };
            
            const currentTime = new Date('2025-06-09T22:00:00');
            
            let result = '续借条件检查结果:\n\n';
            
            // 1. 时间窗口检查
            const timeCalc = calculateDaysDifference(borrowRecord.dueTime, currentTime.toISOString());
            const withinTimeWindow = timeCalc.canRenew;
            result += `${withinTimeWindow ? '✅' : '❌'} 距离到期日期在5天内: ${timeCalc.diffDays}天 (${withinTimeWindow ? '通过' : '失败'})\n`;
            
            // 2. 续借次数检查
            const withinRenewLimit = (borrowRecord.renewCount || 0) < 2;
            result += `${withinRenewLimit ? '✅' : '❌'} 续借次数未超过限制: ${borrowRecord.renewCount}/2 (${withinRenewLimit ? '通过' : '失败'})\n`;
            
            // 3. 总借阅时间检查
            const borrowDate = parseDateTime(borrowRecord.borrowTime);
            const diffMonths = (currentTime.getFullYear() - borrowDate.getFullYear()) * 12 + 
                              (currentTime.getMonth() - borrowDate.getMonth());
            const withinDurationLimit = diffMonths < 3;
            result += `${withinDurationLimit ? '✅' : '❌'} 总借阅时间未超过3个月: ${diffMonths}个月 (${withinDurationLimit ? '通过' : '失败'})\n`;
            
            // 4. 图书状态检查
            const isValidStatus = borrowRecord.status === 'BORROWED';
            result += `${isValidStatus ? '✅' : '❌'} 图书状态正确: ${borrowRecord.status} (${isValidStatus ? '通过' : '失败'})\n`;
            
            // 5. 逾期检查
            const isNotOverdue = timeCalc.diffDays >= 0;
            result += `${isNotOverdue ? '✅' : '❌'} 图书未逾期: ${isNotOverdue ? '未逾期' : '已逾期'} (${isNotOverdue ? '通过' : '失败'})\n`;
            
            const allConditionsPassed = withinTimeWindow && withinRenewLimit && withinDurationLimit && isValidStatus && isNotOverdue;
            
            result += `\n总体结果: ${allConditionsPassed ? '✅ 可以续借' : '❌ 无法续借'}`;
            
            resultDiv.textContent = result;
            resultDiv.className = `result ${allConditionsPassed ? 'success' : 'error'}`;
        }

        function testBoundaryConditions() {
            const resultDiv = document.getElementById('boundary-result');
            
            const boundaryTests = [
                { name: '正好5天边界', currentDate: '2025-06-06', dueDate: '2025-06-11', expected: true },
                { name: '超过5天边界', currentDate: '2025-06-05', dueDate: '2025-06-11', expected: false },
                { name: '当天到期边界', currentDate: '2025-06-11', dueDate: '2025-06-11', expected: true },
                { name: '逾期1天边界', currentDate: '2025-06-12', dueDate: '2025-06-11', expected: false },
                { name: '提前1天边界', currentDate: '2025-06-10', dueDate: '2025-06-11', expected: true }
            ];
            
            let result = '边界条件测试结果:\n\n';
            let allPassed = true;
            
            boundaryTests.forEach((test, index) => {
                const calc = calculateDaysDifference(`${test.dueDate} 23:59:59`, `${test.currentDate} 12:00:00`);
                const passed = calc.canRenew === test.expected;
                allPassed = allPassed && passed;
                
                result += `${passed ? '✅' : '❌'} ${test.name}\n`;
                result += `  当前: ${test.currentDate}, 到期: ${test.dueDate}\n`;
                result += `  天数差: ${calc.diffDays}天, 可续借: ${calc.canRenew}\n`;
                result += `  预期: ${test.expected}, 结果: ${passed ? '通过' : '失败'}\n\n`;
            });
            
            result += `边界测试总结: ${allPassed ? '✅ 所有边界条件正确' : '❌ 部分边界条件失败'}`;
            
            resultDiv.textContent = result;
            resultDiv.className = `result ${allPassed ? 'success' : 'error'}`;
        }

        function testTimeFormatCompatibility() {
            const resultDiv = document.getElementById('format-result');
            
            const formatTests = [
                { name: 'ISO格式', dateStr: '2025-06-11T23:59:59' },
                { name: '后端格式', dateStr: '2025-06-11 23:59:59' },
                { name: '日期格式', dateStr: '2025-06-11' },
                { name: 'ISO带时区', dateStr: '2025-06-11T23:59:59Z' }
            ];
            
            let result = '时间格式兼容性测试结果:\n\n';
            let allPassed = true;
            
            formatTests.forEach((test, index) => {
                try {
                    const parsed = parseDateTime(test.dateStr);
                    const isValid = parsed && !isNaN(parsed.getTime());
                    allPassed = allPassed && isValid;
                    
                    result += `${isValid ? '✅' : '❌'} ${test.name}\n`;
                    result += `  输入: ${test.dateStr}\n`;
                    result += `  解析: ${isValid ? parsed.toLocaleString('zh-CN') : '解析失败'}\n`;
                    result += `  状态: ${isValid ? '成功' : '失败'}\n\n`;
                } catch (error) {
                    allPassed = false;
                    result += `❌ ${test.name}\n`;
                    result += `  输入: ${test.dateStr}\n`;
                    result += `  错误: ${error.message}\n\n`;
                }
            });
            
            result += `格式兼容性总结: ${allPassed ? '✅ 所有格式支持正常' : '❌ 部分格式不支持'}`;
            
            resultDiv.textContent = result;
            resultDiv.className = `result ${allPassed ? 'success' : 'error'}`;
        }

        // 页面加载时显示当前日期
        window.onload = function() {
            document.getElementById('current-date').textContent = new Date().toLocaleString('zh-CN');
        };
    </script>
</body>
</html>
