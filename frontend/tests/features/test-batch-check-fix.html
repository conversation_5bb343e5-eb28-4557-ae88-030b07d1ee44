<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量检查收藏状态修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        .auth-section {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>批量检查收藏状态修复测试</h1>
        <p>此页面用于测试修复后的批量检查收藏状态API。</p>

        <!-- 认证状态 -->
        <div class="auth-section">
            <h3>认证状态</h3>
            <div id="auth-status">未登录</div>
            <div style="margin-top: 10px;">
                <input type="text" id="username" placeholder="用户名" value="admin">
                <input type="password" id="password" placeholder="密码" value="123456">
                <button onclick="login()">登录</button>
            </div>
        </div>

        <!-- 修复前后对比测试 -->
        <div class="test-section">
            <h3>修复前后对比测试</h3>
            <p><strong>问题描述：</strong>前端发送 <code>{ bookIds: [1,2,3] }</code> 对象，但后端期望直接接收 <code>[1,2,3]</code> 数组。</p>
            
            <div style="margin: 15px 0;">
                <input type="text" id="bookIds" placeholder="图书ID列表(逗号分隔)" value="1,2,3">
            </div>
            
            <button onclick="testOldWay()">测试修复前的方式 (发送对象)</button>
            <button onclick="testNewWay()">测试修复后的方式 (发送数组)</button>
            
            <div id="test-result" class="result"></div>
        </div>

        <!-- 实际API测试 -->
        <div class="test-section">
            <h3>实际前端API测试</h3>
            <p>测试前端修复后的API调用是否正常工作。</p>
            
            <button onclick="testFrontendAPI()">测试前端API调用</button>
            
            <div id="frontend-result" class="result"></div>
        </div>
    </div>

    <script>
        let authToken = localStorage.getItem('authToken') || '';
        
        // 更新认证状态
        function updateAuthStatus() {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.textContent = authToken ? '已登录' : '未登录';
        }

        // 登录
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    authToken = data.data.token;
                    localStorage.setItem('authToken', authToken);
                    updateAuthStatus();
                    showResult('auth-status', '✅ 登录成功', 'success');
                } else {
                    showResult('auth-status', `❌ 登录失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('auth-status', `❌ 登录请求失败: ${error.message}`, 'error');
            }
        }

        // 测试修复前的方式 (发送对象)
        async function testOldWay() {
            if (!authToken) {
                showResult('test-result', '❌ 请先登录', 'error');
                return;
            }
            
            const bookIdsStr = document.getElementById('bookIds').value;
            const bookIds = bookIdsStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            
            try {
                showResult('test-result', '正在测试修复前的方式 (发送对象)...', 'success');
                
                const response = await fetch('http://localhost:8080/api/user/favorites/batch-check', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ bookIds }) // 发送对象 (错误方式)
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    showResult('test-result', '❌ 意外成功！这不应该发生。\n' + JSON.stringify(data, null, 2), 'error');
                } else {
                    showResult('test-result', `✅ 预期的错误 (修复前):\n错误信息: ${data.message}\n这证明了问题的存在`, 'success');
                }
            } catch (error) {
                showResult('test-result', `✅ 预期的网络错误 (修复前):\n${error.message}\n这证明了问题的存在`, 'success');
            }
        }

        // 测试修复后的方式 (发送数组)
        async function testNewWay() {
            if (!authToken) {
                showResult('test-result', '❌ 请先登录', 'error');
                return;
            }
            
            const bookIdsStr = document.getElementById('bookIds').value;
            const bookIds = bookIdsStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            
            try {
                showResult('test-result', '正在测试修复后的方式 (发送数组)...', 'success');
                
                const response = await fetch('http://localhost:8080/api/user/favorites/batch-check', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(bookIds) // 直接发送数组 (正确方式)
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    let result = '✅ 修复成功！API正常工作\n\n';
                    result += '返回数据:\n';
                    Object.entries(data.data).forEach(([bookId, isFavorited]) => {
                        result += `图书ID ${bookId}: ${isFavorited ? '已收藏' : '未收藏'}\n`;
                    });
                    showResult('test-result', result, 'success');
                } else {
                    showResult('test-result', `❌ API调用失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('test-result', `❌ 请求失败: ${error.message}`, 'error');
            }
        }

        // 测试前端API调用
        async function testFrontendAPI() {
            if (!authToken) {
                showResult('frontend-result', '❌ 请先登录', 'error');
                return;
            }
            
            const bookIdsStr = document.getElementById('bookIds').value;
            const bookIds = bookIdsStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            
            try {
                showResult('frontend-result', '正在测试前端修复后的API调用...', 'success');
                
                // 模拟前端API调用
                const response = await fetch('http://localhost:3001/api/user/favorites/batch-check', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(bookIds)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    let result = '✅ 前端API代理正常工作\n\n';
                    result += JSON.stringify(data, null, 2);
                    showResult('frontend-result', result, 'success');
                } else {
                    // 前端可能没有代理，直接测试后端
                    showResult('frontend-result', '前端代理未配置，直接测试后端API...', 'success');
                    await testNewWay();
                }
            } catch (error) {
                // 前端代理失败，直接测试后端
                showResult('frontend-result', '前端代理连接失败，直接测试后端API...', 'success');
                await testNewWay();
            }
        }

        // 显示结果
        function showResult(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 页面加载时更新认证状态
        window.onload = function() {
            updateAuthStatus();
        };
    </script>
</body>
</html>
