<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人信息API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        .auth-section {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>个人信息API测试</h1>
        <p>此页面用于测试个人信息相关的后端API接口。</p>

        <!-- 认证状态 -->
        <div class="auth-section">
            <h3>认证状态</h3>
            <div id="auth-status">未登录</div>
            <div style="margin-top: 10px;">
                <input type="text" id="username" placeholder="用户名" value="admin">
                <input type="password" id="password" placeholder="密码" value="123456">
                <button onclick="login()">登录</button>
            </div>
        </div>

        <!-- 获取个人信息测试 -->
        <div class="test-section">
            <h3>1. 获取个人信息</h3>
            <button onclick="getProfile()">获取个人信息</button>
            <div id="profile-result" class="result"></div>
        </div>

        <!-- 更新个人信息测试 -->
        <div class="test-section">
            <h3>2. 更新个人信息</h3>
            <div>
                <input type="text" id="realName" placeholder="真实姓名" value="测试用户">
                <input type="email" id="email" placeholder="邮箱" value="<EMAIL>">
                <input type="text" id="phone" placeholder="手机号" value="13800138000">
            </div>
            <button onclick="updateProfile()">更新个人信息</button>
            <div id="update-result" class="result"></div>
        </div>

        <!-- 获取统计信息测试 -->
        <div class="test-section">
            <h3>3. 获取统计信息</h3>
            <button onclick="getStats()">获取统计信息</button>
            <div id="stats-result" class="result"></div>
        </div>
    </div>

    <script>
        let authToken = localStorage.getItem('authToken') || '';
        
        // 更新认证状态
        function updateAuthStatus() {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.textContent = authToken ? '已登录' : '未登录';
        }

        // 登录
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    authToken = data.data.token;
                    localStorage.setItem('authToken', authToken);
                    updateAuthStatus();
                    showResult('auth-status', '✅ 登录成功', 'success');
                } else {
                    showResult('auth-status', `❌ 登录失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('auth-status', `❌ 登录请求失败: ${error.message}`, 'error');
            }
        }

        // 获取个人信息
        async function getProfile() {
            if (!authToken) {
                showResult('profile-result', '❌ 请先登录', 'error');
                return;
            }
            
            try {
                showResult('profile-result', '正在获取个人信息...', 'success');
                
                const response = await fetch('http://localhost:8080/api/profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    let result = '✅ 获取个人信息成功\n\n';
                    result += `用户ID: ${data.data.id}\n`;
                    result += `用户名: ${data.data.username}\n`;
                    result += `真实姓名: ${data.data.realName || '未设置'}\n`;
                    result += `邮箱: ${data.data.email}\n`;
                    result += `手机号: ${data.data.phone || '未设置'}\n`;
                    result += `角色: ${data.data.role}\n`;
                    result += `状态: ${data.data.status}\n`;
                    result += `注册时间: ${data.data.createdTime}\n`;
                    
                    if (data.data.borrowStats) {
                        result += '\n统计信息:\n';
                        result += `总借阅: ${data.data.borrowStats.totalBorrows}\n`;
                        result += `当前借阅: ${data.data.borrowStats.activeBorrows}\n`;
                        result += `逾期图书: ${data.data.borrowStats.overdueBorrows}\n`;
                        result += `收藏图书: ${data.data.borrowStats.totalFavorites}\n`;
                    }
                    
                    showResult('profile-result', result, 'success');
                } else {
                    showResult('profile-result', `❌ 获取个人信息失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('profile-result', `❌ 请求失败: ${error.message}`, 'error');
            }
        }

        // 更新个人信息
        async function updateProfile() {
            if (!authToken) {
                showResult('update-result', '❌ 请先登录', 'error');
                return;
            }
            
            const realName = document.getElementById('realName').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            
            if (!realName || !email) {
                showResult('update-result', '❌ 真实姓名和邮箱不能为空', 'error');
                return;
            }
            
            try {
                showResult('update-result', '正在更新个人信息...', 'success');
                
                const response = await fetch('http://localhost:8080/api/profile', {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        realName,
                        email,
                        phone: phone || null
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    let result = '✅ 更新个人信息成功\n\n';
                    result += `真实姓名: ${data.data.realName}\n`;
                    result += `邮箱: ${data.data.email}\n`;
                    result += `手机号: ${data.data.phone || '未设置'}\n`;
                    
                    showResult('update-result', result, 'success');
                } else {
                    showResult('update-result', `❌ 更新个人信息失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('update-result', `❌ 请求失败: ${error.message}`, 'error');
            }
        }

        // 获取统计信息
        async function getStats() {
            if (!authToken) {
                showResult('stats-result', '❌ 请先登录', 'error');
                return;
            }
            
            try {
                showResult('stats-result', '正在获取统计信息...', 'success');
                
                const response = await fetch('http://localhost:8080/api/profile/stats', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    let result = '✅ 获取统计信息成功\n\n';
                    result += `总借阅: ${data.data.totalBorrows}\n`;
                    result += `当前借阅: ${data.data.activeBorrows}\n`;
                    result += `逾期图书: ${data.data.overdueBorrows}\n`;
                    result += `收藏图书: ${data.data.totalFavorites}\n`;
                    
                    showResult('stats-result', result, 'success');
                } else {
                    showResult('stats-result', `❌ 获取统计信息失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('stats-result', `❌ 请求失败: ${error.message}`, 'error');
            }
        }

        // 显示结果
        function showResult(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 页面加载时更新认证状态
        window.onload = function() {
            updateAuthStatus();
        };
    </script>
</body>
</html>
