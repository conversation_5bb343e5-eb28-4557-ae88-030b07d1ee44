import type { User, Book, BorrowRecord, Category } from '@/types';

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    realName: '系统管理员',
    phone: '13800138000',
    role: 'SUPER_ADMIN',
    status: 1,
    avatar: '',
    createdTime: '2024-01-01T00:00:00Z',
    updatedTime: '2024-01-01T00:00:00Z',
    lastLoginTime: '2024-01-15T10:30:00Z',
  },
  {
    id: 2,
    username: 'librarian',
    email: '<EMAIL>',
    realName: '图书管理员',
    phone: '13800138001',
    role: 'ADMIN',
    status: 1,
    avatar: '',
    createdTime: '2024-01-02T00:00:00Z',
    updatedTime: '2024-01-02T00:00:00Z',
    lastLoginTime: '2024-01-15T09:15:00Z',
  },
  {
    id: 3,
    username: 'user001',
    email: '<EMAIL>',
    realName: '张三',
    phone: '13800138002',
    role: 'USER',
    status: 1,
    avatar: '',
    createdTime: '2024-01-03T00:00:00Z',
    updatedTime: '2024-01-03T00:00:00Z',
    lastLoginTime: '2024-01-15T08:45:00Z',
  },
  {
    id: 4,
    username: 'user002',
    email: '<EMAIL>',
    realName: '李四',
    phone: '13800138003',
    role: 'USER',
    status: 1,
    avatar: '',
    createdTime: '2024-01-04T00:00:00Z',
    updatedTime: '2024-01-04T00:00:00Z',
    lastLoginTime: '2024-01-14T16:20:00Z',
  },
  {
    id: 5,
    username: 'user003',
    email: '<EMAIL>',
    realName: '王五',
    phone: '13800138004',
    role: 'USER',
    status: 0,
    avatar: '',
    createdTime: '2024-01-05T00:00:00Z',
    updatedTime: '2024-01-05T00:00:00Z',
    lastLoginTime: '2024-01-10T14:30:00Z',
  },
];

// 模拟分类数据
export const mockCategories: Category[] = [
  { id: 1, name: '文学', description: '文学类图书', status: 1 },
  { id: 2, name: '科技', description: '科技类图书', status: 1 },
  { id: 3, name: '历史', description: '历史类图书', status: 1 },
  { id: 4, name: '艺术', description: '艺术类图书', status: 1 },
  { id: 5, name: '教育', description: '教育类图书', status: 1 },
  { id: 6, name: '经济', description: '经济类图书', status: 1 },
];

// 模拟图书数据
export const mockBooks: Book[] = [
  {
    id: 1,
    title: '红楼梦',
    author: '曹雪芹',
    publisher: '人民文学出版社',
    publishDate: '2020-01-01',
    isbn: '9787020123456',
    categoryId: 1,
    categoryName: '文学',
    price: 45.00,
    totalQuantity: 10,
    availableQuantity: 7,
    description: '中国古典四大名著之一，描写了贾、史、王、薛四大家族的兴衰史。',
    coverUrl: '',
    status: 1,
    createdTime: '2024-01-01T00:00:00Z',
    updatedTime: '2024-01-01T00:00:00Z',
  },
  {
    id: 2,
    title: '西游记',
    author: '吴承恩',
    publisher: '人民文学出版社',
    publishDate: '2020-02-01',
    isbn: '9787020123457',
    categoryId: 1,
    categoryName: '文学',
    price: 42.00,
    totalQuantity: 8,
    availableQuantity: 5,
    description: '中国古典四大名著之一，讲述了孙悟空等人西天取经的故事。',
    coverUrl: '',
    status: 1,
    createdTime: '2024-01-02T00:00:00Z',
    updatedTime: '2024-01-02T00:00:00Z',
  },
  {
    id: 3,
    title: '算法导论',
    author: 'Thomas H. Cormen',
    publisher: '机械工业出版社',
    publishDate: '2021-03-01',
    isbn: '9787111234567',
    categoryId: 2,
    categoryName: '科技',
    price: 128.00,
    totalQuantity: 5,
    availableQuantity: 2,
    description: '计算机科学领域的经典教材，全面介绍了算法设计与分析。',
    coverUrl: '',
    status: 1,
    createdTime: '2024-01-03T00:00:00Z',
    updatedTime: '2024-01-03T00:00:00Z',
  },
  {
    id: 4,
    title: '史记',
    author: '司马迁',
    publisher: '中华书局',
    publishDate: '2019-05-01',
    isbn: '9787101345678',
    categoryId: 3,
    categoryName: '历史',
    price: 89.00,
    totalQuantity: 6,
    availableQuantity: 4,
    description: '中国第一部纪传体通史，被誉为"史家之绝唱，无韵之离骚"。',
    coverUrl: '',
    status: 1,
    createdTime: '2024-01-04T00:00:00Z',
    updatedTime: '2024-01-04T00:00:00Z',
  },
  {
    id: 5,
    title: 'JavaScript高级程序设计',
    author: 'Matt Frisbie',
    publisher: '人民邮电出版社',
    publishDate: '2022-01-01',
    isbn: '9787115456789',
    categoryId: 2,
    categoryName: '科技',
    price: 99.00,
    totalQuantity: 12,
    availableQuantity: 8,
    description: 'JavaScript开发者必读的经典教程，深入讲解JavaScript语言特性。',
    coverUrl: '',
    status: 1,
    createdTime: '2024-01-05T00:00:00Z',
    updatedTime: '2024-01-05T00:00:00Z',
  },
];

// 模拟借阅记录数据
export const mockBorrowRecords: BorrowRecord[] = [
  {
    id: 1,
    userId: 3,
    bookId: 1,
    borrowDate: '2024-01-10T10:00:00Z',
    dueDate: '2024-02-09T10:00:00Z',
    returnDate: '',
    status: 'BORROWED',
    renewCount: 0,
    fine: 0,
    remarks: '正常借阅',
    user: mockUsers[2],
    book: mockBooks[0],
  },
  {
    id: 2,
    userId: 4,
    bookId: 2,
    borrowDate: '2024-01-08T14:30:00Z',
    dueDate: '2024-02-07T14:30:00Z',
    returnDate: '2024-01-25T16:20:00Z',
    status: 'RETURNED',
    renewCount: 1,
    fine: 0,
    remarks: '续借一次后归还',
    user: mockUsers[3],
    book: mockBooks[1],
  },
  {
    id: 3,
    userId: 3,
    bookId: 3,
    borrowDate: '2023-12-15T09:15:00Z',
    dueDate: '2024-01-14T09:15:00Z',
    returnDate: '',
    status: 'OVERDUE',
    renewCount: 0,
    fine: 5.00,
    remarks: '逾期未还',
    user: mockUsers[2],
    book: mockBooks[2],
  },
  {
    id: 4,
    userId: 4,
    bookId: 4,
    borrowDate: '2024-01-12T11:45:00Z',
    dueDate: '2024-02-11T11:45:00Z',
    returnDate: '',
    status: 'BORROWED',
    renewCount: 0,
    fine: 0,
    remarks: '学习需要',
    user: mockUsers[3],
    book: mockBooks[3],
  },
  {
    id: 5,
    userId: 3,
    bookId: 5,
    borrowDate: '2024-01-05T15:20:00Z',
    dueDate: '2024-02-04T15:20:00Z',
    returnDate: '',
    status: 'RENEWED',
    renewCount: 1,
    fine: 0,
    remarks: '续借学习',
    user: mockUsers[2],
    book: mockBooks[4],
  },
];

// 生成更多模拟数据的工具函数
export function generateMockUsers(count: number): User[] {
  const users: User[] = [];
  const roles = ['USER', 'ADMIN', 'SUPER_ADMIN'];
  const statuses = [0, 1];
  
  for (let i = 0; i < count; i++) {
    users.push({
      id: mockUsers.length + i + 1,
      username: `user${String(i + 1).padStart(3, '0')}`,
      email: `user${i + 1}@example.com`,
      realName: `用户${i + 1}`,
      phone: `138${String(i + 1).padStart(8, '0')}`,
      role: roles[Math.floor(Math.random() * roles.length)] as any,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      avatar: '',
      createdTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedTime: new Date().toISOString(),
      lastLoginTime: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString() : '',
    });
  }
  
  return users;
}

export function generateMockBooks(count: number): Book[] {
  const books: Book[] = [];
  const categories = mockCategories;
  const publishers = ['人民文学出版社', '机械工业出版社', '清华大学出版社', '电子工业出版社', '中华书局'];
  
  for (let i = 0; i < count; i++) {
    const category = categories[Math.floor(Math.random() * categories.length)];
    const totalQuantity = Math.floor(Math.random() * 20) + 1;
    const availableQuantity = Math.floor(Math.random() * totalQuantity);
    
    books.push({
      id: mockBooks.length + i + 1,
      title: `图书${i + 1}`,
      author: `作者${i + 1}`,
      publisher: publishers[Math.floor(Math.random() * publishers.length)],
      publishDate: new Date(Date.now() - Math.random() * 5 * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      isbn: `978${String(Math.floor(Math.random() * 1000000000)).padStart(9, '0')}`,
      categoryId: category.id,
      categoryName: category.name,
      price: Math.floor(Math.random() * 200) + 20,
      totalQuantity,
      availableQuantity,
      description: `这是图书${i + 1}的描述信息，内容丰富，值得阅读。`,
      coverUrl: '',
      status: Math.random() > 0.1 ? 1 : 0,
      createdTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedTime: new Date().toISOString(),
    });
  }
  
  return books;
}
