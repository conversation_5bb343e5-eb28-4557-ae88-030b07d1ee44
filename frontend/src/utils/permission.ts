import type { User } from '@/types';

// 角色权限级别定义
export const ROLE_LEVELS = {
  USER: 1,
  ADMIN: 2,
  SUPER_ADMIN: 3,
} as const;

export type RoleType = keyof typeof ROLE_LEVELS;

/**
 * 检查用户是否有指定角色
 */
export const hasRole = (user: User | null, role: RoleType): boolean => {
  if (!user) return false;
  return user.role === role;
};

/**
 * 检查用户是否有指定角色中的任意一个
 */
export const hasAnyRole = (user: User | null, roles: RoleType[]): boolean => {
  if (!user) return false;
  return roles.includes(user.role as RoleType);
};

/**
 * 检查用户角色级别是否满足最低要求
 */
export const hasMinRole = (user: User | null, minRole: RoleType): boolean => {
  if (!user) return false;
  
  const userLevel = ROLE_LEVELS[user.role as RoleType];
  const minLevel = ROLE_LEVELS[minRole];
  
  return userLevel >= minLevel;
};

/**
 * 检查用户是否为管理员（ADMIN或SUPER_ADMIN）
 */
export const isAdmin = (user: User | null): boolean => {
  return hasAnyRole(user, ['ADMIN', 'SUPER_ADMIN']);
};

/**
 * 检查用户是否为超级管理员
 */
export const isSuperAdmin = (user: User | null): boolean => {
  return hasRole(user, 'SUPER_ADMIN');
};

/**
 * 检查用户是否为普通用户
 */
export const isUser = (user: User | null): boolean => {
  return hasRole(user, 'USER');
};

/**
 * 检查用户账户状态是否正常
 */
export const isAccountActive = (user: User | null): boolean => {
  if (!user) return false;
  return user.status === 1; // 1表示启用，0表示禁用
};

/**
 * 检查用户是否有权限访问指定功能
 */
export const hasPermission = (
  user: User | null,
  requiredRoles?: RoleType[],
  requireActive: boolean = true
): boolean => {
  // 检查用户是否存在
  if (!user) return false;
  
  // 检查账户状态
  if (requireActive && !isAccountActive(user)) return false;
  
  // 如果没有指定角色要求，则允许访问
  if (!requiredRoles || requiredRoles.length === 0) return true;
  
  // 检查角色权限
  return hasAnyRole(user, requiredRoles);
};

/**
 * 获取角色显示名称
 */
export const getRoleDisplayName = (role: string): string => {
  const roleNames: Record<string, string> = {
    USER: '普通用户',
    ADMIN: '管理员',
    SUPER_ADMIN: '超级管理员',
  };
  
  return roleNames[role] || '未知角色';
};

/**
 * 获取用户状态显示名称
 */
export const getStatusDisplayName = (status: number): string => {
  return status === 1 ? '正常' : '禁用';
};

/**
 * 检查用户是否可以管理指定用户
 */
export const canManageUser = (currentUser: User | null, targetUser: User): boolean => {
  if (!currentUser) return false;
  
  // 超级管理员可以管理所有用户
  if (isSuperAdmin(currentUser)) return true;
  
  // 管理员可以管理普通用户，但不能管理其他管理员
  if (isAdmin(currentUser) && isUser(targetUser)) return true;
  
  // 用户只能管理自己
  if (currentUser.id === targetUser.id) return true;
  
  return false;
};

/**
 * 检查用户是否可以执行图书管理操作
 */
export const canManageBooks = (user: User | null): boolean => {
  return isAdmin(user);
};

/**
 * 检查用户是否可以执行借阅管理操作
 */
export const canManageBorrows = (user: User | null): boolean => {
  return isAdmin(user);
};

/**
 * 检查用户是否可以查看统计数据
 */
export const canViewStatistics = (user: User | null): boolean => {
  return isAdmin(user);
};

/**
 * 权限验证装饰器（用于组件方法）
 */
export const requirePermission = (requiredRoles: RoleType[]) => {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      const user = this.userStore?.userInfo || null;
      
      if (!hasPermission(user, requiredRoles)) {
        console.warn(`权限不足，需要角色: ${requiredRoles.join(', ')}`);
        return;
      }
      
      return originalMethod.apply(this, args);
    };
    
    return descriptor;
  };
};
