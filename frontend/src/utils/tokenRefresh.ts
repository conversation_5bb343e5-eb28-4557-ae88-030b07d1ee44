import { getToken, setToken, removeToken, parseJWT, isTokenValid } from './token';
import { useUserStore } from '@/stores/user';
import { useMessageStore } from '@/stores/message';
import router from '@/router';

/**
 * Token自动刷新服务
 */
export class TokenRefreshService {
  private refreshTimer: NodeJS.Timeout | null = null;
  private isRefreshing = false;
  private refreshPromise: Promise<string> | null = null;
  private readonly REFRESH_BEFORE_EXPIRE = 5 * 60 * 1000; // 过期前5分钟刷新
  private readonly MIN_REFRESH_INTERVAL = 60 * 1000; // 最小刷新间隔1分钟

  /**
   * 启动自动刷新
   */
  startAutoRefresh() {
    console.log('启动Token自动刷新服务');
    this.scheduleRefresh();
  }

  /**
   * 停止自动刷新
   */
  stopAutoRefresh() {
    console.log('停止Token自动刷新服务');
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
    this.isRefreshing = false;
    this.refreshPromise = null;
  }

  /**
   * 手动刷新Token
   */
  async manualRefresh(): Promise<string> {
    console.log('手动刷新Token');
    return this.refreshToken();
  }

  /**
   * 检查Token是否需要刷新
   */
  shouldRefreshToken(): boolean {
    const token = getToken();
    if (!token || !isTokenValid()) {
      return false;
    }

    const payload = parseJWT(token);
    if (!payload || !payload.exp) {
      return false;
    }

    const expireTime = payload.exp * 1000;
    const currentTime = Date.now();
    const timeUntilExpire = expireTime - currentTime;

    // 如果距离过期时间小于5分钟，需要刷新
    return timeUntilExpire <= this.REFRESH_BEFORE_EXPIRE;
  }

  /**
   * 计划下次刷新
   */
  private scheduleRefresh() {
    const token = getToken();
    if (!token || !isTokenValid()) {
      console.log('Token无效，停止自动刷新');
      return;
    }

    const payload = parseJWT(token);
    if (!payload || !payload.exp) {
      console.log('无法解析Token过期时间，停止自动刷新');
      return;
    }

    const expireTime = payload.exp * 1000;
    const currentTime = Date.now();
    const timeUntilExpire = expireTime - currentTime;
    
    // 在过期前5分钟刷新，但至少等待1分钟
    const refreshTime = Math.max(
      timeUntilExpire - this.REFRESH_BEFORE_EXPIRE, 
      this.MIN_REFRESH_INTERVAL
    );

    console.log(`计划在${Math.round(refreshTime / 1000)}秒后刷新Token`);

    this.refreshTimer = setTimeout(() => {
      this.refreshToken().catch(error => {
        console.error('自动刷新Token失败:', error);
      });
    }, refreshTime);
  }

  /**
   * 刷新Token
   */
  private async refreshToken(): Promise<string> {
    if (this.isRefreshing) {
      console.log('Token正在刷新中，等待完成');
      return this.refreshPromise!;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.doRefreshToken();

    try {
      const newToken = await this.refreshPromise;
      console.log('Token刷新成功');
      this.scheduleRefresh(); // 计划下次刷新
      return newToken;
    } catch (error) {
      console.error('Token刷新失败:', error);
      this.handleRefreshError(error);
      throw error;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * 执行Token刷新
   */
  private async doRefreshToken(): Promise<string> {
    const currentToken = getToken();
    if (!currentToken) {
      throw new Error('没有可用的Token');
    }

    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.code === 200 && data.data && data.data.token) {
        const newToken = data.data.token;
        
        // 更新Token
        setToken(newToken);
        
        // 更新用户Store中的token
        const userStore = useUserStore();
        userStore.setToken(newToken);
        
        // 如果有用户信息，也更新一下
        if (data.data.user) {
          userStore.setUserInfo(data.data.user);
        }
        
        console.log('Token刷新成功，新Token已保存');
        return newToken;
      } else {
        throw new Error(data.message || 'Token刷新失败');
      }
    } catch (error: any) {
      console.error('Token刷新请求失败:', error);
      throw new Error(`Token刷新失败: ${error.message}`);
    }
  }

  /**
   * 处理刷新错误
   */
  private handleRefreshError(error: any) {
    console.error('Token刷新错误处理:', error);
    
    const userStore = useUserStore();
    const messageStore = useMessageStore();
    
    // 清除认证状态
    userStore.logout();
    
    // 显示错误消息
    messageStore.error('登录已过期，请重新登录');
    
    // 跳转到登录页
    if (router.currentRoute.value.path !== '/login') {
      router.push({
        path: '/login',
        query: { redirect: router.currentRoute.value.fullPath }
      });
    }
  }

  /**
   * 获取Token剩余时间（秒）
   */
  getTokenRemainingTime(): number {
    const token = getToken();
    if (!token) return 0;

    const payload = parseJWT(token);
    if (!payload || !payload.exp) return 0;

    const expireTime = payload.exp * 1000;
    const currentTime = Date.now();
    const remaining = Math.max(0, expireTime - currentTime);
    
    return Math.floor(remaining / 1000);
  }

  /**
   * 获取Token过期时间
   */
  getTokenExpireTime(): Date | null {
    const token = getToken();
    if (!token) return null;

    const payload = parseJWT(token);
    if (!payload || !payload.exp) return null;

    return new Date(payload.exp * 1000);
  }

  /**
   * 检查Token状态
   */
  getTokenStatus(): {
    hasToken: boolean;
    isValid: boolean;
    remainingTime: number;
    expireTime: Date | null;
    needsRefresh: boolean;
  } {
    const hasToken = !!getToken();
    const isValid = isTokenValid();
    const remainingTime = this.getTokenRemainingTime();
    const expireTime = this.getTokenExpireTime();
    const needsRefresh = this.shouldRefreshToken();

    return {
      hasToken,
      isValid,
      remainingTime,
      expireTime,
      needsRefresh
    };
  }
}

// 创建全局实例
export const tokenRefreshService = new TokenRefreshService();

// 自动启动服务（如果有有效token）
if (typeof window !== 'undefined') {
  // 页面加载时检查是否需要启动自动刷新
  const checkAndStartRefresh = () => {
    if (isTokenValid()) {
      tokenRefreshService.startAutoRefresh();
    }
  };

  // 立即检查
  checkAndStartRefresh();

  // 监听storage变化，当token更新时重新启动服务
  window.addEventListener('storage', (event) => {
    if (event.key === 'token') {
      tokenRefreshService.stopAutoRefresh();
      if (event.newValue && isTokenValid()) {
        tokenRefreshService.startAutoRefresh();
      }
    }
  });

  // 页面可见性变化时检查token状态
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden && isTokenValid()) {
      // 页面变为可见时，检查是否需要刷新token
      if (tokenRefreshService.shouldRefreshToken()) {
        tokenRefreshService.manualRefresh().catch(console.error);
      }
    }
  });
}

export default tokenRefreshService;
