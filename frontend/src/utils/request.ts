import axios from 'axios';
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError, AxiosRequestConfig } from 'axios';
import { useUserStore } from '@/stores/user';
import { useMessageStore } from '@/stores/message';
import { useLoadingStore } from '@/stores/loading';
import { errorHand<PERSON>, parseAxiosError, isAuthError } from '@/utils/errorHandler';
import { apiConfig } from '@/config';
import router from '@/router';

// 请求队列管理
class RequestQueue {
  private queue = new Map<string, AbortController>();

  add(key: string, controller: AbortController) {
    if (this.queue.has(key)) {
      this.queue.get(key)?.abort();
    }
    this.queue.set(key, controller);
  }

  remove(key: string) {
    this.queue.delete(key);
  }

  clear() {
    this.queue.forEach(controller => controller.abort());
    this.queue.clear();
  }
}

const requestQueue = new RequestQueue();

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: apiConfig.baseURL,
  timeout: apiConfig.timeout,
  headers: apiConfig.headers,
});

// 请求计数器
let requestCount = 0;

// 生成请求唯一标识
function generateRequestKey(config: AxiosRequestConfig): string {
  return `${config.method?.toUpperCase()}_${config.url}_${JSON.stringify(config.params || {})}_${JSON.stringify(config.data || {})}`;
}

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const userStore = useUserStore();
    const loadingStore = useLoadingStore();

    // 检查token有效性并添加认证头
    const token = userStore.token;
    if (token && config.headers) {
      // 检查token是否过期
      if (!userStore.checkTokenValid()) {
        console.log('Token已过期，清除用户信息');
        userStore.clearUserInfo();
        // 如果是需要认证的请求，直接拒绝
        if (config.url && !config.url.includes('/login') && !config.url.includes('/register') && !config.url.includes('/auth/')) {
          return Promise.reject(new Error('Token已过期，请重新登录'));
        }
      } else {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    // 添加请求ID用于追踪
    if (config.headers) {
      config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // 添加时间戳防止缓存
    if (config.method?.toLowerCase() === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
    }

    // 请求去重处理
    const requestKey = generateRequestKey(config);
    const controller = new AbortController();
    config.signal = controller.signal;
    requestQueue.add(requestKey, controller);

    // 全局loading处理
    requestCount++;
    if (requestCount === 1 && !config.headers?.['X-No-Loading']) {
      loadingStore.showLoading({ message: '加载中...' });
    }

    // 开发环境日志
    if (import.meta.env.DEV) {
      console.log(`🚀 Request: ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      });
    }

    return config;
  },
  (error) => {
    requestCount = Math.max(0, requestCount - 1);
    if (requestCount === 0) {
      useLoadingStore().hide();
    }

    const appError = errorHandler.handleError(error);
    return Promise.reject(appError);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const loadingStore = useLoadingStore();
    const requestKey = generateRequestKey(response.config);

    // 移除请求队列
    requestQueue.remove(requestKey);

    // 全局loading处理
    requestCount = Math.max(0, requestCount - 1);
    if (requestCount === 0) {
      loadingStore.hide();
    }

    // 开发环境日志
    if (import.meta.env.DEV) {
      console.log(`✅ Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });
    }

    const { data } = response;

    // 如果是文件下载等特殊情况，直接返回
    if (response.config.responseType === 'blob') {
      return response;
    }

    // 检查业务状态码
    if (data && typeof data === 'object') {
      if (data.code !== undefined) {
        // 标准API响应格式
        if (data.code === 200 || data.code === 0) {
          return data;
        } else {
          // 业务错误
          const error = new Error(data.message || '请求失败');
          (error as any).code = data.code;
          (error as any).data = data;
          throw error;
        }
      }
    }

    // 直接返回数据（非标准格式）
    return data;
  },
  (error: AxiosError) => {
    const loadingStore = useLoadingStore();
    const messageStore = useMessageStore();
    const userStore = useUserStore();

    // 移除请求队列
    if (error.config) {
      const requestKey = generateRequestKey(error.config);
      requestQueue.remove(requestKey);
    }

    // 全局loading处理
    requestCount = Math.max(0, requestCount - 1);
    if (requestCount === 0) {
      loadingStore.hide();
    }

    // 请求被取消，不处理错误
    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }

    // 开发环境日志
    if (import.meta.env.DEV) {
      console.error(`❌ Request Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      });
    }

    // 解析错误
    const appError = parseAxiosError(error);

    // 认证错误处理
    if (isAuthError(appError)) {
      console.log('检测到认证错误，清除用户状态并跳转到登录页');
      userStore.logout();

      // 避免在登录页面重复跳转
      if (router.currentRoute.value.path !== '/login') {
        router.push({
          path: '/login',
          query: { redirect: router.currentRoute.value.fullPath }
        });
        messageStore.warning('登录已过期，请重新登录');
      }

      return Promise.reject(appError);
    }

    // 其他错误通过全局错误处理器处理
    errorHandler.handleError(appError);

    return Promise.reject(appError);
  }
);

// 请求方法封装
export const http = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return request.get(url, config);
  },

  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return request.post(url, data, config);
  },

  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return request.put(url, data, config);
  },

  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return request.delete(url, config);
  },

  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return request.patch(url, data, config);
  },

  // 上传文件
  upload<T = any>(url: string, file: File, config?: AxiosRequestConfig): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    return request.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    });
  },

  // 下载文件
  download(url: string, filename?: string, config?: AxiosRequestConfig): Promise<void> {
    return request.get(url, {
      ...config,
      responseType: 'blob',
    }).then((response) => {
      const blob = new Blob([response]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    });
  },

  // 取消所有请求
  cancelAll() {
    requestQueue.clear();
  },
};

export default request;
