import type { AxiosError } from 'axios';

// 错误类型定义
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

// 错误代码映射
export const ERROR_CODES = {
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  
  // 业务错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  
  // 系统错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  
  // 未知错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

// 错误消息映射
export const ERROR_MESSAGES: Record<string, string> = {
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_CODES.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [ERROR_CODES.UNAUTHORIZED]: '未授权访问，请重新登录',
  [ERROR_CODES.FORBIDDEN]: '权限不足，无法访问该资源',
  [ERROR_CODES.TOKEN_EXPIRED]: '登录已过期，请重新登录',
  [ERROR_CODES.VALIDATION_ERROR]: '输入数据验证失败',
  [ERROR_CODES.RESOURCE_NOT_FOUND]: '请求的资源不存在',
  [ERROR_CODES.RESOURCE_CONFLICT]: '资源冲突，操作失败',
  [ERROR_CODES.INTERNAL_ERROR]: '系统内部错误，请联系管理员',
  [ERROR_CODES.SERVICE_UNAVAILABLE]: '服务暂时不可用，请稍后重试',
  [ERROR_CODES.UNKNOWN_ERROR]: '未知错误，请稍后重试',
};

// HTTP状态码到错误代码的映射
const HTTP_STATUS_TO_ERROR_CODE: Record<number, string> = {
  400: ERROR_CODES.VALIDATION_ERROR,
  401: ERROR_CODES.UNAUTHORIZED,
  403: ERROR_CODES.FORBIDDEN,
  404: ERROR_CODES.RESOURCE_NOT_FOUND,
  409: ERROR_CODES.RESOURCE_CONFLICT,
  500: ERROR_CODES.INTERNAL_ERROR,
  502: ERROR_CODES.SERVICE_UNAVAILABLE,
  503: ERROR_CODES.SERVICE_UNAVAILABLE,
  504: ERROR_CODES.TIMEOUT_ERROR,
};

// 创建应用错误
export function createAppError(
  code: string,
  message?: string,
  details?: any
): AppError {
  return {
    code,
    message: message || ERROR_MESSAGES[code] || ERROR_MESSAGES[ERROR_CODES.UNKNOWN_ERROR],
    details,
    timestamp: Date.now(),
  };
}

// 解析Axios错误
export function parseAxiosError(error: AxiosError): AppError {
  // 网络错误
  if (!error.response) {
    if (error.code === 'ECONNABORTED') {
      return createAppError(ERROR_CODES.TIMEOUT_ERROR);
    }
    return createAppError(ERROR_CODES.NETWORK_ERROR);
  }

  const { status, data } = error.response;
  
  // 根据HTTP状态码确定错误类型
  const errorCode = HTTP_STATUS_TO_ERROR_CODE[status] || ERROR_CODES.UNKNOWN_ERROR;
  
  // 尝试从响应中获取错误消息
  let message = ERROR_MESSAGES[errorCode];
  if (data && typeof data === 'object') {
    if (data.message) {
      message = data.message;
    } else if (data.error) {
      message = data.error;
    } else if (data.msg) {
      message = data.msg;
    }
  }

  return createAppError(errorCode, message, data);
}

// 解析通用错误
export function parseError(error: any): AppError {
  // 如果已经是AppError
  if (error && typeof error === 'object' && error.code && error.message) {
    return error as AppError;
  }

  // 如果是Axios错误
  if (error && error.isAxiosError) {
    return parseAxiosError(error as AxiosError);
  }

  // 如果是Error对象
  if (error instanceof Error) {
    return createAppError(ERROR_CODES.UNKNOWN_ERROR, error.message);
  }

  // 如果是字符串
  if (typeof error === 'string') {
    return createAppError(ERROR_CODES.UNKNOWN_ERROR, error);
  }

  // 其他情况
  return createAppError(ERROR_CODES.UNKNOWN_ERROR);
}

// 错误处理器类
export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorListeners: Array<(error: AppError) => void> = [];

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  // 添加错误监听器
  addErrorListener(listener: (error: AppError) => void): void {
    this.errorListeners.push(listener);
  }

  // 移除错误监听器
  removeErrorListener(listener: (error: AppError) => void): void {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  // 处理错误
  handleError(error: any): AppError {
    const appError = parseError(error);
    
    // 通知所有监听器
    this.errorListeners.forEach(listener => {
      try {
        listener(appError);
      } catch (e) {
        console.error('Error in error listener:', e);
      }
    });

    // 记录错误日志
    this.logError(appError);

    return appError;
  }

  // 记录错误日志
  private logError(error: AppError): void {
    const logData = {
      code: error.code,
      message: error.message,
      details: error.details,
      timestamp: new Date(error.timestamp).toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    // 开发环境下在控制台输出
    if (import.meta.env.DEV) {
      console.error('Application Error:', logData);
    }

    // 生产环境下可以发送到日志服务
    if (import.meta.env.PROD) {
      // TODO: 发送错误日志到服务器
      // this.sendErrorToServer(logData);
    }
  }

  // 发送错误到服务器（可选实现）
  private async sendErrorToServer(errorData: any): Promise<void> {
    try {
      // 这里可以实现发送错误日志到服务器的逻辑
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorData),
      // });
    } catch (e) {
      console.error('Failed to send error to server:', e);
    }
  }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance();

// 便捷函数
export function handleError(error: any): AppError {
  return errorHandler.handleError(error);
}

// 检查是否为特定类型的错误
export function isErrorCode(error: AppError, code: string): boolean {
  return error.code === code;
}

export function isNetworkError(error: AppError): boolean {
  return isErrorCode(error, ERROR_CODES.NETWORK_ERROR) || 
         isErrorCode(error, ERROR_CODES.TIMEOUT_ERROR);
}

export function isAuthError(error: AppError): boolean {
  return isErrorCode(error, ERROR_CODES.UNAUTHORIZED) || 
         isErrorCode(error, ERROR_CODES.FORBIDDEN) ||
         isErrorCode(error, ERROR_CODES.TOKEN_EXPIRED);
}

export function isValidationError(error: AppError): boolean {
  return isErrorCode(error, ERROR_CODES.VALIDATION_ERROR);
}

// 获取用户友好的错误消息
export function getUserFriendlyMessage(error: AppError): string {
  // 可以根据错误类型返回更友好的消息
  switch (error.code) {
    case ERROR_CODES.NETWORK_ERROR:
      return '网络连接异常，请检查网络后重试';
    case ERROR_CODES.UNAUTHORIZED:
      return '登录状态已失效，请重新登录';
    case ERROR_CODES.FORBIDDEN:
      return '您没有权限执行此操作';
    case ERROR_CODES.RESOURCE_NOT_FOUND:
      return '请求的内容不存在';
    default:
      return error.message;
  }
}
