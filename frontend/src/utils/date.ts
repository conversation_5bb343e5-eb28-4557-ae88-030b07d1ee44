/**
 * 日期工具函数
 */

/**
 * 格式化日期为本地日期字符串
 * @param dateString 日期字符串
 * @param options 格式化选项
 * @returns 格式化后的日期字符串
 */
export const formatDate = (
  dateString: string | null | undefined,
  options?: Intl.DateTimeFormatOptions
): string => {
  if (!dateString) return '未知';

  try {
    // 处理后端返回的时间格式 "yyyy-MM-dd HH:mm:ss"
    let date: Date;

    // 如果是标准的ISO格式或后端格式，直接解析
    if (dateString.includes('T')) {
      // ISO格式：2025-06-09T19:25:15
      date = new Date(dateString);
    } else if (dateString.includes(' ')) {
      // 后端格式：2025-06-09 19:25:15
      // 确保时区正确处理，假设后端返回的是本地时间
      date = new Date(dateString.replace(' ', 'T'));
    } else {
      // 其他格式
      date = new Date(dateString);
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无效的日期格式:', dateString);
      return '日期格式错误';
    }

    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      timeZone: 'Asia/Shanghai' // 确保使用正确的时区
    };

    return date.toLocaleDateString('zh-CN', options || defaultOptions);
  } catch (error) {
    console.error('日期格式化错误:', error, dateString);
    return '日期格式错误';
  }
};

/**
 * 格式化日期时间为本地日期时间字符串
 * @param dateString 日期字符串
 * @param options 格式化选项
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (
  dateString: string | null | undefined,
  options?: Intl.DateTimeFormatOptions
): string => {
  if (!dateString) return '未知';
  
  try {
    // 处理后端返回的时间格式 "yyyy-MM-dd HH:mm:ss"
    let date: Date;

    // 如果是标准的ISO格式或后端格式，直接解析
    if (dateString.includes('T')) {
      // ISO格式：2025-06-09T19:25:15
      date = new Date(dateString);
    } else if (dateString.includes(' ')) {
      // 后端格式：2025-06-09 19:25:15
      // 确保时区正确处理，假设后端返回的是本地时间
      date = new Date(dateString.replace(' ', 'T'));
    } else {
      // 其他格式
      date = new Date(dateString);
    }

    if (isNaN(date.getTime())) {
      console.warn('无效的日期格式:', dateString);
      return '日期格式错误';
    }

    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
      timeZone: 'Asia/Shanghai' // 确保使用正确的时区
    };

    return date.toLocaleString('zh-CN', options || defaultOptions);
  } catch (error) {
    console.error('日期时间格式化错误:', error, dateString);
    return '日期格式错误';
  }
};

/**
 * 格式化时间为本地时间字符串
 * @param dateString 日期字符串
 * @param options 格式化选项
 * @returns 格式化后的时间字符串
 */
export const formatTime = (
  dateString: string | null | undefined,
  options?: Intl.DateTimeFormatOptions
): string => {
  if (!dateString) return '未知';
  
  try {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      console.warn('无效的日期格式:', dateString);
      return '时间格式错误';
    }
    
    const defaultOptions: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    };
    
    return date.toLocaleTimeString('zh-CN', options || defaultOptions);
  } catch (error) {
    console.error('时间格式化错误:', error, dateString);
    return '时间格式错误';
  }
};

/**
 * 检查日期是否逾期
 * @param dateString 日期字符串
 * @returns 是否逾期
 */
export const isDateOverdue = (dateString: string | null | undefined): boolean => {
  if (!dateString) return false;
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return false;
    return date < new Date();
  } catch (error) {
    console.error('检查逾期状态时出错:', error);
    return false;
  }
};

/**
 * 计算两个日期之间的天数差
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数差
 */
export const getDaysDifference = (
  startDate: string | Date,
  endDate: string | Date = new Date()
): number => {
  try {
    const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
    const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return 0;
    }
    
    const diffTime = end.getTime() - start.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  } catch (error) {
    console.error('计算日期差异时出错:', error);
    return 0;
  }
};

/**
 * 获取相对时间描述
 * @param dateString 日期字符串
 * @returns 相对时间描述
 */
export const getRelativeTime = (dateString: string | null | undefined): string => {
  if (!dateString) return '未知';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '日期格式错误';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffDays > 0) {
      return `${diffDays}天前`;
    } else if (diffHours > 0) {
      return `${diffHours}小时前`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes}分钟前`;
    } else {
      return '刚刚';
    }
  } catch (error) {
    console.error('计算相对时间时出错:', error);
    return '时间计算错误';
  }
};

/**
 * 从借阅记录中获取借阅日期（兼容不同字段名）
 * @param record 借阅记录
 * @returns 借阅日期字符串
 */
export const getBorrowDate = (record: any): string => {
  return record?.borrowTime || record?.borrowDate || '';
};

/**
 * 从借阅记录中获取应还日期（兼容不同字段名）
 * @param record 借阅记录
 * @returns 应还日期字符串
 */
export const getDueDate = (record: any): string => {
  return record?.dueTime || record?.dueDate || '';
};

/**
 * 从借阅记录中获取归还日期（兼容不同字段名）
 * @param record 借阅记录
 * @returns 归还日期字符串
 */
export const getReturnDate = (record: any): string => {
  return record?.returnTime || record?.returnDate || '';
};

/**
 * 专门用于借阅记录的时间格式化
 * @param dateString 日期字符串
 * @param showSeconds 是否显示秒数，默认false
 * @returns 格式化后的时间字符串
 */
export const formatBorrowTime = (
  dateString: string | null | undefined,
  showSeconds: boolean = false
): string => {
  if (!dateString) return '未知';

  try {
    // 处理后端返回的时间格式 "yyyy-MM-dd HH:mm:ss"
    let date: Date;

    if (dateString.includes('T')) {
      // ISO格式：2025-06-09T19:25:15
      date = new Date(dateString);
    } else if (dateString.includes(' ')) {
      // 后端格式：2025-06-09 19:25:15
      date = new Date(dateString.replace(' ', 'T'));
    } else {
      date = new Date(dateString);
    }

    if (isNaN(date.getTime())) {
      console.warn('无效的借阅时间格式:', dateString);
      return '时间格式错误';
    }

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
      timeZone: 'Asia/Shanghai'
    };

    if (showSeconds) {
      options.second = '2-digit';
    }

    return date.toLocaleString('zh-CN', options);
  } catch (error) {
    console.error('借阅时间格式化错误:', error, dateString);
    return '时间格式错误';
  }
};

/**
 * 格式化借阅记录的日期（只显示日期部分）
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export const formatBorrowDate = (
  dateString: string | null | undefined
): string => {
  if (!dateString) return '未知';

  try {
    let date: Date;

    if (dateString.includes('T')) {
      date = new Date(dateString);
    } else if (dateString.includes(' ')) {
      date = new Date(dateString.replace(' ', 'T'));
    } else {
      date = new Date(dateString);
    }

    if (isNaN(date.getTime())) {
      console.warn('无效的借阅日期格式:', dateString);
      return '日期格式错误';
    }

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      timeZone: 'Asia/Shanghai'
    };

    return date.toLocaleDateString('zh-CN', options);
  } catch (error) {
    console.error('借阅日期格式化错误:', error, dateString);
    return '日期格式错误';
  }
};
