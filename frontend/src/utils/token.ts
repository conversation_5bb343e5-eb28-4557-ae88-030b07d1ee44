// Token管理工具类

const TOKEN_KEY = 'token';
const TOKEN_EXPIRE_KEY = 'token_expire';

/**
 * 获取token
 */
export const getToken = (): string | null => {
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * 设置token
 */
export const setToken = (token: string, expireTime?: number): void => {
  localStorage.setItem(TOKEN_KEY, token);

  // 如果没有提供过期时间，尝试从JWT中解析
  if (!expireTime && token) {
    const payload = parseJWT(token);
    if (payload && payload.exp) {
      // JWT的exp是秒级时间戳，需要转换为毫秒
      expireTime = payload.exp * 1000;
    }
  }

  if (expireTime) {
    localStorage.setItem(TOKEN_EXPIRE_KEY, expireTime.toString());
  }
};

/**
 * 移除token
 */
export const removeToken = (): void => {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(TOKEN_EXPIRE_KEY);
};

/**
 * 检查token是否存在
 */
export const hasToken = (): boolean => {
  return !!getToken();
};

/**
 * 检查token是否过期
 */
export const isTokenExpired = (): boolean => {
  const token = getToken();
  if (!token) {
    return true;
  }

  const expireTime = localStorage.getItem(TOKEN_EXPIRE_KEY);

  // 如果没有存储的过期时间，尝试从JWT中解析
  if (!expireTime) {
    const payload = parseJWT(token);
    if (payload && payload.exp) {
      const jwtExpireTime = payload.exp * 1000; // 转换为毫秒
      localStorage.setItem(TOKEN_EXPIRE_KEY, jwtExpireTime.toString());
      return Date.now() > jwtExpireTime;
    }
    // 如果无法解析过期时间，认为未过期（向后兼容）
    return false;
  }

  return Date.now() > parseInt(expireTime);
};

/**
 * 解析JWT token
 */
export const parseJWT = (token: string): any => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('解析JWT token失败:', error);
    return null;
  }
};

/**
 * 获取token中的用户信息
 */
export const getTokenUserInfo = (): any => {
  const token = getToken();
  
  if (!token) {
    return null;
  }
  
  return parseJWT(token);
};

/**
 * 检查token是否有效（存在且未过期）
 */
export const isTokenValid = (): boolean => {
  return hasToken() && !isTokenExpired();
};
