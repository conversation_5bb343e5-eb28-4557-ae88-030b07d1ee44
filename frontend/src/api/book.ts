import { http } from '@/utils/request';
import type {
  ApiResponse,
  Book,
  BookForm,
  BookCategory,
  PageResult,
  BookQueryParams,
  BorrowRequest,
  UploadResponse
} from '@/types';

// 图书相关API
export const bookApi = {
  // 获取图书列表
  getBookList(params: BookQueryParams): Promise<ApiResponse<PageResult<Book>>> {
    return http.get('/books', { params });
  },

  // 获取图书详情
  getBookById(id: number): Promise<ApiResponse<Book>> {
    return http.get(`/books/${id}`);
  },

  // 搜索图书（使用普通列表接口，通过keyword参数搜索）
  searchBooks(keyword: string, params?: Partial<BookQueryParams>): Promise<ApiResponse<PageResult<Book>>> {
    return http.get('/books', {
      params: { keyword, ...params }
    });
  },

  // 获取热门图书（暂时返回模拟数据）
  getPopularBooks(limit: number = 10): Promise<ApiResponse<Book[]>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: []
    });
  },

  // 获取最新图书（暂时返回模拟数据）
  getLatestBooks(limit: number = 10): Promise<ApiResponse<Book[]>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: []
    });
  },

  // 获取推荐图书（暂时返回模拟数据）
  getRecommendedBooks(limit: number = 10): Promise<ApiResponse<Book[]>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: []
    });
  },

  // 借阅图书（使用借阅API）
  borrowBook(data: BorrowRequest): Promise<ApiResponse<void>> {
    return http.post('/borrow/borrow', data);
  },

  // 收藏图书（暂时返回模拟数据）
  favoriteBook(bookId: number): Promise<ApiResponse<void>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: undefined
    });
  },

  // 取消收藏（暂时返回模拟数据）
  unfavoriteBook(bookId: number): Promise<ApiResponse<void>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: undefined
    });
  },

  // 检查是否已收藏（暂时返回模拟数据）
  checkFavorite(bookId: number): Promise<ApiResponse<{ isFavorited: boolean }>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: { isFavorited: false }
    });
  },

  // 获取图书统计信息（暂时返回模拟数据）
  getBookStats(): Promise<ApiResponse<{
    totalBooks: number;
    availableBooks: number;
    borrowedBooks: number;
    categoryStats: Array<{ categoryName: string; count: number }>;
  }>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        totalBooks: 0,
        availableBooks: 0,
        borrowedBooks: 0,
        categoryStats: []
      }
    });
  },
};

// 管理员图书管理API
export const adminBookApi = {
  // 获取图书列表（管理员使用相同接口）
  getBookList(params: BookQueryParams): Promise<ApiResponse<PageResult<Book>>> {
    return http.get('/books', { params });
  },

  // 创建图书
  createBook(data: BookForm): Promise<ApiResponse<Book>> {
    return http.post('/books', data);
  },

  // 更新图书
  updateBook(id: number, data: Partial<BookForm>): Promise<ApiResponse<Book>> {
    return http.put(`/books/${id}`, data);
  },

  // 删除图书
  deleteBook(id: number): Promise<ApiResponse<void>> {
    return http.delete(`/books/${id}`);
  },

  // 批量删除图书（暂时不支持）
  batchDeleteBooks(ids: number[]): Promise<ApiResponse<void>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: undefined
    });
  },

  // 切换图书状态
  toggleBookStatus(id: number): Promise<ApiResponse<void>> {
    return http.put(`/books/${id}/status`);
  },

  // 上传图书封面（暂时返回模拟数据）
  uploadCover(file: File): Promise<ApiResponse<UploadResponse>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        url: 'https://via.placeholder.com/300x400',
        filename: file.name,
        size: file.size,
        type: file.type
      }
    });
  },

  // 导入图书（暂时返回模拟数据）
  importBooks(file: File): Promise<ApiResponse<{
    success: number;
    failed: number;
    errors: string[]
  }>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        success: 0,
        failed: 0,
        errors: []
      }
    });
  },

  // 导出图书（暂时不支持）
  exportBooks(params?: BookQueryParams): Promise<void> {
    return Promise.resolve();
  },

  // 获取图书统计（管理员）（暂时返回模拟数据）
  getBookStatistics(): Promise<ApiResponse<{
    totalBooks: number;
    availableBooks: number;
    borrowedBooks: number;
    overdueBooks: number;
    categoryDistribution: Array<{ categoryName: string; count: number }>;
    monthlyBorrows: Array<{ month: string; count: number }>;
  }>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        totalBooks: 0,
        availableBooks: 0,
        borrowedBooks: 0,
        overdueBooks: 0,
        categoryDistribution: [],
        monthlyBorrows: []
      }
    });
  },
};

// 分类相关API
export const categoryApi = {
  // 获取所有分类
  getAllCategories(): Promise<ApiResponse<BookCategory[]>> {
    return http.get('/categories');
  },

  // 获取分类树
  getCategoryTree(): Promise<ApiResponse<BookCategory[]>> {
    return http.get('/categories/tree');
  },

  // 获取分类详情（暂时返回模拟数据）
  getCategoryById(id: number): Promise<ApiResponse<BookCategory>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        id,
        name: '分类名称',
        description: '分类描述',
        status: 1
      }
    });
  },

  // 创建分类（管理员）（暂时返回模拟数据）
  createCategory(data: Partial<BookCategory>): Promise<ApiResponse<BookCategory>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: data as BookCategory
    });
  },

  // 更新分类（管理员）（暂时返回模拟数据）
  updateCategory(id: number, data: Partial<BookCategory>): Promise<ApiResponse<BookCategory>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: { id, ...data } as BookCategory
    });
  },

  // 删除分类（管理员）（暂时返回模拟数据）
  deleteCategory(id: number): Promise<ApiResponse<void>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: undefined
    });
  },
};

// 兼容性导出
export const getBookList = bookApi.getBookList;
export const getBookById = bookApi.getBookById;
export const createBook = adminBookApi.createBook;
export const updateBook = adminBookApi.updateBook;
export const deleteBook = adminBookApi.deleteBook;
export const toggleBookStatus = adminBookApi.toggleBookStatus;
export const getCategoryTree = categoryApi.getCategoryTree;
export const getAllCategories = categoryApi.getAllCategories;
