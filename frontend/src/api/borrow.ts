import { http } from '@/utils/request';
import type {
  ApiResponse,
  BorrowRecord,
  BorrowForm,
  PageResult,
  BorrowQueryParams,
  BorrowRequest,
  ReturnRequest,
  RenewRequest,
  Statistics
} from '@/types';

// 借阅相关API
export const borrowApi = {
  // 获取我的借阅记录
  getMyBorrowList(params: Partial<BorrowQueryParams>): Promise<ApiResponse<PageResult<BorrowRecord>>> {
    return http.get('/borrow/records', { params });
  },

  // 获取借阅记录详情
  getBorrowById(id: number): Promise<ApiResponse<BorrowRecord>> {
    return http.get(`/borrow/records/${id}`);
  },

  // 借阅图书
  borrowBook(data: BorrowRequest): Promise<ApiResponse<BorrowRecord>> {
    return http.post('/borrow/borrow', data);
  },

  // 归还图书
  returnBook(borrowId: number): Promise<ApiResponse<void>> {
    return http.post(`/borrow/return/${borrowId}`);
  },

  // 续借图书
  renewBook(borrowId: number): Promise<ApiResponse<BorrowRecord>> {
    return http.post(`/borrow/renew/${borrowId}`);
  },

  // 获取当前借阅数量
  getCurrentBorrowCount(): Promise<ApiResponse<number>> {
    return http.get('/borrow/current-count');
  },

  // 检查用户是否有逾期图书
  hasOverdueBooks(): Promise<ApiResponse<boolean>> {
    return http.get('/borrow/has-overdue');
  },

  // 获取借阅统计（暂时使用测试接口）
  getBorrowStats(): Promise<ApiResponse<{
    totalBorrows: number;
    activeBorrows: number;
    overdueBorrows: number;
    totalFines: number;
    borrowHistory: Array<{ date: string; count: number }>;
  }>> {
    return http.get('/test/borrow-stats');
  },

  // 检查图书是否可借阅
  checkBookAvailability(bookId: number): Promise<ApiResponse<{
    available: boolean;
    reason?: string;
    availableDate?: string;
    hasAlreadyBorrowed?: boolean;
  }>> {
    // 在开发环境中使用模拟数据
    if (import.meta.env.DEV) {
      const mockAvailability: Record<number, { available: boolean; reason?: string }> = {
        1: { available: true },
        2: { available: true },
        3: { available: false, reason: '图书库存不足' },
        4: { available: true },
      };

      const availability = mockAvailability[bookId] || { available: true };

      return Promise.resolve({
        code: 200,
        message: 'success',
        data: {
          available: availability.available,
          reason: availability.reason,
          availableDate: availability.available ? undefined : '2024-12-31',
          hasAlreadyBorrowed: false,
        }
      });
    }
    return http.get(`/borrow/check-availability/${bookId}`);
  },

  // 检查用户是否已借过该图书（区分当前借阅和历史借阅）
  checkUserBookHistory(bookId: number): Promise<ApiResponse<{
    hasCurrentlyBorrowed: boolean;
    hasHistoryBorrowed: boolean;
    lastBorrowDate?: string;
    canBorrowAgain: boolean;
    reason?: string;
  }>> {
    return http.get(`/borrow/check-history/${bookId}`);
  },

  // 获取借阅规则（暂时返回模拟数据）
  getBorrowRules(): Promise<ApiResponse<{
    maxBorrowDays: number;
    maxRenewTimes: number;
    maxBorrowBooks: number;
    finePerDay: number;
    renewDays: number;
  }>> {
    // 暂时返回模拟数据，避免404错误
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        maxBorrowDays: 30,
        maxRenewTimes: 1,
        maxBorrowBooks: 5,
        finePerDay: 1.0,
        renewDays: 30
      }
    });
  },
};

// 管理员借阅管理API
export const adminBorrowApi = {
  // 获取所有借阅记录（管理员可以看到所有记录，普通用户只能看到自己的）
  getBorrowList(params: BorrowQueryParams): Promise<ApiResponse<PageResult<BorrowRecord>>> {
    return http.get('/borrow/records', { params });
  },

  // 获取借阅记录详情
  getBorrowById(id: number): Promise<ApiResponse<BorrowRecord>> {
    return http.get(`/borrow/records/${id}`);
  },

  // 代理借阅（管理员为用户借阅）
  proxyBorrow(userId: number, data: BorrowRequest): Promise<ApiResponse<BorrowRecord>> {
    return http.post('/borrow/borrow', { ...data, userId });
  },

  // 强制归还（使用普通归还接口，管理员权限）
  forceReturn(borrowId: number, remarks?: string): Promise<ApiResponse<void>> {
    return http.post(`/borrow/return/${borrowId}`, null, {
      params: { remarks }
    });
  },

  // 批准续借（使用普通续借接口，管理员权限）
  approveRenew(borrowId: number, remarks?: string): Promise<ApiResponse<BorrowRecord>> {
    return http.post(`/borrow/renew/${borrowId}`);
  },

  // 拒绝续借（暂时使用续借接口，后续可扩展）
  rejectRenew(borrowId: number, reason: string): Promise<ApiResponse<void>> {
    return http.post(`/borrow/renew/${borrowId}`, { reason });
  },

  // 处理逾期
  processOverdue(): Promise<ApiResponse<{
    processed: number;
    totalFines: number;
  }>> {
    return http.post('/borrow/process-overdue');
  },

  // 发送催还通知
  sendReturnReminder(borrowIds: number[]): Promise<ApiResponse<{
    sent: number;
    failed: number;
  }>> {
    return http.post('/admin/borrows/send-reminder', { borrowIds });
  },

  // 获取借阅统计（管理员）
  getBorrowStatistics(): Promise<ApiResponse<Statistics>> {
    return http.get('/admin/borrows/statistics');
  },

  // 导出借阅记录
  exportBorrows(params?: BorrowQueryParams): Promise<void> {
    return http.download('/admin/borrows/export', 'borrows.xlsx', { params });
  },

  // 获取逾期报告
  getOverdueReport(): Promise<ApiResponse<{
    totalOverdue: number;
    totalFines: number;
    overdueByUser: Array<{
      userId: number;
      userName: string;
      overdueCount: number;
      totalFines: number;
    }>;
    overdueByBook: Array<{
      bookId: number;
      bookTitle: string;
      overdueCount: number;
    }>;
  }>> {
    return http.get('/admin/borrows/overdue-report');
  },

  // 获取借阅趋势
  getBorrowTrends(period: 'week' | 'month' | 'year' = 'month'): Promise<ApiResponse<{
    borrowTrends: Array<{ date: string; borrows: number; returns: number }>;
    popularBooks: Array<{ bookId: number; bookTitle: string; borrowCount: number }>;
    activeUsers: Array<{ userId: number; userName: string; borrowCount: number }>;
  }>> {
    return http.get('/admin/borrows/trends', { params: { period } });
  },
};

// 兼容性导出
export const borrowBook = (data: BorrowForm): Promise<ApiResponse<string>> => {
  return borrowApi.borrowBook({ bookId: data.bookId, remarks: data.remarks }) as any;
};

export const returnBook = (id: number): Promise<ApiResponse<string>> => {
  return borrowApi.returnBook(id) as any;
};

export const renewBook = (id: number): Promise<ApiResponse<string>> => {
  return borrowApi.renewBook(id) as any;
};

export const getBorrowList = (params: any): Promise<ApiResponse<PageResult<BorrowRecord>>> => {
  return adminBorrowApi.getBorrowList(params);
};

export const getBorrowById = borrowApi.getBorrowById;
export const getCurrentBorrowCount = borrowApi.getCurrentBorrowCount;
export const hasOverdueBooks = borrowApi.hasOverdueBooks;
export const processOverdueBooks = (): Promise<ApiResponse<string>> => {
  return adminBorrowApi.processOverdue() as any;
};
