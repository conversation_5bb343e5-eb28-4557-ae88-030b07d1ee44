import { http } from '@/utils/request';
import type {
  User,
  LoginForm,
  RegisterForm,
  ApiResponse,
  PageResult,
  UserQueryParams,
  LoginResponse,
  ChangePasswordRequest,
  CaptchaResponse
} from '@/types';

// 认证相关API
export const authApi = {
  // 用户登录
  login(data: LoginForm): Promise<ApiResponse<LoginResponse>> {
    return http.post('/auth/login', data);
  },

  // 用户注册
  register(data: RegisterForm): Promise<ApiResponse<User>> {
    return http.post('/auth/register', data);
  },

  // 用户登出
  logout(): Promise<ApiResponse<void>> {
    return http.post('/auth/logout');
  },

  // 刷新token
  refreshToken(): Promise<ApiResponse<{ token: string; expiresIn: number }>> {
    return http.post('/auth/refresh');
  },

  // 获取验证码
  getCaptcha(): Promise<ApiResponse<CaptchaResponse>> {
    return http.get('/auth/captcha');
  },

  // 忘记密码
  forgotPassword(email: string): Promise<ApiResponse<void>> {
    return http.post('/auth/forgot-password', { email });
  },

  // 重置密码
  resetPassword(token: string, newPassword: string): Promise<ApiResponse<void>> {
    return http.post('/auth/reset-password', { token, newPassword });
  },
};

// 用户相关API
export const userApi = {
  // 获取当前用户信息（暂时返回模拟数据，后续需要后端支持）
  getCurrentUser(): Promise<ApiResponse<User>> {
    // 暂时从localStorage获取用户信息
    const userInfo = localStorage.getItem('library_user');
    if (userInfo) {
      try {
        const user = JSON.parse(userInfo);
        return Promise.resolve({
          code: 200,
          message: 'success',
          data: user
        });
      } catch (error) {
        console.error('解析用户信息失败:', error);
        localStorage.removeItem('library_user');
      }
    }

    // 如果没有用户信息，返回一个错误，但不抛出异常
    return Promise.reject({
      code: 401,
      message: '用户信息不存在',
      data: null
    });
  },

  // 更新当前用户信息（暂时返回模拟数据）
  updateCurrentUser(data: Partial<User>): Promise<ApiResponse<User>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: data as User
    });
  },

  // 修改密码（暂时返回模拟数据）
  changePassword(data: ChangePasswordRequest): Promise<ApiResponse<void>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: undefined
    });
  },

  // 上传头像（暂时返回模拟数据）
  uploadAvatar(file: File): Promise<ApiResponse<{ url: string }>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        url: 'https://via.placeholder.com/100x100'
      }
    });
  },

  // 获取用户统计信息（暂时返回模拟数据）
  getUserStats(): Promise<ApiResponse<{
    totalBorrows: number;
    activeBorrows: number;
    overdueBorrows: number;
    totalFines: number;
  }>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        totalBorrows: 0,
        activeBorrows: 0,
        overdueBorrows: 0,
        totalFines: 0
      }
    });
  },
};

// 管理员用户管理API
export const adminUserApi = {
  // 获取用户列表
  getUserList(params: UserQueryParams): Promise<ApiResponse<PageResult<User>>> {
    return http.get('/admin/users', { params });
  },

  // 获取用户详情
  getUserById(id: number): Promise<ApiResponse<User>> {
    return http.get(`/admin/users/${id}`);
  },

  // 创建用户
  createUser(data: RegisterForm & { role: string }): Promise<ApiResponse<User>> {
    const { role, ...userData } = data;
    return http.post('/admin/users', userData, { params: { role } });
  },

  // 更新用户
  updateUser(id: number, data: Partial<User>): Promise<ApiResponse<User>> {
    return http.put(`/admin/users/${id}`, data);
  },

  // 删除用户
  deleteUser(id: number): Promise<ApiResponse<void>> {
    return http.delete(`/admin/users/${id}`);
  },

  // 切换用户状态
  toggleUserStatus(id: number): Promise<ApiResponse<void>> {
    return http.put(`/admin/users/${id}/status`);
  },

  // 重置用户密码
  resetUserPassword(id: number, newPassword: string): Promise<ApiResponse<void>> {
    return http.put(`/admin/users/${id}/password`, null, {
      params: { newPassword }
    });
  },

  // 批量删除用户
  batchDeleteUsers(ids: number[]): Promise<ApiResponse<void>> {
    return http.delete('/admin/users/batch', { data: { ids } });
  },

  // 导出用户列表
  exportUsers(params?: UserQueryParams): Promise<void> {
    return http.download('/admin/users/export', 'users.xlsx', { params });
  },

  // 获取用户统计
  getUserStatistics(): Promise<ApiResponse<{
    totalUsers: number;
    activeUsers: number;
    newUsersToday: number;
    usersByRole: Record<string, number>;
    usersByStatus: Record<string, number>;
  }>> {
    return http.get('/admin/users/statistics');
  },
};

// 兼容性导出（保持向后兼容）
export const login = authApi.login;
export const register = authApi.register;
export const getCurrentUser = userApi.getCurrentUser;
export const updateCurrentUser = userApi.updateCurrentUser;
export const changePassword = userApi.changePassword;
export const getUserList = adminUserApi.getUserList;
export const getUserById = adminUserApi.getUserById;
export const createUser = (data: RegisterForm, role: string) =>
  adminUserApi.createUser({ ...data, role });
export const updateUser = adminUserApi.updateUser;
export const deleteUser = adminUserApi.deleteUser;
export const toggleUserStatus = adminUserApi.toggleUserStatus;
