import http from '@/utils/request';
import type { ApiResponse, UserFavorite, FavoriteForm, PageResult, QueryParams } from '@/types';

// 收藏相关API
export const favoriteApi = {
  // 添加收藏
  addFavorite(data: FavoriteForm): Promise<ApiResponse<string>> {
    return http.post('/user/favorites/add', data);
  },

  // 取消收藏
  removeFavorite(data: FavoriteForm): Promise<ApiResponse<string>> {
    return http.post('/user/favorites/remove', data);
  },

  // 切换收藏状态
  toggleFavorite(data: FavoriteForm): Promise<ApiResponse<{
    isFavorited: boolean;
    message: string;
  }>> {
    return http.post('/user/favorites/toggle', data);
  },

  // 检查是否已收藏
  checkFavorite(bookId: number): Promise<ApiResponse<boolean>> {
    return http.get(`/user/favorites/check/${bookId}`);
  },

  // 分页查询用户收藏列表
  getFavoriteList(params: QueryParams): Promise<ApiResponse<PageResult<UserFavorite>>> {
    return http.get('/user/favorites/list', { params });
  },

  // 获取用户收藏统计信息
  getUserFavoriteStats(): Promise<ApiResponse<{
    totalCount: number;
    userId: number;
  }>> {
    return http.get('/user/favorites/stats');
  },

  // 批量查询图书收藏状态
  batchCheckFavoriteStatus(bookIds: number[]): Promise<ApiResponse<Record<number, boolean>>> {
    return http.post('/user/favorites/batch-check', bookIds);
  },
};

// 兼容性导出
export const addFavorite = favoriteApi.addFavorite;
export const removeFavorite = favoriteApi.removeFavorite;
export const toggleFavorite = favoriteApi.toggleFavorite;
export const checkFavorite = favoriteApi.checkFavorite;
export const getFavoriteList = favoriteApi.getFavoriteList;
export const getUserFavoriteStats = favoriteApi.getUserFavoriteStats;
export const batchCheckFavoriteStatus = favoriteApi.batchCheckFavoriteStatus;
