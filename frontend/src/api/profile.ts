import http from '@/utils/request';
import type { ApiResponse } from '@/types';

// 用户个人信息类型定义
export interface UserProfile {
  id: number;
  username: string;
  realName: string;
  email: string;
  phone?: string;
  avatar?: string;
  role: string;
  status: number;
  createdTime: string;
  lastLoginTime?: string;
  borrowStats: BorrowStats;
}

export interface BorrowStats {
  totalBorrows: number;
  activeBorrows: number;
  overdueBorrows: number;
  totalFavorites: number;
}

export interface UserProfileUpdateForm {
  realName: string;
  email: string;
  phone?: string;
  avatar?: string;
}

export interface ChangePasswordForm {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 个人信息相关API
export const profileApi = {
  // 获取当前用户个人信息
  getCurrentUserProfile(): Promise<ApiResponse<UserProfile>> {
    return http.get('/profile');
  },

  // 更新个人信息
  updateProfile(data: UserProfileUpdateForm): Promise<ApiResponse<UserProfile>> {
    return http.put('/profile', data);
  },

  // 修改密码
  changePassword(data: ChangePasswordForm): Promise<ApiResponse<string>> {
    return http.put('/profile/password', data);
  },

  // 上传头像
  uploadAvatar(file: File): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData();
    formData.append('file', file);
    
    return http.post('/profile/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 获取用户统计信息
  getUserStats(): Promise<ApiResponse<BorrowStats>> {
    return http.get('/profile/stats');
  },
};

// 兼容性导出
export const getCurrentUserProfile = profileApi.getCurrentUserProfile;
export const updateProfile = profileApi.updateProfile;
export const changePassword = profileApi.changePassword;
export const uploadAvatar = profileApi.uploadAvatar;
export const getUserStats = profileApi.getUserStats;
