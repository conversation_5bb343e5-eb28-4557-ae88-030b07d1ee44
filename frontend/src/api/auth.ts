import { http } from '@/utils/request';
import type {
  LoginForm,
  RegisterForm,
  ApiResponse,
  LoginResponse,
  User,
  CaptchaResponse,
  ResetPasswordRequest
} from '@/types';

// 认证相关API
export const authApi = {
  // 用户登录
  login(data: LoginForm): Promise<ApiResponse<LoginResponse>> {
    return http.post('/auth/login', data);
  },

  // 用户注册
  register(data: RegisterForm): Promise<ApiResponse<User>> {
    return http.post('/auth/register', data);
  },

  // 用户登出
  logout(): Promise<ApiResponse<void>> {
    return http.post('/auth/logout');
  },

  // 刷新token（暂时返回模拟数据）
  refreshToken(): Promise<ApiResponse<{ token: string; expiresIn: number }>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        token: 'mock-token',
        expiresIn: 3600
      }
    });
  },

  // 获取验证码（暂时返回模拟数据）
  getCaptcha(): Promise<ApiResponse<CaptchaResponse>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        key: 'mock-key',
        image: 'data:image/png;base64,mock-image'
      }
    });
  },

  // 验证token有效性（暂时返回模拟数据）
  validateToken(): Promise<ApiResponse<{ valid: boolean; user?: User }>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        valid: true
      }
    });
  },

  // 忘记密码 - 发送重置邮件（暂时返回模拟数据）
  forgotPassword(email: string): Promise<ApiResponse<void>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: undefined
    });
  },

  // 重置密码（暂时返回模拟数据）
  resetPassword(data: ResetPasswordRequest): Promise<ApiResponse<void>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: undefined
    });
  },

  // 验证重置密码token（暂时返回模拟数据）
  validateResetToken(token: string): Promise<ApiResponse<{ valid: boolean }>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        valid: true
      }
    });
  },

  // 检查用户名是否可用（暂时返回模拟数据）
  checkUsername(username: string): Promise<ApiResponse<{ available: boolean }>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        available: true
      }
    });
  },

  // 检查邮箱是否可用（暂时返回模拟数据）
  checkEmail(email: string): Promise<ApiResponse<{ available: boolean }>> {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        available: true
      }
    });
  },
};

// 兼容性导出
export const login = authApi.login;
export const register = authApi.register;
export const logout = authApi.logout;
export const refreshToken = authApi.refreshToken;
export const getCaptcha = authApi.getCaptcha;
export const validateToken = authApi.validateToken;
export const forgotPassword = authApi.forgotPassword;
export const resetPassword = authApi.resetPassword;
export const validateResetToken = authApi.validateResetToken;
export const checkUsername = authApi.checkUsername;
export const checkEmail = authApi.checkEmail;

export default authApi;
