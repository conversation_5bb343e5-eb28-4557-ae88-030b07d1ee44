import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';
import { useUserStore } from '@/stores/user';

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard',
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requireAuth: false,
    },
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: {
      title: '注册',
      requireAuth: false,
    },
  },
  // 用户界面路由
  {
    path: '/',
    component: () => import('@/layouts/UserLayout.vue'),
    meta: {
      requireAuth: true,
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '仪表盘',
          requireAuth: true,
        },
      },
      {
        path: 'books',
        name: 'Books',
        component: () => import('@/views/books/BookList.vue'),
        meta: {
          title: '图书浏览',
          requireAuth: true,
        },
      },
      {
        path: 'books/:id',
        name: 'BookDetail',
        component: () => import('@/views/books/BookDetail.vue'),
        meta: {
          title: '图书详情',
          requireAuth: true,
        },
      },
      {
        path: 'my-borrows',
        name: 'MyBorrows',
        component: () => import('@/views/borrows/MyBorrows.vue'),
        meta: {
          title: '我的借阅',
          requireAuth: true,
          roles: ['USER', 'ADMIN', 'SUPER_ADMIN'],
        },
      },
      {
        path: 'my-favorites',
        name: 'MyFavorites',
        component: () => import('@/views/favorites/MyFavorites.vue'),
        meta: {
          title: '我的收藏',
          requireAuth: true,
          roles: ['USER', 'ADMIN', 'SUPER_ADMIN'],
        },
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/profile/ProfileView.vue'),
        meta: {
          title: '个人信息',
          requireAuth: true,
          roles: ['USER', 'ADMIN', 'SUPER_ADMIN'],
        },
      },
      {
        path: 'profile/edit',
        name: 'ProfileEdit',
        component: () => import('@/views/profile/ProfileEdit.vue'),
        meta: {
          title: '编辑个人信息',
          requireAuth: true,
          roles: ['USER', 'ADMIN', 'SUPER_ADMIN'],
        },
      },
      {
        path: 'profile/change-password',
        name: 'ChangePassword',
        component: () => import('@/views/profile/ChangePassword.vue'),
        meta: {
          title: '修改密码',
          requireAuth: true,
          roles: ['USER', 'ADMIN', 'SUPER_ADMIN'],
        },
      },
    ],
  },
  // 管理员路由
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/layouts/AdminLayout.vue'),
    meta: {
      title: '管理后台',
      requireAuth: true,
      roles: ['ADMIN', 'SUPER_ADMIN'],
    },
    children: [
      {
        path: '',
        redirect: '/admin/dashboard',
      },
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/Dashboard.vue'),
        meta: {
          title: '管理仪表盘',
          requireAuth: true,
          roles: ['ADMIN', 'SUPER_ADMIN'],
        },
      },
      {
        path: 'books',
        name: 'AdminBooks',
        component: () => import('@/views/admin/books/BookManagement.vue'),
        meta: {
          title: '图书管理',
          requireAuth: true,
          roles: ['ADMIN', 'SUPER_ADMIN'],
        },
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('@/views/admin/users/UserManagement.vue'),
        meta: {
          title: '用户管理',
          requireAuth: true,
          roles: ['SUPER_ADMIN'],
        },
      },
      {
        path: 'borrows',
        name: 'AdminBorrows',
        component: () => import('@/views/admin/borrows/BorrowManagement.vue'),
        meta: {
          title: '借阅管理',
          requireAuth: true,
          roles: ['ADMIN', 'SUPER_ADMIN'],
        },
      },
    ],
  },

  // 开发工具页面（仅开发环境）
  ...(import.meta.env.DEV ? [
    {
      path: '/dev-tools',
      name: 'DevTools',
      component: () => import('@/views/DevTools.vue'),
      meta: {
        title: '开发工具',
        requireAuth: true,
      },
    },
    {
      path: '/test/book-card',
      name: 'BookCardTest',
      component: () => import('@/views/test/BookCardTest.vue'),
      meta: {
        title: '图书卡片测试',
        requireAuth: false,
      },
    },
    {
      path: '/test/borrow-management',
      name: 'BorrowManagementTest',
      component: () => import('@/views/test/BorrowManagementTest.vue'),
      meta: {
        title: '借阅管理测试',
        requireAuth: false,
      },
    },
    {
      path: '/test/book-cover',
      name: 'BookCoverTest',
      component: () => import('@/views/test/BookCoverTest.vue'),
      meta: {
        title: '图书封面测试',
        requireAuth: false,
      },
    },
    {
      path: '/test/borrow-system-fix',
      name: 'BorrowSystemFixTest',
      component: () => import('@/views/test/BorrowSystemFixTest.vue'),
      meta: {
        title: '借阅系统修复测试',
        requireAuth: false,
      },
    }
  ] : []),

  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/NotFound.vue'),
    meta: {
      title: '页面不存在',
      requireAuth: false,
    },
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore();

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 图书管理系统`;
  }

  try {
    // 如果是跳转到登录或注册页面，跳过初始化等待
    if (to.path === '/login' || to.path === '/register') {
      console.log('跳转到登录/注册页面，跳过认证状态检查');
    } else {
      // 等待认证状态初始化完成
      if (!userStore.isInitialized) {
        console.log('等待认证状态初始化完成...');
        try {
          await userStore.waitForInitialization();
          console.log('认证状态初始化等待完成');
        } catch (initError) {
          console.error('认证状态初始化等待失败:', initError);
          // 初始化超时时，根据是否有token决定处理方式
          if (userStore.token && to.meta.requireAuth) {
            console.log('有token但初始化超时，尝试直接验证登录状态');
            // 如果有token，尝试直接检查登录状态
            if (!userStore.isLoggedIn) {
              console.log('token存在但用户信息缺失，跳转到登录页');
              next({
                path: '/login',
                query: { redirect: to.fullPath },
              });
              return;
            }
          } else if (to.meta.requireAuth) {
            console.log('初始化超时且需要认证，跳转到登录页');
            next({
              path: '/login',
              query: { redirect: to.fullPath },
            });
            return;
          }
        }
      }
    }

    // 检查是否需要认证
    if (to.meta.requireAuth) {
      // 检查是否已登录
      if (!userStore.isLoggedIn) {
        console.log('用户未登录，跳转到登录页');
        next({
          path: '/login',
          query: { redirect: to.fullPath },
        });
        return;
      }

      // 检查角色权限
      if (to.meta.roles && !userStore.hasPermission(to.meta.roles)) {
        console.log('权限不足，跳转到仪表盘');
        next('/dashboard');
        return;
      }
    }

    // 如果已登录用户访问登录或注册页，跳转到仪表盘
    if ((to.path === '/login' || to.path === '/register') && userStore.isLoggedIn) {
      console.log('已登录用户访问登录页，跳转到仪表盘');
      next('/dashboard');
      return;
    }

    next();
  } catch (error) {
    console.error('路由守卫处理失败:', error);

    // 如果是需要认证的路由，跳转到登录页
    if (to.meta.requireAuth) {
      console.log('路由守卫异常，跳转到登录页');
      next({
        path: '/login',
        query: { redirect: to.fullPath },
      });
    } else {
      console.log('路由守卫异常，但不需要认证，继续导航');
      next();
    }
  }
});

export default router;
