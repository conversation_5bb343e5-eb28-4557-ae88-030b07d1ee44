<template>
  <div class="not-found-container">
    <v-card class="not-found-card" elevation="2">
      <v-card-text class="text-center pa-8">
        <v-icon size="120" color="grey-lighten-1" class="mb-4">
          mdi-book-remove
        </v-icon>
        <h1 class="text-h3 font-weight-bold mb-2">404</h1>
        <h2 class="text-h5 font-weight-medium mb-4">页面不存在</h2>
        <p class="text-body-1 text-medium-emphasis mb-6">
          抱歉，您访问的页面不存在或已被删除
        </p>
        <div class="d-flex justify-center gap-4">
          <v-btn
            variant="outlined"
            prepend-icon="mdi-arrow-left"
            @click="$router.go(-1)"
          >
            返回上页
          </v-btn>
          <v-btn
            color="primary"
            variant="elevated"
            prepend-icon="mdi-home"
            @click="$router.push('/dashboard')"
          >
            回到首页
          </v-btn>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
// 404页面组件
</script>

<style scoped>
.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
}

.not-found-card {
  border-radius: 16px !important;
  max-width: 500px;
  width: 100%;
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .not-found-container {
    background: linear-gradient(135deg, #1e1e1e 0%, #121212 100%);
  }
}
</style>
