<template>
  <div class="my-favorites-container">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-btn
            icon
            variant="text"
            @click="handleGoBack"
            class="me-3"
          >
            <v-icon>mdi-arrow-left</v-icon>
          </v-btn>
          <div>
            <h1 class="text-h4 font-weight-bold text-primary">我的收藏</h1>
            <p class="text-body-2 text-medium-emphasis mt-1">
              共收藏了 {{ favoriteStore.totalFavorites }} 本图书
            </p>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="d-flex align-center gap-2">
          <v-btn
            v-if="selectedFavorites.length > 0"
            color="error"
            variant="outlined"
            prepend-icon="mdi-delete"
            @click="handleBatchRemove"
            :loading="isRemoving"
          >
            批量取消收藏 ({{ selectedFavorites.length }})
          </v-btn>

          <v-btn
            color="primary"
            variant="elevated"
            prepend-icon="mdi-refresh"
            @click="handleRefresh"
            :loading="favoriteStore.isLoading"
          >
            刷新
          </v-btn>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="favoriteStore.isLoading && favoriteStore.favorites.length === 0" class="loading-section">
      <v-row>
        <v-col
          v-for="i in 8"
          :key="i"
          cols="12"
          sm="6"
          md="4"
          lg="3"
          xl="3"
        >
          <FavoriteCardSkeleton />
        </v-col>
      </v-row>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!favoriteStore.hasFavorites && !favoriteStore.isLoading" class="empty-section">
      <v-card class="empty-card" elevation="2">
        <v-card-text class="text-center pa-8">
          <v-icon size="80" color="grey-lighten-2" class="mb-4">
            mdi-heart-outline
          </v-icon>
          <h3 class="text-h5 font-weight-bold mb-2">还没有收藏任何图书</h3>
          <p class="text-body-1 text-medium-emphasis mb-4">
            去图书列表发现喜欢的图书并收藏吧
          </p>
          <v-btn
            color="primary"
            variant="elevated"
            prepend-icon="mdi-book-search"
            @click="$router.push('/books')"
          >
            浏览图书
          </v-btn>
        </v-card-text>
      </v-card>
    </div>

    <!-- 收藏列表 -->
    <div v-else class="favorites-content">
      <!-- 批量选择工具栏 -->
      <v-card v-if="favoriteStore.hasFavorites" class="toolbar-card mb-4" elevation="1">
        <v-card-text class="py-3">
          <div class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <v-checkbox
                v-model="selectAll"
                :indeterminate="isIndeterminate"
                @change="handleSelectAll"
                hide-details
                class="me-3"
              />
              <span class="text-body-2">
                {{ selectedFavorites.length > 0 ? `已选择 ${selectedFavorites.length} 项` : '全选' }}
              </span>
            </div>

            <div class="d-flex align-center gap-2">
              <v-chip
                v-if="selectedFavorites.length > 0"
                color="primary"
                variant="tonal"
                size="small"
              >
                {{ selectedFavorites.length }} 项已选择
              </v-chip>
            </div>
          </div>
        </v-card-text>
      </v-card>

      <!-- 收藏图书网格 -->
      <div class="favorites-grid">
        <v-row class="favorites-row">
          <v-col
            v-for="favorite in favoriteStore.favorites"
            :key="favorite.id"
            cols="12"
            sm="6"
            md="4"
            lg="3"
            xl="3"
            class="favorite-col"
          >
            <FavoriteCard
              :favorite="favorite"
              :selected="selectedFavorites.includes(favorite.id)"
              @select="handleSelectFavorite"
              @view-detail="handleViewDetail"
              @remove="handleRemoveFavorite"
            />
          </v-col>
        </v-row>
      </div>

      <!-- 分页 -->
      <div v-if="favoriteStore.pagination.pages > 1" class="pagination-section mt-6">
        <v-pagination
          v-model="currentPage"
          :length="favoriteStore.pagination.pages"
          :total-visible="7"
          @update:model-value="handlePageChange"
          class="justify-center"
        />
      </div>
    </div>

    <!-- 确认删除对话框 -->
    <v-dialog
      v-model="confirmDialog.show"
      max-width="400"
      persistent
    >
      <v-card>
        <v-card-title class="text-h6">
          确认取消收藏
        </v-card-title>
        <v-card-text>
          {{ confirmDialog.message }}
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            variant="text"
            @click="confirmDialog.show = false"
          >
            取消
          </v-btn>
          <v-btn
            color="error"
            variant="elevated"
            @click="handleConfirmRemove"
            :loading="isRemoving"
          >
            确认
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useFavoriteStore } from '@/stores/favorite';
import { useMessageStore } from '@/stores/message';
import FavoriteCard from '@/components/favorites/FavoriteCard.vue';
import FavoriteCardSkeleton from '@/components/favorites/FavoriteCardSkeleton.vue';
import type { UserFavorite } from '@/types';

const router = useRouter();
const favoriteStore = useFavoriteStore();
const messageStore = useMessageStore();

// 响应式状态
const currentPage = ref(1);
const selectedFavorites = ref<number[]>([]);
const isRemoving = ref(false);

const confirmDialog = reactive({
  show: false,
  message: '',
  action: null as (() => Promise<void>) | null,
});

// 计算属性
const selectAll = computed({
  get: () => {
    return favoriteStore.favorites.length > 0 &&
           selectedFavorites.value.length === favoriteStore.favorites.length;
  },
  set: (value: boolean) => {
    if (value) {
      selectedFavorites.value = favoriteStore.favorites.map(fav => fav.id);
    } else {
      selectedFavorites.value = [];
    }
  }
});

const isIndeterminate = computed(() => {
  return selectedFavorites.value.length > 0 &&
         selectedFavorites.value.length < favoriteStore.favorites.length;
});

// 获取收藏列表
const fetchFavorites = async () => {
  try {
    await favoriteStore.fetchFavoriteList({
      current: currentPage.value,
      size: 12,
    });
  } catch (error: any) {
    console.error('获取收藏列表失败:', error);
    showMessage(error.message || '获取收藏列表失败', 'error');
  }
};

// 处理返回
const handleGoBack = () => {
  router.push('/dashboard');
};

// 处理刷新
const handleRefresh = async () => {
  selectedFavorites.value = [];
  await fetchFavorites();
  await favoriteStore.fetchFavoriteStats();
};

// 处理页码变化
const handlePageChange = async (page: number) => {
  currentPage.value = page;
  selectedFavorites.value = [];
  await fetchFavorites();
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

// 处理全选
const handleSelectAll = () => {
  // selectAll computed 属性会自动处理
};

// 处理选择收藏项
const handleSelectFavorite = (favoriteId: number, selected: boolean) => {
  if (selected) {
    if (!selectedFavorites.value.includes(favoriteId)) {
      selectedFavorites.value.push(favoriteId);
    }
  } else {
    const index = selectedFavorites.value.indexOf(favoriteId);
    if (index > -1) {
      selectedFavorites.value.splice(index, 1);
    }
  }
};

// 处理查看详情
const handleViewDetail = (favorite: UserFavorite) => {
  router.push(`/books/${favorite.bookId}`);
};

// 处理单个取消收藏
const handleRemoveFavorite = (favorite: UserFavorite) => {
  confirmDialog.message = `确定要取消收藏《${favorite.bookTitle}》吗？`;
  confirmDialog.action = async () => {
    await favoriteStore.removeFavoriteAction(favorite.bookId);
    showMessage('已取消收藏', 'success');
    await handleRefresh();
  };
  confirmDialog.show = true;
};

// 处理批量取消收藏
const handleBatchRemove = () => {
  const count = selectedFavorites.value.length;
  confirmDialog.message = `确定要取消收藏选中的 ${count} 本图书吗？`;
  confirmDialog.action = async () => {
    isRemoving.value = true;
    try {
      // 批量取消收藏
      const promises = selectedFavorites.value.map(favoriteId => {
        const favorite = favoriteStore.favorites.find(fav => fav.id === favoriteId);
        return favorite ? favoriteStore.removeFavoriteAction(favorite.bookId) : Promise.resolve();
      });

      await Promise.all(promises);
      showMessage(`已取消收藏 ${count} 本图书`, 'success');
      selectedFavorites.value = [];
      await handleRefresh();
    } catch (error: any) {
      console.error('批量取消收藏失败:', error);
      showMessage(error.message || '批量取消收藏失败', 'error');
    } finally {
      isRemoving.value = false;
    }
  };
  confirmDialog.show = true;
};

// 处理确认删除
const handleConfirmRemove = async () => {
  if (confirmDialog.action) {
    try {
      await confirmDialog.action();
    } catch (error: any) {
      console.error('操作失败:', error);
      showMessage(error.message || '操作失败', 'error');
    }
  }
  confirmDialog.show = false;
  confirmDialog.action = null;
};

// 显示消息
const showMessage = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'success') => {
  messageStore.showMessage(message, type);
};

// 组件挂载
onMounted(async () => {
  await handleRefresh();
});
</script>

<style scoped>
.my-favorites-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 200px);
}

.page-header {
  margin-bottom: 24px;
}

.loading-section,
.empty-section {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-card {
  border-radius: 16px !important;
  max-width: 500px;
  margin: 0 auto;
}

.toolbar-card {
  border-radius: 12px !important;
  border: 1px solid rgba(25, 118, 210, 0.2);
}

.favorites-grid {
  min-height: 400px;
}

.favorites-row {
  margin: 0 -8px;
}

.favorite-col {
  padding: 8px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding: 24px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .my-favorites-container {
    padding: 16px;
  }

  .page-header .d-flex {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 16px;
  }

  .page-header .d-flex > div:last-child {
    width: 100%;
    justify-content: flex-end;
  }

  .toolbar-card .d-flex {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;
  }

  .favorites-row {
    margin: 0 -4px;
  }

  .favorite-col {
    padding: 4px;
  }
}

@media (max-width: 600px) {
  .page-header h1 {
    font-size: 1.8rem !important;
  }

  .page-header .d-flex > div:last-child {
    flex-direction: column;
    gap: 8px;
  }

  .page-header .d-flex > div:last-child .v-btn {
    width: 100%;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .toolbar-card {
    border-color: rgba(66, 165, 245, 0.3);
  }
}

/* 动画效果 */
.favorites-grid {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.favorite-col {
  transition: all 0.3s ease;
}

.favorite-col:hover {
  transform: translateY(-2px);
}
</style>
