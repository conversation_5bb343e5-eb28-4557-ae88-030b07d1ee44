<template>
  <div class="borrow-test-container">
    <div class="test-header mb-6">
      <h1 class="text-h4 font-weight-bold gradient-text">借阅管理功能测试</h1>
      <p class="text-body-1 text-medium-emphasis mt-2">
        测试借阅限制、续借功能和图书封面显示
      </p>
    </div>

    <!-- 功能测试说明 -->
    <v-card class="test-info-card mb-6" elevation="2">
      <v-card-text>
        <h3 class="text-h6 mb-3">测试功能列表</h3>
        <v-row>
          <v-col cols="12" md="6">
            <h4 class="text-subtitle-1 mb-2">借阅限制测试：</h4>
            <ul class="test-list">
              <li>同一本图书只能借阅一次</li>
              <li>最大借阅数量限制（5本）</li>
              <li>借阅历史检查</li>
              <li>图书可用性检查</li>
            </ul>
          </v-col>
          <v-col cols="12" md="6">
            <h4 class="text-subtitle-1 mb-2">续借功能测试：</h4>
            <ul class="test-list">
              <li>距离到期日5天内可续借</li>
              <li>最多续借2次</li>
              <li>总借阅时间不超过3个月</li>
              <li>续借状态显示</li>
            </ul>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 测试图书列表 -->
    <div class="test-books-section mb-6">
      <h3 class="text-h6 mb-4">测试图书（点击借阅按钮测试限制功能）</h3>
      <v-row>
        <v-col
          v-for="book in testBooks"
          :key="book.id"
          cols="12"
          sm="6"
          md="4"
          lg="3"
        >
          <BookCard
            :book="book"
            @view-detail="handleViewDetail"
            @borrow="handleBorrow"
            @toggle-favorite="handleToggleFavorite"
          />
        </v-col>
      </v-row>
    </div>

    <!-- 测试借阅记录 -->
    <div class="test-records-section mb-6">
      <h3 class="text-h6 mb-4">测试借阅记录（测试续借功能）</h3>
      <v-row>
        <v-col
          v-for="record in testBorrowRecords"
          :key="record.id"
          cols="12"
          md="6"
          lg="4"
        >
          <BorrowRecordCard
            :record="record"
            @return="handleReturn"
            @renew="handleRenew"
            @view-detail="handleViewBorrowDetail"
          />
        </v-col>
      </v-row>
    </div>

    <!-- 测试结果显示 -->
    <v-card class="test-results mb-6" elevation="2">
      <v-card-text>
        <h3 class="text-h6 mb-3">功能测试状态</h3>
        <div class="test-status">
          <v-chip color="success" class="me-2 mb-2">✓ 图书封面3:5比例显示</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 借阅限制检查</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 续借条件验证</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 续借次数显示</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 借阅历史检查</v-chip>
          <v-chip color="success" class="mb-2">✓ 状态提示优化</v-chip>
        </div>
      </v-card-text>
    </v-card>

    <!-- 借阅对话框 -->
    <BorrowDialog
      v-model="borrowDialog.show"
      :book="borrowDialog.book"
      @confirm="handleConfirmBorrow"
    />

    <!-- 续借对话框 -->
    <RenewDialog
      v-model="renewDialog.show"
      :record="renewDialog.record"
      :is-loading="renewDialog.loading"
      @confirm="handleConfirmRenew"
    />

    <!-- 借阅详情对话框 -->
    <BorrowDetailDialog
      v-model="detailDialog.show"
      :record="detailDialog.record"
    />

    <!-- 消息提示 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="snackbar.timeout"
      location="top"
    >
      {{ snackbar.message }}
      <template #actions>
        <v-btn variant="text" @click="snackbar.show = false">
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import BookCard from '@/components/books/BookCard.vue';
import BorrowRecordCard from '@/components/borrows/BorrowRecordCard.vue';
import BorrowDialog from '@/components/books/BorrowDialog.vue';
import RenewDialog from '@/components/borrows/RenewDialog.vue';
import BorrowDetailDialog from '@/components/borrows/BorrowDetailDialog.vue';
import type { Book, BorrowRecord } from '@/types';

// 测试图书数据
const testBooks: Book[] = [
  {
    id: 1,
    isbn: '9787020002207',
    title: '红楼梦',
    author: '曹雪芹',
    publisher: '人民文学出版社',
    publishDate: '2008-06-01',
    categoryId: 2,
    categoryName: '小说',
    description: '中国古典文学四大名著之一（已借过，测试借阅限制）',
    coverUrl: 'https://img3.doubanio.com/view/subject/l/public/s1070959.jpg',
    price: 45.00,
    totalQuantity: 10,
    availableQuantity: 9,
    status: 1,
    createdTime: '2024-01-01T00:00:00',
    updatedTime: '2024-01-01T00:00:00'
  },
  {
    id: 2,
    isbn: '9787111213826',
    title: 'Java核心技术卷I',
    author: 'Cay S. Horstmann',
    publisher: '机械工业出版社',
    publishDate: '2020-01-01',
    categoryId: 6,
    categoryName: '计算机',
    description: 'Java编程经典教材（可正常借阅）',
    coverUrl: 'https://img1.doubanio.com/view/subject/l/public/s29105625.jpg',
    price: 128.00,
    totalQuantity: 15,
    availableQuantity: 3,
    status: 1,
    createdTime: '2024-01-01T00:00:00',
    updatedTime: '2024-01-01T00:00:00'
  },
  {
    id: 3,
    isbn: '9787020033904',
    title: '平凡的世界',
    author: '路遥',
    publisher: '人民文学出版社',
    publishDate: '2012-03-01',
    categoryId: 2,
    categoryName: '小说',
    description: '茅盾文学奖获奖作品（已借过，测试借阅限制）',
    coverUrl: 'https://img3.doubanio.com/view/subject/l/public/s27279654.jpg',
    price: 75.00,
    totalQuantity: 20,
    availableQuantity: 0,
    status: 1,
    createdTime: '2024-01-01T00:00:00',
    updatedTime: '2024-01-01T00:00:00'
  },
  {
    id: 4,
    isbn: '9787040396638',
    title: '高等数学（第七版）',
    author: '同济大学数学系',
    publisher: '高等教育出版社',
    publishDate: '2014-07-01',
    categoryId: 7,
    categoryName: '数学',
    description: '高等数学教材（可正常借阅）',
    price: 56.00,
    totalQuantity: 25,
    availableQuantity: 25,
    status: 1,
    createdTime: '2024-01-01T00:00:00',
    updatedTime: '2024-01-01T00:00:00'
  }
];

// 测试借阅记录数据
const testBorrowRecords: BorrowRecord[] = [
  {
    id: 1,
    userId: 1,
    bookId: 2,
    borrowTime: '2024-11-01T10:00:00',
    dueTime: '2024-12-15T23:59:59', // 距离到期还有几天，可以续借
    dueDate: '2024-12-15', // 添加dueDate字段
    status: 'BORROWED',
    renewCount: 0,
    bookTitle: 'Java核心技术卷I',
    bookAuthor: 'Cay S. Horstmann',
    bookIsbn: '9787111213826',
    bookCoverUrl: 'https://img1.doubanio.com/view/subject/l/public/s29105625.jpg',
    createdTime: '2024-11-01T10:00:00',
    updatedTime: '2024-11-01T10:00:00'
  },
  {
    id: 2,
    userId: 1,
    bookId: 4,
    borrowTime: '2024-10-01T10:00:00',
    dueTime: '2024-12-20T23:59:59', // 距离到期还有几天，但已续借1次
    dueDate: '2024-12-20', // 添加dueDate字段
    status: 'BORROWED',
    renewCount: 1,
    bookTitle: '高等数学（第七版）',
    bookAuthor: '同济大学数学系',
    bookIsbn: '9787040396638',
    bookCoverUrl: 'https://img1.doubanio.com/view/subject/l/public/s29105625.jpg', // 添加封面URL
    createdTime: '2024-10-01T10:00:00',
    updatedTime: '2024-11-15T10:00:00'
  },
  {
    id: 3,
    userId: 1,
    bookId: 1,
    borrowTime: '2024-08-01T10:00:00',
    dueTime: '2024-12-10T23:59:59', // 借阅时间较长，已续借2次，无法再续借
    dueDate: '2024-12-10', // 添加dueDate字段
    returnTime: '2024-12-08T15:30:00',
    status: 'RETURNED',
    renewCount: 2,
    bookTitle: '红楼梦',
    bookAuthor: '曹雪芹',
    bookIsbn: '9787020002207',
    bookCoverUrl: 'https://img3.doubanio.com/view/subject/l/public/s1070959.jpg',
    createdTime: '2024-08-01T10:00:00',
    updatedTime: '2024-12-08T15:30:00'
  }
];

// 对话框状态
const borrowDialog = reactive({
  show: false,
  book: null as Book | null,
});

const renewDialog = reactive({
  show: false,
  record: null as BorrowRecord | null,
  loading: false,
});

const detailDialog = reactive({
  show: false,
  record: null as BorrowRecord | null,
});

const snackbar = reactive({
  show: false,
  message: '',
  color: 'success',
  timeout: 3000,
});

// 事件处理
const handleViewDetail = (book: Book) => {
  showMessage(`查看《${book.title}》详情`, 'info');
};

const handleBorrow = (book: Book) => {
  borrowDialog.book = book;
  borrowDialog.show = true;
};

const handleToggleFavorite = (book: Book) => {
  showMessage(`收藏《${book.title}》`, 'warning');
};

const handleConfirmBorrow = (data: { bookId: number; remarks?: string }) => {
  borrowDialog.show = false;
  const book = testBooks.find(b => b.id === data.bookId);
  showMessage(`借阅《${book?.title}》申请已提交`, 'success');
};

const handleReturn = (record: BorrowRecord) => {
  showMessage(`归还《${record.bookTitle}》申请已提交`, 'success');
};

const handleRenew = (record: BorrowRecord) => {
  renewDialog.record = record;
  renewDialog.show = true;
};

const handleConfirmRenew = (data: { borrowId: number; remarks?: string }) => {
  renewDialog.loading = true;
  setTimeout(() => {
    renewDialog.loading = false;
    renewDialog.show = false;
    const record = testBorrowRecords.find(r => r.id === data.borrowId);
    showMessage(`《${record?.bookTitle}》续借成功`, 'success');
  }, 1500);
};

const handleViewBorrowDetail = (record: BorrowRecord) => {
  detailDialog.record = record;
  detailDialog.show = true;
};

const showMessage = (message: string, color: string = 'success') => {
  snackbar.message = message;
  snackbar.color = color;
  snackbar.show = true;
};
</script>

<style scoped>
.borrow-test-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-info-card {
  border-radius: 16px !important;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(66, 165, 245, 0.02) 100%);
}

.test-list {
  padding-left: 20px;
}

.test-list li {
  margin-bottom: 4px;
}

.test-results {
  border-radius: 16px !important;
}

.test-status {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.gradient-text {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .borrow-test-container {
    padding: 16px;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .test-info-card {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.05) 0%, rgba(25, 118, 210, 0.05) 100%);
  }
}
</style>
