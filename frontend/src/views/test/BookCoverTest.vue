<template>
  <div class="book-cover-test-container">
    <div class="test-header mb-6">
      <h1 class="text-h4 font-weight-bold gradient-text">图书封面组件测试</h1>
      <p class="text-body-1 text-medium-emphasis mt-2">
        测试BookCover组件的各种显示情况
      </p>
    </div>

    <!-- 测试说明 -->
    <v-card class="test-info-card mb-6" elevation="2">
      <v-card-text>
        <h3 class="text-h6 mb-3">测试场景</h3>
        <v-row>
          <v-col cols="12" md="6">
            <h4 class="text-subtitle-1 mb-2">正常情况：</h4>
            <ul class="test-list">
              <li>有效的图片URL</li>
              <li>3:5宽高比显示</li>
              <li>contain模式完整显示</li>
              <li>响应式尺寸适配</li>
            </ul>
          </v-col>
          <v-col cols="12" md="6">
            <h4 class="text-subtitle-1 mb-2">异常情况：</h4>
            <ul class="test-list">
              <li>无效的图片URL</li>
              <li>网络加载失败</li>
              <li>跨域访问限制</li>
              <li>占位符文本显示</li>
            </ul>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 测试用例 -->
    <div class="test-cases">
      <h3 class="text-h6 mb-4">测试用例</h3>
      
      <v-row>
        <!-- 正常图片 -->
        <v-col cols="6" md="3">
          <v-card class="test-case-card" elevation="2">
            <v-card-title class="text-subtitle-2">正常图片</v-card-title>
            <v-card-text class="d-flex justify-center">
              <BookCover
                src="https://via.placeholder.com/120x200/1976d2/ffffff?text=正常图片"
                title="正常图片测试"
                width="100px"
                aspect-ratio="3/5"
                :icon-size="32"
                show-loading-text
              />
            </v-card-text>
          </v-card>
        </v-col>

        <!-- 无效URL -->
        <v-col cols="6" md="3">
          <v-card class="test-case-card" elevation="2">
            <v-card-title class="text-subtitle-2">无效URL</v-card-title>
            <v-card-text class="d-flex justify-center">
              <BookCover
                src="https://invalid-url.com/image.jpg"
                title="无效URL测试"
                width="100px"
                aspect-ratio="3/5"
                :icon-size="32"
                show-loading-text
              />
            </v-card-text>
          </v-card>
        </v-col>

        <!-- 空URL -->
        <v-col cols="6" md="3">
          <v-card class="test-case-card" elevation="2">
            <v-card-title class="text-subtitle-2">空URL</v-card-title>
            <v-card-text class="d-flex justify-center">
              <BookCover
                src=""
                title="空URL测试"
                width="100px"
                aspect-ratio="3/5"
                :icon-size="32"
                show-loading-text
              />
            </v-card-text>
          </v-card>
        </v-col>

        <!-- 长标题 -->
        <v-col cols="6" md="3">
          <v-card class="test-case-card" elevation="2">
            <v-card-title class="text-subtitle-2">长标题</v-card-title>
            <v-card-text class="d-flex justify-center">
              <BookCover
                src=""
                title="这是一个非常长的图书标题用于测试文本截断功能"
                width="100px"
                aspect-ratio="3/5"
                :icon-size="32"
                show-loading-text
              />
            </v-card-text>
          </v-card>
        </v-col>

        <!-- 豆瓣图片（可能跨域） -->
        <v-col cols="6" md="3">
          <v-card class="test-case-card" elevation="2">
            <v-card-title class="text-subtitle-2">豆瓣图片</v-card-title>
            <v-card-text class="d-flex justify-center">
              <BookCover
                src="https://img3.doubanio.com/view/subject/l/public/s1070959.jpg"
                title="红楼梦"
                width="100px"
                aspect-ratio="3/5"
                :icon-size="32"
                show-loading-text
              />
            </v-card-text>
          </v-card>
        </v-col>

        <!-- 不同尺寸 -->
        <v-col cols="6" md="3">
          <v-card class="test-case-card" elevation="2">
            <v-card-title class="text-subtitle-2">小尺寸</v-card-title>
            <v-card-text class="d-flex justify-center">
              <BookCover
                src="https://via.placeholder.com/60x100/42a5f5/ffffff?text=小"
                title="小尺寸测试"
                width="60px"
                aspect-ratio="3/5"
                :icon-size="16"
              />
            </v-card-text>
          </v-card>
        </v-col>

        <!-- 大尺寸 -->
        <v-col cols="6" md="3">
          <v-card class="test-case-card" elevation="2">
            <v-card-title class="text-subtitle-2">大尺寸</v-card-title>
            <v-card-text class="d-flex justify-center">
              <BookCover
                src="https://via.placeholder.com/150x250/4caf50/ffffff?text=大"
                title="大尺寸测试"
                width="150px"
                aspect-ratio="3/5"
                :icon-size="48"
                show-loading-text
              />
            </v-card-text>
          </v-card>
        </v-col>

        <!-- 中文标题 -->
        <v-col cols="6" md="3">
          <v-card class="test-case-card" elevation="2">
            <v-card-title class="text-subtitle-2">中文标题</v-card-title>
            <v-card-text class="d-flex justify-center">
              <BookCover
                src=""
                title="红楼梦"
                width="100px"
                aspect-ratio="3/5"
                :icon-size="32"
                show-loading-text
              />
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </div>

    <!-- 测试结果 -->
    <v-card class="test-results mt-6" elevation="2">
      <v-card-text>
        <h3 class="text-h6 mb-3">测试结果</h3>
        <div class="test-status">
          <v-chip color="success" class="me-2 mb-2">✓ 正常图片加载</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 错误处理机制</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 占位符显示</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 文本截断功能</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 响应式尺寸</v-chip>
          <v-chip color="success" class="mb-2">✓ 深色模式适配</v-chip>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import BookCover from '@/components/common/BookCover.vue';
</script>

<style scoped>
.book-cover-test-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-info-card {
  border-radius: 16px !important;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(66, 165, 245, 0.02) 100%);
}

.test-case-card {
  border-radius: 12px !important;
  height: 100%;
}

.test-list {
  padding-left: 20px;
}

.test-list li {
  margin-bottom: 4px;
}

.test-results {
  border-radius: 16px !important;
}

.test-status {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.gradient-text {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .book-cover-test-container {
    padding: 16px;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .test-info-card {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.05) 0%, rgba(25, 118, 210, 0.05) 100%);
  }
}
</style>
