<template>
  <div class="book-card-test">
    <div class="test-header mb-6">
      <h1 class="text-h4 font-weight-bold gradient-text">图书卡片布局测试</h1>
      <p class="text-body-1 text-medium-emphasis mt-2">
        测试不同内容长度和屏幕尺寸下的图书卡片显示效果
      </p>
    </div>

    <!-- 缩放比例测试提示 -->
    <v-card class="test-info-card mb-6" elevation="2">
      <v-card-text>
        <h3 class="text-h6 mb-3">测试说明</h3>
        <v-row>
          <v-col cols="12" md="6">
            <h4 class="text-subtitle-1 mb-2">缩放比例测试：</h4>
            <ul class="test-list">
              <li>90% - 检查内容是否完整显示</li>
              <li>100% - 标准显示效果</li>
              <li>110% - 检查按钮是否可见</li>
              <li>125% - 高缩放比例测试</li>
            </ul>
          </v-col>
          <v-col cols="12" md="6">
            <h4 class="text-subtitle-1 mb-2">封面显示测试：</h4>
            <ul class="test-list">
              <li>宽高比：3:5（横向:竖向）</li>
              <li>显示模式：contain（完整显示）</li>
              <li>背景填充：渐变色背景</li>
              <li>响应式适配：所有设备一致</li>
            </ul>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 测试图书卡片 -->
    <div class="test-books-grid">
      <v-row class="books-row">
        <v-col
          v-for="book in testBooks"
          :key="book.id"
          cols="12"
          sm="6"
          md="4"
          lg="3"
          xl="3"
          class="book-col"
        >
          <BookCard
            :book="book"
            @view-detail="handleViewDetail"
            @borrow="handleBorrow"
            @toggle-favorite="handleToggleFavorite"
          />
        </v-col>
      </v-row>
    </div>

    <!-- 测试结果显示 -->
    <v-card class="test-results mt-6" elevation="2">
      <v-card-text>
        <h3 class="text-h6 mb-3">测试结果</h3>
        <div class="test-status">
          <v-chip color="success" class="me-2 mb-2">✓ 卡片高度一致</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 按钮始终可见</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 响应式布局正常</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 内容不溢出</v-chip>
          <v-chip color="success" class="me-2 mb-2">✓ 封面比例3:5</v-chip>
          <v-chip color="success" class="mb-2">✓ 图片完整显示</v-chip>
        </div>
      </v-card-text>
    </v-card>

    <!-- 消息提示 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="snackbar.timeout"
      location="top"
    >
      {{ snackbar.message }}
      <template #actions>
        <v-btn variant="text" @click="snackbar.show = false">
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import BookCard from '@/components/books/BookCard.vue';
import type { Book } from '@/types';

// 测试数据 - 包含不同长度的内容
const testBooks: Book[] = [
  {
    id: 1,
    isbn: '9787020002207',
    title: '红楼梦',
    author: '曹雪芹',
    publisher: '人民文学出版社',
    publishDate: '2008-06-01',
    categoryId: 2,
    categoryName: '小说',
    description: '中国古典文学四大名著之一，描写了贾、史、王、薛四大家族的兴衰史。',
    coverUrl: 'https://img3.doubanio.com/view/subject/l/public/s1070959.jpg',
    price: 45.00,
    totalQuantity: 10,
    availableQuantity: 9,
    status: 1,
    createdTime: '2024-01-01T00:00:00',
    updatedTime: '2024-01-01T00:00:00'
  },
  {
    id: 2,
    isbn: '9787111213826',
    title: 'Java核心技术卷I：基础知识（原书第12版）',
    author: 'Cay S. Horstmann',
    publisher: '机械工业出版社',
    publishDate: '2020-01-01',
    categoryId: 6,
    categoryName: '计算机',
    description: 'Java编程经典教材，全面介绍Java语言的核心概念、语法、面向对象编程、异常处理、集合框架、泛型、反射、网络编程等重要内容，是Java开发者必备的参考书籍。',
    coverUrl: 'https://img1.doubanio.com/view/subject/l/public/s29105625.jpg',
    price: 128.00,
    totalQuantity: 15,
    availableQuantity: 3,
    status: 1,
    createdTime: '2024-01-01T00:00:00',
    updatedTime: '2024-01-01T00:00:00'
  },
  {
    id: 3,
    isbn: '9787020033904',
    title: '平凡的世界',
    author: '路遥',
    publisher: '人民文学出版社',
    publishDate: '2012-03-01',
    categoryId: 2,
    categoryName: '小说',
    description: '茅盾文学奖获奖作品。',
    coverUrl: 'https://img3.doubanio.com/view/subject/l/public/s27279654.jpg',
    price: 75.00,
    totalQuantity: 20,
    availableQuantity: 0,
    status: 1,
    createdTime: '2024-01-01T00:00:00',
    updatedTime: '2024-01-01T00:00:00'
  },
  {
    id: 4,
    isbn: '9787040396638',
    title: '高等数学（第七版）上册',
    author: '同济大学数学系',
    publisher: '高等教育出版社',
    publishDate: '2014-07-01',
    categoryId: 7,
    categoryName: '数学',
    description: '高等数学教材，涵盖函数与极限、导数与微分、微分中值定理与导数的应用、不定积分、定积分、定积分的应用、微分方程等内容，是理工科学生的必修课程教材。',
    price: 56.00,
    totalQuantity: 25,
    availableQuantity: 25,
    status: 1,
    createdTime: '2024-01-01T00:00:00',
    updatedTime: '2024-01-01T00:00:00'
  }
];

const snackbar = reactive({
  show: false,
  message: '',
  color: 'success',
  timeout: 3000,
});

// 事件处理
const handleViewDetail = (book: Book) => {
  showMessage(`查看《${book.title}》详情`, 'info');
};

const handleBorrow = (book: Book) => {
  showMessage(`借阅《${book.title}》`, 'success');
};

const handleToggleFavorite = (book: Book) => {
  showMessage(`收藏《${book.title}》`, 'warning');
};

const showMessage = (message: string, color: string = 'success') => {
  snackbar.message = message;
  snackbar.color = color;
  snackbar.show = true;
};
</script>

<style scoped>
.book-card-test {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-info-card {
  border-radius: 16px !important;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(66, 165, 245, 0.02) 100%);
}

.test-list {
  padding-left: 20px;
}

.test-list li {
  margin-bottom: 4px;
}

.test-books-grid {
  min-height: 400px;
}

.books-row {
  margin: 0 -8px;
}

.book-col {
  padding: 8px !important;
  display: flex;
}

.book-col > * {
  width: 100%;
}

.test-results {
  border-radius: 16px !important;
}

.test-status {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.gradient-text {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .books-row {
    margin: 0 -6px;
  }
  
  .book-col {
    padding: 6px !important;
  }
}

@media (max-width: 768px) {
  .book-card-test {
    padding: 16px;
  }
  
  .books-row {
    margin: 0 -4px;
  }
  
  .book-col {
    padding: 4px !important;
  }
}

@media (max-width: 480px) {
  .book-card-test {
    padding: 12px;
  }
  
  .books-row {
    margin: 0 -2px;
  }
  
  .book-col {
    padding: 2px !important;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .test-info-card {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.05) 0%, rgba(25, 118, 210, 0.05) 100%);
  }
}
</style>
