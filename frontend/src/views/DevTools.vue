<template>
  <div class="dev-tools-container">
    <!-- 页面头部 -->
    <div class="page-header mb-6">
      <h1 class="text-h4 font-weight-bold gradient-text">开发工具</h1>
      <p class="text-body-1 text-medium-emphasis mt-2">
        用于测试系统功能和组件的开发工具页面
      </p>
    </div>

    <v-row>
      <!-- 消息测试 -->
      <v-col cols="12" md="6">
        <v-card class="test-card" elevation="2">
          <v-card-title class="card-header">
            <v-icon class="me-2">mdi-message</v-icon>
            消息测试
          </v-card-title>
          <v-card-text>
            <div class="d-flex flex-column gap-3">
              <v-btn
                color="success"
                variant="elevated"
                @click="testSuccessMessage"
                block
              >
                成功消息
              </v-btn>
              <v-btn
                color="error"
                variant="elevated"
                @click="testErrorMessage"
                block
              >
                错误消息
              </v-btn>
              <v-btn
                color="warning"
                variant="elevated"
                @click="testWarningMessage"
                block
              >
                警告消息
              </v-btn>
              <v-btn
                color="info"
                variant="elevated"
                @click="testInfoMessage"
                block
              >
                信息消息
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 确认对话框测试 -->
      <v-col cols="12" md="6">
        <v-card class="test-card" elevation="2">
          <v-card-title class="card-header">
            <v-icon class="me-2">mdi-help-circle</v-icon>
            确认对话框测试
          </v-card-title>
          <v-card-text>
            <div class="d-flex flex-column gap-3">
              <v-btn
                color="error"
                variant="elevated"
                @click="testDeleteConfirm"
                block
              >
                删除确认
              </v-btn>
              <v-btn
                color="warning"
                variant="elevated"
                @click="testActionConfirm"
                block
              >
                操作确认
              </v-btn>
              <v-btn
                color="info"
                variant="elevated"
                @click="testInfoConfirm"
                block
              >
                信息确认
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 加载测试 -->
      <v-col cols="12" md="6">
        <v-card class="test-card" elevation="2">
          <v-card-title class="card-header">
            <v-icon class="me-2">mdi-loading</v-icon>
            加载测试
          </v-card-title>
          <v-card-text>
            <div class="d-flex flex-column gap-3">
              <v-btn
                color="primary"
                variant="elevated"
                @click="testSimpleLoading"
                block
              >
                简单加载
              </v-btn>
              <v-btn
                color="primary"
                variant="elevated"
                @click="testProgressLoading"
                block
              >
                进度加载
              </v-btn>
              <v-btn
                color="primary"
                variant="elevated"
                @click="testTimeoutLoading"
                block
              >
                超时加载
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 错误处理测试 -->
      <v-col cols="12" md="6">
        <v-card class="test-card" elevation="2">
          <v-card-title class="card-header">
            <v-icon class="me-2">mdi-bug</v-icon>
            错误处理测试
          </v-card-title>
          <v-card-text>
            <div class="d-flex flex-column gap-3">
              <v-btn
                color="error"
                variant="elevated"
                @click="testNetworkError"
                block
              >
                网络错误
              </v-btn>
              <v-btn
                color="error"
                variant="elevated"
                @click="testValidationError"
                block
              >
                验证错误
              </v-btn>
              <v-btn
                color="error"
                variant="elevated"
                @click="testUnknownError"
                block
              >
                未知错误
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 权限测试 -->
      <v-col cols="12" md="6">
        <v-card class="test-card" elevation="2">
          <v-card-title class="card-header">
            <v-icon class="me-2">mdi-shield-account</v-icon>
            权限测试
          </v-card-title>
          <v-card-text>
            <div class="mb-3">
              <p class="text-body-2">当前用户：{{ userStore.userInfo?.realName }}</p>
              <p class="text-body-2">当前角色：{{ getRoleDisplayName(userStore.userInfo?.role || '') }}</p>
            </div>
            <div class="d-flex flex-column gap-3">
              <v-btn
                v-permission="['USER']"
                color="primary"
                variant="elevated"
                block
              >
                用户权限按钮
              </v-btn>
              <v-btn
                v-permission="['ADMIN']"
                color="warning"
                variant="elevated"
                block
              >
                管理员权限按钮
              </v-btn>
              <v-btn
                v-permission="['SUPER_ADMIN']"
                color="error"
                variant="elevated"
                block
              >
                超级管理员权限按钮
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 模拟数据测试 -->
      <v-col cols="12" md="6">
        <v-card class="test-card" elevation="2">
          <v-card-title class="card-header">
            <v-icon class="me-2">mdi-database</v-icon>
            模拟数据测试
          </v-card-title>
          <v-card-text>
            <div class="d-flex flex-column gap-3">
              <v-btn
                color="info"
                variant="elevated"
                @click="showMockUsers"
                block
              >
                查看模拟用户
              </v-btn>
              <v-btn
                color="info"
                variant="elevated"
                @click="showMockBooks"
                block
              >
                查看模拟图书
              </v-btn>
              <v-btn
                color="info"
                variant="elevated"
                @click="showMockBorrows"
                block
              >
                查看模拟借阅
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 数据展示对话框 -->
    <v-dialog v-model="dataDialog.show" max-width="800">
      <v-card>
        <v-card-title>{{ dataDialog.title }}</v-card-title>
        <v-card-text>
          <pre class="data-display">{{ dataDialog.data }}</pre>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="dataDialog.show = false">关闭</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { useUserStore } from '@/stores/user';
import { useMessageStore } from '@/stores/message';
import { useConfirmStore } from '@/stores/confirm';
import { useLoadingStore } from '@/stores/loading';
import { errorHandler, createAppError, ERROR_CODES } from '@/utils/errorHandler';
import { getRoleDisplayName } from '@/utils/permission';
import { mockUsers, mockBooks, mockBorrowRecords } from '@/utils/mockData';

const userStore = useUserStore();
const messageStore = useMessageStore();
const confirmStore = useConfirmStore();
const loadingStore = useLoadingStore();

// 数据展示对话框
const dataDialog = reactive({
  show: false,
  title: '',
  data: '',
});

// 消息测试
const testSuccessMessage = () => {
  messageStore.success('这是一个成功消息！');
};

const testErrorMessage = () => {
  messageStore.error('这是一个错误消息！');
};

const testWarningMessage = () => {
  messageStore.warning('这是一个警告消息！');
};

const testInfoMessage = () => {
  messageStore.info('这是一个信息消息！');
};

// 确认对话框测试
const testDeleteConfirm = async () => {
  const confirmed = await confirmStore.confirmDelete('测试项目', async () => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    messageStore.success('删除成功！');
  });
  console.log('删除确认结果:', confirmed);
};

const testActionConfirm = async () => {
  const confirmed = await confirmStore.confirmAction('重置', '确定要重置所有设置吗？', async () => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    messageStore.success('重置成功！');
  });
  console.log('操作确认结果:', confirmed);
};

const testInfoConfirm = async () => {
  const confirmed = await confirmStore.confirmInfo(
    '系统信息',
    '当前系统版本为 v1.0.0，是否查看更新日志？',
    JSON.stringify({ version: '1.0.0', date: '2024-01-15' }, null, 2)
  );
  console.log('信息确认结果:', confirmed);
};

// 加载测试
const testSimpleLoading = () => {
  loadingStore.showWithMessage('正在处理...', 3000);
};

const testProgressLoading = () => {
  loadingStore.showWithProgress('正在上传文件...', 0);
  
  let progress = 0;
  const interval = setInterval(() => {
    progress += 10;
    loadingStore.updateProgress(progress, `正在上传文件... ${progress}%`);
    
    if (progress >= 100) {
      clearInterval(interval);
      setTimeout(() => {
        loadingStore.hide();
        messageStore.success('文件上传完成！');
      }, 500);
    }
  }, 200);
};

const testTimeoutLoading = () => {
  loadingStore.showLoading({ message: '自动关闭加载...', timeout: 2000 });
};

// 错误处理测试
const testNetworkError = () => {
  const error = createAppError(ERROR_CODES.NETWORK_ERROR);
  errorHandler.handleError(error);
};

const testValidationError = () => {
  const error = createAppError(ERROR_CODES.VALIDATION_ERROR, '用户名不能为空');
  errorHandler.handleError(error);
};

const testUnknownError = () => {
  const error = new Error('这是一个未知错误');
  errorHandler.handleError(error);
};

// 模拟数据展示
const showMockUsers = () => {
  dataDialog.title = '模拟用户数据';
  dataDialog.data = JSON.stringify(mockUsers, null, 2);
  dataDialog.show = true;
};

const showMockBooks = () => {
  dataDialog.title = '模拟图书数据';
  dataDialog.data = JSON.stringify(mockBooks, null, 2);
  dataDialog.show = true;
};

const showMockBorrows = () => {
  dataDialog.title = '模拟借阅数据';
  dataDialog.data = JSON.stringify(mockBorrowRecords, null, 2);
  dataDialog.show = true;
};
</script>

<style scoped>
.dev-tools-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.test-card {
  border-radius: 16px !important;
  height: 100%;
}

.card-header {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 100%);
  font-weight: 600;
}

.data-display {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
  background: rgba(0, 0, 0, 0.05);
  padding: 16px;
  border-radius: 8px;
  max-height: 400px;
  overflow-y: auto;
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .card-header {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  }
  
  .data-display {
    background: rgba(255, 255, 255, 0.05);
    color: #e0e0e0;
  }
}
</style>
