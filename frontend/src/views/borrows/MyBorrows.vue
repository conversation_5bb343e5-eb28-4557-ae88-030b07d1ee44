<template>
  <div class="my-borrows-container">
    <!-- 页面头部 -->
    <div class="page-header mb-6">
      <h1 class="text-h4 font-weight-bold gradient-text">我的借阅</h1>
      <p class="text-body-1 text-medium-emphasis mt-2">
        管理您的图书借阅记录
      </p>
    </div>

    <!-- 借阅统计 -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card class="stats-card" elevation="2">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="primary" class="mb-2">mdi-book-clock</v-icon>
            <h3 class="text-h5 font-weight-bold">{{ myStats.total }}</h3>
            <p class="text-body-2 text-medium-emphasis">总借阅</p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="stats-card" elevation="2">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="success" class="mb-2">mdi-book-check</v-icon>
            <h3 class="text-h5 font-weight-bold">{{ myStats.borrowed }}</h3>
            <p class="text-body-2 text-medium-emphasis">借阅中</p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="stats-card" elevation="2">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="info" class="mb-2">mdi-book-arrow-left</v-icon>
            <h3 class="text-h5 font-weight-bold">{{ myStats.returned }}</h3>
            <p class="text-body-2 text-medium-emphasis">已归还</p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="stats-card" elevation="2">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="error" class="mb-2">mdi-book-alert</v-icon>
            <h3 class="text-h5 font-weight-bold">{{ myStats.overdue }}</h3>
            <p class="text-body-2 text-medium-emphasis">逾期未还</p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 筛选选项 -->
    <v-card class="filter-card mb-6" elevation="2">
      <v-card-text>
        <v-row align="center">
          <v-col cols="12" md="4">
            <v-text-field
              v-model="searchForm.keyword"
              label="搜索图书"
              placeholder="输入图书名或作者"
              prepend-inner-icon="mdi-magnify"
              variant="outlined"
              density="compact"
              hide-details
              clearable
              @keyup.enter="handleSearch"
              @click:clear="handleClearSearch"
            />
          </v-col>

          <v-col cols="12" md="3">
            <v-select
              v-model="searchForm.status"
              :items="statusOptions"
              label="借阅状态"
              variant="outlined"
              density="compact"
              hide-details
              clearable
              @update:model-value="handleSearch"
            />
          </v-col>

          <v-col cols="12" md="5">
            <v-btn
              color="primary"
              variant="elevated"
              prepend-icon="mdi-magnify"
              @click="handleSearch"
              class="me-2"
            >
              搜索
            </v-btn>
            <v-btn
              variant="outlined"
              prepend-icon="mdi-refresh"
              @click="handleReset"
            >
              重置
            </v-btn>
            <v-btn
              variant="outlined"
              prepend-icon="mdi-book-search"
              @click="$router.push('/books')"
              class="ml-2"
            >
              去借书
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 借阅记录列表 -->
    <div class="borrow-records">
      <!-- 加载状态 -->
      <div v-if="borrowStore.isLoading" class="loading-section">
        <v-row>
          <v-col v-for="n in 6" :key="n" cols="12" md="6" lg="4">
            <BorrowRecordSkeleton />
          </v-col>
        </v-row>
      </div>

      <!-- 借阅记录卡片 -->
      <div v-else-if="borrowStore.myBorrowRecords.length > 0">
        <v-row>
          <v-col
            v-for="record in borrowStore.myBorrowRecords"
            :key="record.id"
            cols="12"
            md="6"
            lg="4"
          >
            <BorrowRecordCard
              :record="record"
              @return="handleReturn"
              @renew="handleRenew"
              @view-detail="handleViewDetail"
            />
          </v-col>
        </v-row>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-section">
        <v-card class="text-center pa-8" variant="tonal">
          <v-icon size="64" color="grey-lighten-1" class="mb-4">
            mdi-book-search
          </v-icon>
          <h3 class="text-h6 mb-2">暂无借阅记录</h3>
          <p class="text-body-2 text-medium-emphasis mb-4">
            {{ searchForm.keyword ? '没有找到匹配的借阅记录' : '您还没有借阅过任何图书' }}
          </p>
          <v-btn
            v-if="!searchForm.keyword"
            variant="outlined"
            prepend-icon="mdi-book-search"
            @click="$router.push('/books')"
          >
            去借书
          </v-btn>
          <v-btn
            v-else
            variant="outlined"
            @click="handleReset"
          >
            清除搜索条件
          </v-btn>
        </v-card>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="borrowStore.pagination.total > 0" class="pagination-section mt-6">
      <v-pagination
        v-model="currentPage"
        :length="borrowStore.pagination.pages"
        :total-visible="7"
        @update:model-value="handlePageChange"
        class="justify-center"
      />

      <div class="text-center mt-4 text-body-2 text-medium-emphasis">
        共 {{ borrowStore.pagination.total }} 条记录，
        第 {{ borrowStore.pagination.current }} / {{ borrowStore.pagination.pages }} 页
      </div>
    </div>

    <!-- 借阅详情对话框 -->
    <BorrowDetailDialog
      v-model="detailDialog.show"
      :record="detailDialog.record"
    />

    <!-- 续借确认对话框 -->
    <RenewDialog
      v-model="renewDialog.show"
      :record="renewDialog.record"
      :is-loading="renewDialog.loading"
      @confirm="handleConfirmRenew"
    />

    <!-- 消息提示 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="snackbar.timeout"
      location="top"
    >
      {{ snackbar.message }}
      <template #actions>
        <v-btn variant="text" @click="snackbar.show = false">
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useBorrowStore } from '@/stores/borrow';
import BorrowRecordCard from '@/components/borrows/BorrowRecordCard.vue';
import BorrowRecordSkeleton from '@/components/borrows/BorrowRecordSkeleton.vue';
import BorrowDetailDialog from '@/components/borrows/BorrowDetailDialog.vue';
import RenewDialog from '@/components/borrows/RenewDialog.vue';
import type { BorrowRecord } from '@/types';

const borrowStore = useBorrowStore();

// 响应式状态
const currentPage = ref(1);
const searchForm = reactive({
  keyword: '',
  status: null as string | null,
});

const detailDialog = reactive({
  show: false,
  record: null as BorrowRecord | null,
});

const renewDialog = reactive({
  show: false,
  record: null as BorrowRecord | null,
  loading: false,
});

const snackbar = reactive({
  show: false,
  message: '',
  color: 'success',
  timeout: 3000,
});

// 我的借阅统计
const myStats = reactive({
  total: 0,
  borrowed: 0,
  returned: 0,
  overdue: 0,
});

// 状态选项
const statusOptions = [
  { title: '全部状态', value: null },
  { title: '借阅中', value: 'BORROWED' },
  { title: '已归还', value: 'RETURNED' },
  { title: '逾期', value: 'OVERDUE' },
  { title: '续借', value: 'RENEWED' },
];

// 处理搜索
const handleSearch = async () => {
  currentPage.value = 1;
  await fetchMyBorrowRecords();
};

// 处理重置
const handleReset = async () => {
  searchForm.keyword = '';
  searchForm.status = null;
  currentPage.value = 1;
  await fetchMyBorrowRecords();
};

// 处理清除搜索
const handleClearSearch = async () => {
  searchForm.keyword = '';
  await handleSearch();
};

// 处理页码变化
const handlePageChange = async (page: number) => {
  currentPage.value = page;
  await fetchMyBorrowRecords();
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

// 获取我的借阅记录
const fetchMyBorrowRecords = async () => {
  try {
    await borrowStore.fetchMyBorrowList({
      current: currentPage.value,
      size: 12,
      keyword: searchForm.keyword || undefined,
      status: searchForm.status || undefined,
    });

    // 更新统计数据
    updateStats();
  } catch (error) {
    console.error('获取借阅记录失败:', error);
    showMessage('获取借阅记录失败', 'error');
  }
};

// 更新统计数据
const updateStats = () => {
  const records = borrowStore.myBorrowRecords;
  myStats.total = borrowStore.pagination.total;
  myStats.borrowed = records.filter(r => r.status === 'BORROWED').length;
  myStats.returned = records.filter(r => r.status === 'RETURNED').length;
  myStats.overdue = records.filter(r => r.status === 'OVERDUE').length;
};

// 处理归还
const handleReturn = async (record: BorrowRecord) => {
  try {
    await borrowStore.returnBookAction(record.id);
    showMessage('归还申请提交成功', 'success');
    await fetchMyBorrowRecords();
  } catch (error: any) {
    console.error('归还失败:', error);
    showMessage(error.message || '归还失败', 'error');
  }
};

// 处理续借
const handleRenew = (record: BorrowRecord) => {
  renewDialog.record = record;
  renewDialog.show = true;
};

// 处理确认续借
const handleConfirmRenew = async (data: { borrowId: number; remarks?: string }) => {
  try {
    renewDialog.loading = true;
    await borrowStore.renewBookAction(data.borrowId);
    renewDialog.show = false;
    showMessage('续借成功', 'success');
    await fetchMyBorrowRecords();
  } catch (error: any) {
    console.error('续借失败:', error);
    showMessage(error.message || '续借失败', 'error');
  } finally {
    renewDialog.loading = false;
  }
};

// 处理查看详情
const handleViewDetail = (record: BorrowRecord) => {
  detailDialog.record = record;
  detailDialog.show = true;
};

// 显示消息
const showMessage = (message: string, color: string = 'success') => {
  snackbar.message = message;
  snackbar.color = color;
  snackbar.show = true;
};

// 组件挂载
onMounted(async () => {
  await fetchMyBorrowRecords();
});
</script>

<style scoped>
.my-borrows-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.stats-card {
  border-radius: 16px !important;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(25, 118, 210, 0.2) !important;
}

.filter-card {
  border-radius: 16px !important;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(66, 165, 245, 0.02) 100%);
}

.borrow-records {
  min-height: 400px;
}

.loading-section,
.empty-section {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .my-borrows-container {
    padding: 16px;
  }

  .page-header h1 {
    font-size: 1.8rem !important;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .filter-card {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.05) 0%, rgba(25, 118, 210, 0.05) 100%);
  }
}
</style>
