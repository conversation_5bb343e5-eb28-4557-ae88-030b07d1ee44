<template>
  <div class="book-management-container">
    <v-card class="placeholder-card" elevation="2">
      <v-card-text class="text-center pa-8">
        <v-icon size="64" color="primary" class="mb-4">
          mdi-book-multiple
        </v-icon>
        <h2 class="text-h5 font-weight-bold mb-2">图书管理</h2>
        <p class="text-body-1 text-medium-emphasis mb-4">
          此功能正在开发中，敬请期待
        </p>
        <v-btn
          variant="outlined"
          prepend-icon="mdi-arrow-left"
          @click="$router.push('/admin')"
        >
          返回管理后台
        </v-btn>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
// 占位组件
</script>

<style scoped>
.book-management-container {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.placeholder-card {
  border-radius: 16px !important;
}
</style>
