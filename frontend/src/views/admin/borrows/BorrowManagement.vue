<template>
  <div class="borrow-management-container">
    <!-- 页面头部 -->
    <div class="page-header mb-6">
      <div class="d-flex justify-space-between align-center">
        <div>
          <h1 class="text-h4 font-weight-bold gradient-text">借阅管理</h1>
          <p class="text-body-1 text-medium-emphasis mt-2">
            管理图书借阅记录和归还处理
          </p>
        </div>
        <div class="header-actions">
          <v-btn
            color="warning"
            variant="elevated"
            prepend-icon="mdi-alert-circle"
            @click="handleProcessOverdue"
            :loading="isProcessingOverdue"
            class="me-2"
          >
            处理逾期
          </v-btn>
          <v-btn
            color="primary"
            variant="elevated"
            prepend-icon="mdi-refresh"
            @click="handleRefresh"
            :loading="borrowStore.isLoading"
          >
            刷新数据
          </v-btn>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card class="stats-card" elevation="2">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="primary" class="mb-2">mdi-book-clock</v-icon>
            <h3 class="text-h5 font-weight-bold">{{ stats.total }}</h3>
            <p class="text-body-2 text-medium-emphasis">总借阅数</p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="stats-card" elevation="2">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="success" class="mb-2">mdi-book-check</v-icon>
            <h3 class="text-h5 font-weight-bold">{{ stats.borrowed }}</h3>
            <p class="text-body-2 text-medium-emphasis">借阅中</p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="stats-card" elevation="2">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="info" class="mb-2">mdi-book-arrow-left</v-icon>
            <h3 class="text-h5 font-weight-bold">{{ stats.returned }}</h3>
            <p class="text-body-2 text-medium-emphasis">已归还</p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="stats-card" elevation="2">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="error" class="mb-2">mdi-book-alert</v-icon>
            <h3 class="text-h5 font-weight-bold">{{ stats.overdue }}</h3>
            <p class="text-body-2 text-medium-emphasis">逾期未还</p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 搜索和筛选 -->
    <v-card class="search-card mb-6" elevation="2">
      <v-card-text>
        <v-row align="center">
          <v-col cols="12" md="3">
            <v-text-field
              v-model="searchForm.keyword"
              label="搜索"
              placeholder="输入用户名或图书名"
              prepend-inner-icon="mdi-magnify"
              variant="outlined"
              density="compact"
              hide-details
              clearable
              @keyup.enter="handleSearch"
              @click:clear="handleClearSearch"
            />
          </v-col>

          <v-col cols="12" md="2">
            <v-select
              v-model="searchForm.status"
              :items="statusOptions"
              label="借阅状态"
              variant="outlined"
              density="compact"
              hide-details
              clearable
              @update:model-value="handleSearch"
            />
          </v-col>

          <v-col cols="12" md="2">
            <v-text-field
              v-model="searchForm.startDate"
              label="开始日期"
              type="date"
              variant="outlined"
              density="compact"
              hide-details
              @update:model-value="handleSearch"
            />
          </v-col>

          <v-col cols="12" md="2">
            <v-text-field
              v-model="searchForm.endDate"
              label="结束日期"
              type="date"
              variant="outlined"
              density="compact"
              hide-details
              @update:model-value="handleSearch"
            />
          </v-col>

          <v-col cols="12" md="3">
            <v-btn
              color="primary"
              variant="elevated"
              prepend-icon="mdi-magnify"
              @click="handleSearch"
              class="me-2"
            >
              搜索
            </v-btn>
            <v-btn
              variant="outlined"
              prepend-icon="mdi-refresh"
              @click="handleReset"
            >
              重置
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 借阅记录列表 -->
    <v-card class="borrows-table-card" elevation="2">
      <v-card-title class="table-header">
        <v-icon class="me-2">mdi-book-clock</v-icon>
        借阅记录
        <v-spacer />
        <v-chip
          v-if="borrowStore.pagination.total > 0"
          color="primary"
          variant="tonal"
          size="small"
        >
          共 {{ borrowStore.pagination.total }} 条记录
        </v-chip>
      </v-card-title>

      <!-- 数据表格 -->
      <v-data-table
        :headers="tableHeaders"
        :items="borrowStore.borrowRecords"
        :loading="borrowStore.isLoading"
        :items-per-page="borrowStore.pagination.size"
        hide-default-footer
        class="borrows-table"
      >
        <!-- 用户信息列 -->
        <template #item.userInfo="{ item }">
          <div class="d-flex align-center">
            <v-avatar size="32" class="me-2">
              <v-img
                v-if="item.user?.avatar"
                :src="item.user.avatar"
                :alt="item.user.username"
              />
              <v-icon v-else size="20">mdi-account-circle</v-icon>
            </v-avatar>
            <div>
              <div class="font-weight-medium">{{ item.user?.realName || item.user?.username }}</div>
              <div class="text-body-2 text-medium-emphasis">@{{ item.user?.username }}</div>
            </div>
          </div>
        </template>

        <!-- 图书信息列 -->
        <template #item.bookInfo="{ item }">
          <div class="d-flex align-center">
            <v-img
              :src="item.book?.coverUrl || getDefaultCover(item.book?.title)"
              :alt="item.book?.title"
              width="40"
              height="50"
              class="me-2 rounded"
            >
              <template #placeholder>
                <div class="d-flex align-center justify-center fill-height">
                  <v-icon size="20">mdi-book</v-icon>
                </div>
              </template>
            </v-img>
            <div>
              <div class="font-weight-medium">{{ item.book?.title }}</div>
              <div class="text-body-2 text-medium-emphasis">{{ item.book?.author }}</div>
            </div>
          </div>
        </template>

        <!-- 状态列 -->
        <template #item.status="{ item }">
          <v-chip
            :color="getStatusColor(item.status)"
            variant="tonal"
            size="small"
          >
            {{ getStatusText(item.status) }}
          </v-chip>
        </template>

        <!-- 借阅日期列 -->
        <template #item.borrowDate="{ item }">
          <span class="text-body-2">
            {{ formatDate(item.borrowDate) }}
          </span>
        </template>

        <!-- 应还日期列 -->
        <template #item.dueDate="{ item }">
          <span
            class="text-body-2"
            :class="{ 'text-error': isOverdue(item.dueDate, item.status) }"
          >
            {{ formatDate(item.dueDate) }}
            <v-icon
              v-if="isOverdue(item.dueDate, item.status)"
              size="16"
              color="error"
              class="ml-1"
            >
              mdi-alert-circle
            </v-icon>
          </span>
        </template>

        <!-- 实际归还日期列 -->
        <template #item.returnDate="{ item }">
          <span v-if="item.returnDate" class="text-body-2">
            {{ formatDate(item.returnDate) }}
          </span>
          <span v-else class="text-body-2 text-medium-emphasis">
            未归还
          </span>
        </template>

        <!-- 操作列 -->
        <template #item.actions="{ item }">
          <div class="action-buttons">
            <v-btn
              icon
              size="small"
              variant="text"
              @click="handleViewDetail(item)"
            >
              <v-icon>mdi-eye</v-icon>
              <v-tooltip activator="parent" location="top">查看详情</v-tooltip>
            </v-btn>

            <v-btn
              v-if="item.status === 'BORROWED'"
              icon
              size="small"
              variant="text"
              color="success"
              @click="handleReturn(item)"
            >
              <v-icon>mdi-book-arrow-left</v-icon>
              <v-tooltip activator="parent" location="top">归还</v-tooltip>
            </v-btn>

            <v-btn
              v-if="item.status === 'BORROWED' && canRenew(item)"
              icon
              size="small"
              variant="text"
              color="info"
              @click="handleRenew(item)"
            >
              <v-icon>mdi-book-refresh</v-icon>
              <v-tooltip activator="parent" location="top">续借</v-tooltip>
            </v-btn>
          </div>
        </template>

        <!-- 空状态 -->
        <template #no-data>
          <div class="text-center pa-8">
            <v-icon size="64" color="grey-lighten-1" class="mb-4">
              mdi-book-search
            </v-icon>
            <h3 class="text-h6 mb-2">暂无借阅记录</h3>
            <p class="text-body-2 text-medium-emphasis">
              {{ searchForm.keyword ? '没有找到匹配的借阅记录' : '暂时没有借阅记录' }}
            </p>
          </div>
        </template>
      </v-data-table>

      <!-- 分页 -->
      <v-card-actions v-if="borrowStore.pagination.total > 0" class="pagination-section">
        <v-spacer />
        <div class="d-flex align-center gap-4">
          <span class="text-body-2 text-medium-emphasis">
            第 {{ borrowStore.pagination.current }} / {{ borrowStore.pagination.pages }} 页，
            共 {{ borrowStore.pagination.total }} 条记录
          </span>
          <v-pagination
            v-model="currentPage"
            :length="borrowStore.pagination.pages"
            :total-visible="5"
            size="small"
            @update:model-value="handlePageChange"
          />
        </div>
      </v-card-actions>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useBorrowStore } from '@/stores/borrow';
import type { BorrowRecord } from '@/types';

const borrowStore = useBorrowStore();

// 响应式状态
const currentPage = ref(1);
const isProcessingOverdue = ref(false);

const searchForm = reactive({
  keyword: '',
  status: null as string | null,
  startDate: '',
  endDate: '',
});

// 统计数据
const stats = reactive({
  total: 0,
  borrowed: 0,
  returned: 0,
  overdue: 0,
});

// 表格头部配置
const tableHeaders = [
  { title: '用户', key: 'userInfo', sortable: false },
  { title: '图书', key: 'bookInfo', sortable: false },
  { title: '状态', key: 'status', sortable: true },
  { title: '借阅日期', key: 'borrowDate', sortable: true },
  { title: '应还日期', key: 'dueDate', sortable: true },
  { title: '归还日期', key: 'returnDate', sortable: true },
  { title: '操作', key: 'actions', sortable: false, width: '150px' },
];

// 状态选项
const statusOptions = [
  { title: '全部状态', value: null },
  { title: '借阅中', value: 'BORROWED' },
  { title: '已归还', value: 'RETURNED' },
  { title: '逾期', value: 'OVERDUE' },
  { title: '续借', value: 'RENEWED' },
];

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    BORROWED: 'success',
    RETURNED: 'info',
    OVERDUE: 'error',
    RENEWED: 'warning',
  };
  return colors[status] || 'grey';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    BORROWED: '借阅中',
    RETURNED: '已归还',
    OVERDUE: '逾期',
    RENEWED: '续借',
  };
  return texts[status] || '未知';
};

// 获取默认封面
const getDefaultCover = (title?: string) => {
  return `https://via.placeholder.com/80x100/1976d2/ffffff?text=${encodeURIComponent(title || 'Book')}`;
};

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN');
};

// 检查是否逾期
const isOverdue = (dueDate: string, status: string) => {
  if (status === 'RETURNED') return false;
  return new Date(dueDate) < new Date();
};

// 检查是否可以续借
const canRenew = (record: BorrowRecord) => {
  // 简单的续借规则：未逾期且未续借过
  return !isOverdue(record.dueDate, record.status) && record.renewCount < 1;
};

// 处理搜索
const handleSearch = async () => {
  currentPage.value = 1;
  await fetchBorrowRecords();
};

// 处理重置
const handleReset = async () => {
  searchForm.keyword = '';
  searchForm.status = null;
  searchForm.startDate = '';
  searchForm.endDate = '';
  currentPage.value = 1;
  await fetchBorrowRecords();
};

// 处理清除搜索
const handleClearSearch = async () => {
  searchForm.keyword = '';
  await handleSearch();
};

// 处理页码变化
const handlePageChange = async (page: number) => {
  currentPage.value = page;
  await fetchBorrowRecords();
};

// 处理刷新
const handleRefresh = async () => {
  await Promise.all([
    fetchBorrowRecords(),
    fetchStats(),
  ]);
};

// 获取借阅记录
const fetchBorrowRecords = async () => {
  try {
    await borrowStore.fetchBorrowList({
      current: currentPage.value,
      size: 10,
      keyword: searchForm.keyword || undefined,
      status: searchForm.status || undefined,
      startDate: searchForm.startDate || undefined,
      endDate: searchForm.endDate || undefined,
    });
  } catch (error) {
    console.error('获取借阅记录失败:', error);
  }
};

// 获取统计数据
const fetchStats = async () => {
  try {
    // TODO: 实现统计数据获取
    // 这里暂时使用模拟数据
    stats.total = borrowStore.pagination.total;
    stats.borrowed = Math.floor(stats.total * 0.6);
    stats.returned = Math.floor(stats.total * 0.3);
    stats.overdue = stats.total - stats.borrowed - stats.returned;
  } catch (error) {
    console.error('获取统计数据失败:', error);
  }
};

// 处理查看详情
const handleViewDetail = (record: BorrowRecord) => {
  // TODO: 打开借阅详情对话框
  console.log('查看借阅详情:', record);
};

// 处理归还
const handleReturn = async (record: BorrowRecord) => {
  try {
    await borrowStore.returnBookAction(record.id);
    await handleRefresh();
  } catch (error: any) {
    console.error('归还图书失败:', error);
  }
};

// 处理续借
const handleRenew = async (record: BorrowRecord) => {
  try {
    await borrowStore.renewBookAction(record.id);
    await handleRefresh();
  } catch (error: any) {
    console.error('续借图书失败:', error);
  }
};

// 处理逾期处理
const handleProcessOverdue = async () => {
  try {
    isProcessingOverdue.value = true;
    await borrowStore.processOverdueBooksAction();
    await handleRefresh();
  } catch (error: any) {
    console.error('处理逾期图书失败:', error);
  } finally {
    isProcessingOverdue.value = false;
  }
};

// 组件挂载
onMounted(async () => {
  await handleRefresh();
});
</script>

<style scoped>
.borrow-management-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-card {
  border-radius: 16px !important;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(25, 118, 210, 0.2) !important;
}

.search-card {
  border-radius: 16px !important;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(66, 165, 245, 0.02) 100%);
}

.borrows-table-card {
  border-radius: 16px !important;
}

.table-header {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 100%);
  font-weight: 600;
}

.borrows-table {
  border-radius: 0 0 16px 16px !important;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.pagination-section {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  background: rgba(0, 0, 0, 0.02);
  padding: 16px 24px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .borrow-management-container {
    padding: 16px;
  }

  .page-header .d-flex {
    flex-direction: column;
    gap: 16px;
    align-items: stretch !important;
  }

  .header-actions {
    justify-content: stretch;
  }

  .header-actions .v-btn {
    flex: 1;
  }

  .action-buttons {
    justify-content: center;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .search-card {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.05) 0%, rgba(25, 118, 210, 0.05) 100%);
  }

  .table-header {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  }

  .pagination-section {
    border-top-color: rgba(255, 255, 255, 0.12);
    background: rgba(255, 255, 255, 0.02);
  }
}
</style>
