<template>
  <div class="user-management-container">
    <!-- 页面头部 -->
    <div class="page-header mb-6">
      <div class="d-flex justify-space-between align-center">
        <div>
          <h1 class="text-h4 font-weight-bold gradient-text">用户管理</h1>
          <p class="text-body-1 text-medium-emphasis mt-2">
            管理系统用户账户和权限
          </p>
        </div>
        <v-btn
          color="primary"
          variant="elevated"
          prepend-icon="mdi-account-plus"
          @click="handleCreateUser"
          class="gradient-btn"
        >
          添加用户
        </v-btn>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <v-card class="search-card mb-6" elevation="2">
      <v-card-text>
        <v-row align="center">
          <v-col cols="12" md="4">
            <v-text-field
              v-model="searchForm.keyword"
              label="搜索用户"
              placeholder="输入用户名、姓名或邮箱"
              prepend-inner-icon="mdi-magnify"
              variant="outlined"
              density="compact"
              hide-details
              clearable
              @keyup.enter="handleSearch"
              @click:clear="handleClearSearch"
            />
          </v-col>

          <v-col cols="12" md="3">
            <v-select
              v-model="searchForm.role"
              :items="roleOptions"
              label="用户角色"
              variant="outlined"
              density="compact"
              hide-details
              clearable
              @update:model-value="handleSearch"
            />
          </v-col>

          <v-col cols="12" md="2">
            <v-select
              v-model="searchForm.status"
              :items="statusOptions"
              label="账户状态"
              variant="outlined"
              density="compact"
              hide-details
              clearable
              @update:model-value="handleSearch"
            />
          </v-col>

          <v-col cols="12" md="3">
            <v-btn
              color="primary"
              variant="elevated"
              prepend-icon="mdi-magnify"
              @click="handleSearch"
              class="me-2"
            >
              搜索
            </v-btn>
            <v-btn
              variant="outlined"
              prepend-icon="mdi-refresh"
              @click="handleReset"
            >
              重置
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 用户列表 -->
    <v-card class="users-table-card" elevation="2">
      <v-card-title class="table-header">
        <v-icon class="me-2">mdi-account-multiple</v-icon>
        用户列表
        <v-spacer />
        <v-chip
          v-if="userManagementStore.pagination.total > 0"
          color="primary"
          variant="tonal"
          size="small"
        >
          共 {{ userManagementStore.pagination.total }} 个用户
        </v-chip>
      </v-card-title>

      <!-- 数据表格 -->
      <v-data-table
        :headers="tableHeaders"
        :items="userManagementStore.users"
        :loading="userManagementStore.isLoading"
        :items-per-page="userManagementStore.pagination.size"
        hide-default-footer
        class="users-table"
      >
        <!-- 头像列 -->
        <template #item.avatar="{ item }">
          <v-avatar size="40" class="my-2">
            <v-img
              v-if="item.avatar"
              :src="item.avatar"
              :alt="item.username"
            />
            <v-icon v-else>mdi-account-circle</v-icon>
          </v-avatar>
        </template>

        <!-- 用户信息列 -->
        <template #item.userInfo="{ item }">
          <div>
            <div class="font-weight-bold">{{ item.username }}</div>
            <div class="text-body-2 text-medium-emphasis">{{ item.realName }}</div>
            <div class="text-body-2 text-medium-emphasis">{{ item.email }}</div>
          </div>
        </template>

        <!-- 角色列 -->
        <template #item.role="{ item }">
          <v-chip
            :color="getRoleColor(item.role)"
            variant="tonal"
            size="small"
          >
            {{ getRoleDisplayName(item.role) }}
          </v-chip>
        </template>

        <!-- 状态列 -->
        <template #item.status="{ item }">
          <v-chip
            :color="item.status === 1 ? 'success' : 'error'"
            variant="tonal"
            size="small"
          >
            {{ item.status === 1 ? '正常' : '禁用' }}
          </v-chip>
        </template>

        <!-- 最后登录时间列 -->
        <template #item.lastLoginTime="{ item }">
          <span v-if="item.lastLoginTime" class="text-body-2">
            {{ formatDateTime(item.lastLoginTime) }}
          </span>
          <span v-else class="text-body-2 text-medium-emphasis">
            从未登录
          </span>
        </template>

        <!-- 操作列 -->
        <template #item.actions="{ item }">
          <div class="action-buttons">
            <v-btn
              icon
              size="small"
              variant="text"
              @click="handleViewUser(item)"
            >
              <v-icon>mdi-eye</v-icon>
              <v-tooltip activator="parent" location="top">查看详情</v-tooltip>
            </v-btn>

            <v-btn
              icon
              size="small"
              variant="text"
              @click="handleEditUser(item)"
            >
              <v-icon>mdi-pencil</v-icon>
              <v-tooltip activator="parent" location="top">编辑</v-tooltip>
            </v-btn>

            <v-btn
              icon
              size="small"
              variant="text"
              :color="item.status === 1 ? 'warning' : 'success'"
              @click="handleToggleStatus(item)"
            >
              <v-icon>{{ item.status === 1 ? 'mdi-account-off' : 'mdi-account-check' }}</v-icon>
              <v-tooltip activator="parent" location="top">
                {{ item.status === 1 ? '禁用' : '启用' }}
              </v-tooltip>
            </v-btn>

            <v-btn
              icon
              size="small"
              variant="text"
              color="error"
              @click="handleDeleteUser(item)"
              :disabled="item.id === currentUserId"
            >
              <v-icon>mdi-delete</v-icon>
              <v-tooltip activator="parent" location="top">删除</v-tooltip>
            </v-btn>
          </div>
        </template>

        <!-- 空状态 -->
        <template #no-data>
          <div class="text-center pa-8">
            <v-icon size="64" color="grey-lighten-1" class="mb-4">
              mdi-account-search
            </v-icon>
            <h3 class="text-h6 mb-2">暂无用户</h3>
            <p class="text-body-2 text-medium-emphasis">
              {{ searchForm.keyword ? '没有找到匹配的用户' : '暂时没有用户数据' }}
            </p>
          </div>
        </template>
      </v-data-table>

      <!-- 分页 -->
      <v-card-actions v-if="userManagementStore.pagination.total > 0" class="pagination-section">
        <v-spacer />
        <div class="d-flex align-center gap-4">
          <span class="text-body-2 text-medium-emphasis">
            第 {{ userManagementStore.pagination.current }} / {{ userManagementStore.pagination.pages }} 页，
            共 {{ userManagementStore.pagination.total }} 条记录
          </span>
          <v-pagination
            v-model="currentPage"
            :length="userManagementStore.pagination.pages"
            :total-visible="5"
            size="small"
            @update:model-value="handlePageChange"
          />
        </div>
      </v-card-actions>
    </v-card>

    <!-- 用户编辑对话框 -->
    <UserEditDialog
      v-model="editDialog.show"
      :user="editDialog.user"
      :is-edit="editDialog.isEdit"
      @confirm="handleConfirmEdit"
    />

    <!-- 用户详情对话框 -->
    <UserDetailDialog
      v-model="detailDialog.show"
      :user="detailDialog.user"
      @edit="handleEditFromDetail"
    />

    <!-- 删除确认对话框 -->
    <DeleteConfirmDialog
      v-model="deleteDialog.show"
      :title="`删除用户 ${deleteDialog.user?.realName || deleteDialog.user?.username}`"
      :message="`确定要删除用户 ${deleteDialog.user?.realName || deleteDialog.user?.username} 吗？`"
      :extra-info="deleteExtraInfo"
      @confirm="handleConfirmDelete"
    />

    <!-- 消息提示 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="snackbar.timeout"
      location="top"
    >
      {{ snackbar.message }}
      <template #actions>
        <v-btn variant="text" @click="snackbar.show = false">
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useUserStore } from '@/stores/user';
import { useUserManagementStore } from '@/stores/userManagement';
import { getRoleDisplayName } from '@/utils/permission';
import UserEditDialog from '@/components/admin/UserEditDialog.vue';
import UserDetailDialog from '@/components/admin/UserDetailDialog.vue';
import DeleteConfirmDialog from '@/components/admin/DeleteConfirmDialog.vue';
import type { User, RegisterForm } from '@/types';

const userStore = useUserStore();
const userManagementStore = useUserManagementStore();

// 响应式状态
const currentPage = ref(1);
const searchForm = reactive({
  keyword: '',
  role: null as string | null,
  status: null as number | null,
});

// 对话框状态
const editDialog = reactive({
  show: false,
  user: null as User | null,
  isEdit: false,
});

const detailDialog = reactive({
  show: false,
  user: null as User | null,
});

const deleteDialog = reactive({
  show: false,
  user: null as User | null,
});

const snackbar = reactive({
  show: false,
  message: '',
  color: 'success',
  timeout: 3000,
});

// 计算属性
const currentUserId = computed(() => userStore.userInfo?.id);

const deleteExtraInfo = computed(() => {
  if (!deleteDialog.user) return [];
  return [
    '删除用户将同时删除其所有相关数据',
    '包括借阅记录、收藏记录等',
    '此操作不可撤销，请谨慎操作',
  ];
});

// 表格头部配置
const tableHeaders = [
  { title: '头像', key: 'avatar', sortable: false, width: '80px' },
  { title: '用户信息', key: 'userInfo', sortable: false },
  { title: '角色', key: 'role', sortable: true },
  { title: '状态', key: 'status', sortable: true },
  { title: '最后登录', key: 'lastLoginTime', sortable: true },
  { title: '操作', key: 'actions', sortable: false, width: '200px' },
];

// 选项配置
const roleOptions = [
  { title: '全部角色', value: null },
  { title: '普通用户', value: 'USER' },
  { title: '管理员', value: 'ADMIN' },
  { title: '超级管理员', value: 'SUPER_ADMIN' },
];

const statusOptions = [
  { title: '全部状态', value: null },
  { title: '正常', value: 1 },
  { title: '禁用', value: 0 },
];

// 获取角色颜色
const getRoleColor = (role: string) => {
  const colors: Record<string, string> = {
    USER: 'primary',
    ADMIN: 'warning',
    SUPER_ADMIN: 'error',
  };
  return colors[role] || 'grey';
};

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

// 处理搜索
const handleSearch = async () => {
  currentPage.value = 1;
  await fetchUsers();
};

// 处理重置
const handleReset = async () => {
  searchForm.keyword = '';
  searchForm.role = null;
  searchForm.status = null;
  currentPage.value = 1;
  await fetchUsers();
};

// 处理清除搜索
const handleClearSearch = async () => {
  searchForm.keyword = '';
  await handleSearch();
};

// 处理页码变化
const handlePageChange = async (page: number) => {
  currentPage.value = page;
  await fetchUsers();
};

// 获取用户列表
const fetchUsers = async () => {
  try {
    await userManagementStore.fetchUserList({
      current: currentPage.value,
      size: 10,
      keyword: searchForm.keyword || undefined,
      role: searchForm.role || undefined,
      status: searchForm.status || undefined,
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
};

// 处理创建用户
const handleCreateUser = () => {
  editDialog.user = null;
  editDialog.isEdit = false;
  editDialog.show = true;
};

// 处理查看用户
const handleViewUser = (user: User) => {
  detailDialog.user = user;
  detailDialog.show = true;
};

// 处理编辑用户
const handleEditUser = (user: User) => {
  editDialog.user = user;
  editDialog.isEdit = true;
  editDialog.show = true;
};

// 处理从详情页编辑
const handleEditFromDetail = (user: User) => {
  detailDialog.show = false;
  setTimeout(() => {
    handleEditUser(user);
  }, 100);
};

// 处理切换状态
const handleToggleStatus = async (user: User) => {
  try {
    await userManagementStore.toggleUserStatusAction(user.id);
  } catch (error) {
    console.error('切换用户状态失败:', error);
  }
};

// 处理删除用户
const handleDeleteUser = (user: User) => {
  deleteDialog.user = user;
  deleteDialog.show = true;
};

// 处理确认编辑
const handleConfirmEdit = async (data: { userForm: RegisterForm | Partial<User>; isEdit: boolean }) => {
  try {
    if (data.isEdit && editDialog.user) {
      await userManagementStore.updateUserAction(editDialog.user.id, data.userForm as Partial<User>);
      showMessage('用户信息更新成功', 'success');
    } else {
      await userManagementStore.createUserAction(data.userForm as RegisterForm, (data.userForm as any).role);
      showMessage('用户创建成功', 'success');
    }
    editDialog.show = false;
  } catch (error: any) {
    console.error('用户操作失败:', error);
    showMessage(error.message || '操作失败', 'error');
  }
};

// 处理确认删除
const handleConfirmDelete = async () => {
  try {
    if (deleteDialog.user) {
      await userManagementStore.deleteUserAction(deleteDialog.user.id);
      showMessage('用户删除成功', 'success');
      deleteDialog.show = false;
    }
  } catch (error: any) {
    console.error('删除用户失败:', error);
    showMessage(error.message || '删除失败', 'error');
  }
};

// 显示消息
const showMessage = (message: string, color: string = 'success') => {
  snackbar.message = message;
  snackbar.color = color;
  snackbar.show = true;
};

// 组件挂载
onMounted(async () => {
  await fetchUsers();
});
</script>

<style scoped>
.user-management-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.search-card {
  border-radius: 16px !important;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(66, 165, 245, 0.02) 100%);
}

.users-table-card {
  border-radius: 16px !important;
}

.table-header {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 100%);
  font-weight: 600;
}

.users-table {
  border-radius: 0 0 16px 16px !important;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.pagination-section {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  background: rgba(0, 0, 0, 0.02);
  padding: 16px 24px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-management-container {
    padding: 16px;
  }

  .page-header .d-flex {
    flex-direction: column;
    gap: 16px;
    align-items: stretch !important;
  }

  .action-buttons {
    justify-content: center;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .search-card {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.05) 0%, rgba(25, 118, 210, 0.05) 100%);
  }

  .table-header {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  }

  .pagination-section {
    border-top-color: rgba(255, 255, 255, 0.12);
    background: rgba(255, 255, 255, 0.02);
  }
}
</style>
