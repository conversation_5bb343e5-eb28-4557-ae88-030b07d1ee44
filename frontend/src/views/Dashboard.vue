<template>
  <div class="dashboard-container">
    <!-- 欢迎区域 -->
    <v-card class="welcome-card mb-6" elevation="2">
      <v-card-text class="pa-6">
        <div class="d-flex align-center">
          <div class="welcome-content flex-grow-1">
            <h1 class="text-h4 font-weight-bold gradient-text mb-2">
              欢迎回来，{{ userStore.userInfo?.realName || userStore.userInfo?.username }}！
            </h1>
            <p class="text-body-1 text-medium-emphasis">
              今天是 {{ currentDate }}，祝您阅读愉快
            </p>
          </div>
          <div class="welcome-icon">
            <v-icon size="64" color="primary">mdi-book-open-page-variant</v-icon>
          </div>
        </div>
      </v-card-text>
    </v-card>

    <!-- 快捷操作 -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card class="quick-action-card" elevation="2" @click="$router.push('/books')">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="primary" class="mb-2">mdi-book-search</v-icon>
            <h3 class="text-h6 font-weight-bold">浏览图书</h3>
            <p class="text-body-2 text-medium-emphasis">发现好书</p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="quick-action-card" elevation="2" @click="$router.push('/my-borrows')">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="success" class="mb-2">mdi-book-clock</v-icon>
            <h3 class="text-h6 font-weight-bold">我的借阅</h3>
            <p class="text-body-2 text-medium-emphasis">管理借阅</p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="quick-action-card" elevation="2" @click="$router.push('/my-favorites')">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="error" class="mb-2">mdi-heart</v-icon>
            <h3 class="text-h6 font-weight-bold">我的收藏</h3>
            <p class="text-body-2 text-medium-emphasis">收藏夹</p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="quick-action-card" elevation="2" @click="$router.push('/profile')">
          <v-card-text class="text-center pa-4">
            <v-icon size="48" color="info" class="mb-2">mdi-account</v-icon>
            <h3 class="text-h6 font-weight-bold">个人信息</h3>
            <p class="text-body-2 text-medium-emphasis">账户设置</p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 管理员快捷入口 -->
    <v-card v-if="userStore.isAdmin" class="admin-card mb-6" elevation="2">
      <v-card-title class="admin-header">
        <v-icon class="me-2">mdi-shield-crown</v-icon>
        管理员功能
      </v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" sm="6" md="4">
            <v-btn
              variant="outlined"
              size="large"
              prepend-icon="mdi-view-dashboard"
              @click="$router.push('/admin')"
              block
            >
              管理后台
            </v-btn>
          </v-col>
          <v-col cols="12" sm="6" md="4">
            <v-btn
              variant="outlined"
              size="large"
              prepend-icon="mdi-book-multiple"
              @click="$router.push('/admin/books')"
              block
            >
              图书管理
            </v-btn>
          </v-col>
          <v-col v-if="userStore.isSuperAdmin" cols="12" sm="6" md="4">
            <v-btn
              variant="outlined"
              size="large"
              prepend-icon="mdi-account-multiple"
              @click="$router.push('/admin/users')"
              block
            >
              用户管理
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 最新图书推荐 -->
    <v-card class="recommendation-card" elevation="2">
      <v-card-title>
        <v-icon class="me-2">mdi-star</v-icon>
        推荐图书
      </v-card-title>
      <v-card-text>
        <div class="text-center pa-8">
          <v-icon size="64" color="grey-lighten-1" class="mb-4">
            mdi-book-open-variant
          </v-icon>
          <h3 class="text-h6 mb-2">功能开发中</h3>
          <p class="text-body-2 text-medium-emphasis mb-4">
            图书推荐功能正在开发中，敬请期待
          </p>
          <v-btn
            variant="outlined"
            prepend-icon="mdi-book-search"
            @click="$router.push('/books')"
          >
            浏览所有图书
          </v-btn>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useUserStore } from '@/stores/user';

const userStore = useUserStore();

// 计算属性
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });
});
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  border-radius: 16px !important;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 100%);
}

.welcome-content {
  min-width: 0;
}

.welcome-icon {
  flex-shrink: 0;
  margin-left: 16px;
}

.quick-action-card {
  border-radius: 16px !important;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.quick-action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(25, 118, 210, 0.2) !important;
}

.admin-card {
  border-radius: 16px !important;
  border: 2px solid rgba(25, 118, 210, 0.2);
}

.admin-header {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(66, 165, 245, 0.1) 100%);
  color: #1976d2;
  font-weight: 600;
}

.recommendation-card {
  border-radius: 16px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .welcome-content h1 {
    font-size: 1.5rem !important;
  }
  
  .welcome-icon {
    display: none;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .welcome-card {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  }
  
  .admin-card {
    border-color: rgba(66, 165, 245, 0.3);
  }
  
  .admin-header {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.15) 0%, rgba(25, 118, 210, 0.15) 100%);
    color: #42a5f5;
  }
}
</style>
