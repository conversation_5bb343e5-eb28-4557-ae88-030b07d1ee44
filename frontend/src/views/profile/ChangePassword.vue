<template>
  <div class="change-password-container">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="d-flex align-center">
        <v-btn
          icon
          variant="text"
          @click="handleGoBack"
          class="me-3"
        >
          <v-icon>mdi-arrow-left</v-icon>
        </v-btn>
        <div>
          <h1 class="text-h4 font-weight-bold text-primary">修改密码</h1>
          <p class="text-body-2 text-medium-emphasis mt-1">
            为了账户安全，请定期更换密码
          </p>
        </div>
      </div>
    </div>

    <!-- 密码修改表单 -->
    <v-card class="password-card" elevation="2">
      <v-card-title class="text-h6 font-weight-bold d-flex align-center">
        <v-icon class="me-2">mdi-lock-reset</v-icon>
        密码修改
      </v-card-title>

      <v-card-text class="pa-6">
        <!-- 安全提示 -->
        <v-alert
          type="info"
          variant="tonal"
          class="mb-6"
          icon="mdi-information"
        >
          <div class="text-body-2">
            <strong>密码安全要求：</strong>
            <ul class="mt-2">
              <li>密码长度为6-20个字符</li>
              <li>必须包含字母和数字</li>
              <li>可以包含特殊字符 @$!%*?&</li>
              <li>新密码不能与原密码相同</li>
            </ul>
          </div>
        </v-alert>

        <v-form ref="formRef" v-model="formValid" @submit.prevent="handleSubmit">
          <v-row>
            <!-- 原密码 -->
            <v-col cols="12">
              <v-text-field
                v-model="formData.oldPassword"
                label="原密码"
                prepend-inner-icon="mdi-lock"
                :append-inner-icon="showOldPassword ? 'mdi-eye' : 'mdi-eye-off'"
                :type="showOldPassword ? 'text' : 'password'"
                variant="outlined"
                :rules="oldPasswordRules"
                :error-messages="fieldErrors.oldPassword"
                required
                clearable
                @click:append-inner="showOldPassword = !showOldPassword"
                placeholder="请输入当前密码"
              />
            </v-col>

            <!-- 新密码 -->
            <v-col cols="12">
              <v-text-field
                v-model="formData.newPassword"
                label="新密码"
                prepend-inner-icon="mdi-lock-plus"
                :append-inner-icon="showNewPassword ? 'mdi-eye' : 'mdi-eye-off'"
                :type="showNewPassword ? 'text' : 'password'"
                variant="outlined"
                :rules="newPasswordRules"
                :error-messages="fieldErrors.newPassword"
                required
                clearable
                @click:append-inner="showNewPassword = !showNewPassword"
                @input="validatePasswordStrength"
                placeholder="请输入新密码"
              />
              
              <!-- 密码强度指示器 -->
              <div v-if="formData.newPassword" class="password-strength mt-2">
                <div class="d-flex align-center mb-1">
                  <span class="text-caption me-2">密码强度:</span>
                  <v-chip
                    :color="passwordStrength.color"
                    size="small"
                    variant="tonal"
                  >
                    {{ passwordStrength.text }}
                  </v-chip>
                </div>
                <v-progress-linear
                  :model-value="passwordStrength.score * 25"
                  :color="passwordStrength.color"
                  height="4"
                  rounded
                />
              </div>
            </v-col>

            <!-- 确认新密码 -->
            <v-col cols="12">
              <v-text-field
                v-model="formData.confirmPassword"
                label="确认新密码"
                prepend-inner-icon="mdi-lock-check"
                :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                :type="showConfirmPassword ? 'text' : 'password'"
                variant="outlined"
                :rules="confirmPasswordRules"
                :error-messages="fieldErrors.confirmPassword"
                required
                clearable
                @click:append-inner="showConfirmPassword = !showConfirmPassword"
                placeholder="请再次输入新密码"
              />
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-card-actions class="pa-6">
        <v-spacer />
        <v-btn
          variant="outlined"
          @click="handleCancel"
          :disabled="profileStore.isChangingPassword"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          @click="handleSubmit"
          :loading="profileStore.isChangingPassword"
          :disabled="!formValid"
        >
          修改密码
        </v-btn>
      </v-card-actions>
    </v-card>

    <!-- 安全建议 -->
    <v-card class="security-tips-card mt-4" elevation="2">
      <v-card-title class="text-h6 font-weight-bold d-flex align-center">
        <v-icon class="me-2">mdi-shield-check</v-icon>
        安全建议
      </v-card-title>
      <v-card-text>
        <div class="security-tips">
          <div class="tip-item d-flex align-start mb-3">
            <v-icon color="success" class="me-3 mt-1">mdi-check-circle</v-icon>
            <div>
              <div class="font-weight-medium">定期更换密码</div>
              <div class="text-body-2 text-medium-emphasis">建议每3-6个月更换一次密码</div>
            </div>
          </div>
          
          <div class="tip-item d-flex align-start mb-3">
            <v-icon color="success" class="me-3 mt-1">mdi-check-circle</v-icon>
            <div>
              <div class="font-weight-medium">使用强密码</div>
              <div class="text-body-2 text-medium-emphasis">包含大小写字母、数字和特殊字符的组合</div>
            </div>
          </div>
          
          <div class="tip-item d-flex align-start mb-3">
            <v-icon color="success" class="me-3 mt-1">mdi-check-circle</v-icon>
            <div>
              <div class="font-weight-medium">避免重复使用</div>
              <div class="text-body-2 text-medium-emphasis">不要在多个网站使用相同的密码</div>
            </div>
          </div>
          
          <div class="tip-item d-flex align-start">
            <v-icon color="success" class="me-3 mt-1">mdi-check-circle</v-icon>
            <div>
              <div class="font-weight-medium">保护密码安全</div>
              <div class="text-body-2 text-medium-emphasis">不要将密码告诉他人或写在容易被发现的地方</div>
            </div>
          </div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useProfileStore } from '@/stores/profile';
import { useMessageStore } from '@/stores/message';
import type { ChangePasswordForm } from '@/api/profile';

const router = useRouter();
const profileStore = useProfileStore();
const messageStore = useMessageStore();

// 响应式引用
const formRef = ref();
const formValid = ref(false);
const showOldPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

// 表单数据
const formData = reactive<ChangePasswordForm>({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
});

// 字段错误信息
const fieldErrors = reactive({
  oldPassword: [],
  newPassword: [],
  confirmPassword: [],
});

// 密码强度
const passwordStrength = ref({
  score: 0,
  text: '弱',
  color: 'error',
});

// 表单验证规则
const oldPasswordRules = [
  (v: string) => !!v || '原密码不能为空',
];

const newPasswordRules = [
  (v: string) => !!v || '新密码不能为空',
  (v: string) => (v && v.length >= 6) || '密码长度至少6个字符',
  (v: string) => (v && v.length <= 20) || '密码长度不能超过20个字符',
  (v: string) => /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/.test(v) || '密码必须包含字母和数字',
  (v: string) => v !== formData.oldPassword || '新密码不能与原密码相同',
];

const confirmPasswordRules = [
  (v: string) => !!v || '确认密码不能为空',
  (v: string) => v === formData.newPassword || '两次输入的密码不一致',
];

// 验证密码强度
const validatePasswordStrength = () => {
  const password = formData.newPassword;
  let score = 0;
  
  if (password.length >= 8) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/\d/.test(password)) score++;
  if (/[@$!%*?&]/.test(password)) score++;
  
  passwordStrength.value.score = score;
  
  if (score <= 2) {
    passwordStrength.value.text = '弱';
    passwordStrength.value.color = 'error';
  } else if (score <= 3) {
    passwordStrength.value.text = '中等';
    passwordStrength.value.color = 'warning';
  } else if (score <= 4) {
    passwordStrength.value.text = '强';
    passwordStrength.value.color = 'success';
  } else {
    passwordStrength.value.text = '很强';
    passwordStrength.value.color = 'success';
  }
};

// 清除字段错误
const clearFieldErrors = () => {
  fieldErrors.oldPassword = [];
  fieldErrors.newPassword = [];
  fieldErrors.confirmPassword = [];
};

// 处理返回
const handleGoBack = () => {
  router.push('/profile');
};

// 处理取消
const handleCancel = () => {
  handleGoBack();
};

// 处理提交
const handleSubmit = async () => {
  // 验证表单
  const { valid } = await formRef.value.validate();
  if (!valid) return;
  
  clearFieldErrors();
  
  try {
    await profileStore.changePassword(formData);
    showMessage('密码修改成功，请重新登录', 'success');
    
    // 密码修改成功后，清空表单并返回
    formData.oldPassword = '';
    formData.newPassword = '';
    formData.confirmPassword = '';
    
    setTimeout(() => {
      router.push('/profile');
    }, 1500);
    
  } catch (error: any) {
    console.error('修改密码失败:', error);
    
    // 处理字段级错误
    if (error.response?.data?.data) {
      const errors = error.response.data.data;
      Object.keys(errors).forEach(field => {
        if (fieldErrors[field as keyof typeof fieldErrors]) {
          fieldErrors[field as keyof typeof fieldErrors] = [errors[field]];
        }
      });
    } else {
      showMessage(error.message || '修改密码失败', 'error');
    }
  }
};

// 显示消息
const showMessage = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'success') => {
  messageStore.showMessage(message, type);
};
</script>

<style scoped>
.change-password-container {
  padding: 24px;
  max-width: 600px;
  margin: 0 auto;
  min-height: calc(100vh - 200px);
}

.page-header {
  margin-bottom: 24px;
}

.password-card,
.security-tips-card {
  border-radius: 16px !important;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.password-strength {
  margin-top: 8px;
}

.security-tips {
  padding: 8px 0;
}

.tip-item {
  padding: 8px 0;
}

/* 表单样式 */
:deep(.v-text-field) {
  margin-bottom: 8px;
}

:deep(.v-text-field .v-field) {
  border-radius: 12px;
}

:deep(.v-alert) {
  border-radius: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .change-password-container {
    padding: 16px;
  }

  .page-header .d-flex {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 16px;
  }
}

@media (max-width: 600px) {
  .page-header h1 {
    font-size: 1.8rem !important;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .password-card,
  .security-tips-card {
    border-color: rgba(66, 165, 245, 0.3);
  }
}

/* 动画效果 */
.password-card,
.security-tips-card {
  animation: fadeIn 0.3s ease-in-out;
  transition: all 0.3s ease;
}

.password-card:hover,
.security-tips-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(25, 118, 210, 0.15) !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单验证样式 */
:deep(.v-text-field--error) {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* 密码强度指示器样式 */
.password-strength :deep(.v-progress-linear) {
  border-radius: 4px;
}
</style>
