<template>
  <div class="profile-edit-container">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="d-flex align-center">
        <v-btn
          icon
          variant="text"
          @click="handleGoBack"
          class="me-3"
        >
          <v-icon>mdi-arrow-left</v-icon>
        </v-btn>
        <div>
          <h1 class="text-h4 font-weight-bold text-primary">编辑个人信息</h1>
          <p class="text-body-2 text-medium-emphasis mt-1">
            修改您的个人资料信息
          </p>
        </div>
      </div>
    </div>

    <!-- 编辑表单 -->
    <v-card class="edit-card" elevation="2">
      <v-card-title class="text-h6 font-weight-bold d-flex align-center">
        <v-icon class="me-2">mdi-account-edit</v-icon>
        个人信息编辑
      </v-card-title>

      <v-card-text class="pa-6">
        <v-form ref="formRef" v-model="formValid" @submit.prevent="handleSubmit">
          <v-row>
            <!-- 头像上传区域 -->
            <v-col cols="12" class="text-center mb-4">
              <div class="avatar-upload-section">
                <v-avatar
                  :size="120"
                  class="profile-avatar mb-3"
                  @click="handleAvatarUpload"
                  style="cursor: pointer;"
                >
                  <v-img
                    :src="displayAvatarUrl"
                    :alt="formData.realName || 'User'"
                    cover
                  >
                    <template #placeholder>
                      <div class="d-flex align-center justify-center fill-height">
                        <v-icon size="60" color="grey-lighten-2">mdi-account</v-icon>
                      </div>
                    </template>
                  </v-img>
                  
                  <!-- 上传覆盖层 -->
                  <div class="avatar-overlay">
                    <v-icon color="white">mdi-camera</v-icon>
                  </div>
                </v-avatar>
                
                <div>
                  <v-btn
                    color="primary"
                    variant="outlined"
                    size="small"
                    prepend-icon="mdi-camera"
                    @click="handleAvatarUpload"
                    :loading="profileStore.isUploadingAvatar"
                  >
                    更换头像
                  </v-btn>
                  <p class="text-caption text-medium-emphasis mt-2">
                    支持 JPG、PNG、GIF 格式，文件大小不超过 5MB
                  </p>
                </div>
              </div>
            </v-col>

            <!-- 真实姓名 -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.realName"
                label="真实姓名"
                prepend-inner-icon="mdi-account"
                variant="outlined"
                :rules="realNameRules"
                :error-messages="fieldErrors.realName"
                required
                clearable
              />
            </v-col>

            <!-- 邮箱地址 -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.email"
                label="邮箱地址"
                prepend-inner-icon="mdi-email"
                variant="outlined"
                type="email"
                :rules="emailRules"
                :error-messages="fieldErrors.email"
                required
                clearable
              />
            </v-col>

            <!-- 手机号码 -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.phone"
                label="手机号码"
                prepend-inner-icon="mdi-phone"
                variant="outlined"
                :rules="phoneRules"
                :error-messages="fieldErrors.phone"
                clearable
                placeholder="请输入手机号码（可选）"
              />
            </v-col>

            <!-- 用户名（只读） -->
            <v-col cols="12" md="6">
              <v-text-field
                :model-value="profileStore.profile?.username"
                label="用户名"
                prepend-inner-icon="mdi-account-circle"
                variant="outlined"
                readonly
                disabled
                hint="用户名不可修改"
                persistent-hint
              />
            </v-col>

            <!-- 用户角色（只读） -->
            <v-col cols="12" md="6">
              <v-text-field
                :model-value="profileStore.profile ? profileStore.formatRole(profileStore.profile.role) : ''"
                label="用户角色"
                prepend-inner-icon="mdi-shield-account"
                variant="outlined"
                readonly
                disabled
                hint="角色由管理员分配"
                persistent-hint
              />
            </v-col>

            <!-- 注册时间（只读） -->
            <v-col cols="12" md="6">
              <v-text-field
                :model-value="profileStore.profile ? formatDate(profileStore.profile.createdTime) : ''"
                label="注册时间"
                prepend-inner-icon="mdi-calendar"
                variant="outlined"
                readonly
                disabled
                hint="账户创建时间"
                persistent-hint
              />
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-card-actions class="pa-6">
        <v-spacer />
        <v-btn
          variant="outlined"
          @click="handleCancel"
          :disabled="profileStore.isUpdating"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          @click="handleSubmit"
          :loading="profileStore.isUpdating"
          :disabled="!formValid || !hasChanges"
        >
          保存更改
        </v-btn>
      </v-card-actions>
    </v-card>

    <!-- 头像上传文件选择器 -->
    <input
      ref="avatarInput"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleAvatarFileChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useProfileStore } from '@/stores/profile';
import { useMessageStore } from '@/stores/message';
import { formatDate } from '@/utils/date';
import type { UserProfileUpdateForm } from '@/api/profile';

const router = useRouter();
const profileStore = useProfileStore();
const messageStore = useMessageStore();

// 响应式引用
const formRef = ref();
const avatarInput = ref<HTMLInputElement>();
const formValid = ref(false);

// 表单数据
const formData = reactive<UserProfileUpdateForm>({
  realName: '',
  email: '',
  phone: '',
  avatar: '',
});

// 原始数据（用于检测变更）
const originalData = ref<UserProfileUpdateForm>({
  realName: '',
  email: '',
  phone: '',
  avatar: '',
});

// 字段错误信息
const fieldErrors = reactive({
  realName: [],
  email: [],
  phone: [],
});

// 表单验证规则
const realNameRules = [
  (v: string) => !!v || '真实姓名不能为空',
  (v: string) => (v && v.length >= 2) || '真实姓名至少需要2个字符',
  (v: string) => (v && v.length <= 20) || '真实姓名不能超过20个字符',
];

const emailRules = [
  (v: string) => !!v || '邮箱地址不能为空',
  (v: string) => /.+@.+\..+/.test(v) || '请输入有效的邮箱地址',
];

const phoneRules = [
  (v: string) => !v || /^1[3-9]\d{9}$/.test(v) || '请输入有效的手机号码',
];

// 计算属性
const hasChanges = computed(() => {
  return (
    formData.realName !== originalData.value.realName ||
    formData.email !== originalData.value.email ||
    formData.phone !== originalData.value.phone ||
    formData.avatar !== originalData.value.avatar
  );
});

const displayAvatarUrl = computed(() => {
  return profileStore.displayAvatarUrl;
});

// 初始化表单数据
const initializeForm = () => {
  if (profileStore.profile) {
    const profile = profileStore.profile;
    formData.realName = profile.realName || '';
    formData.email = profile.email || '';
    formData.phone = profile.phone || '';
    formData.avatar = profile.avatar || '';
    
    // 保存原始数据
    originalData.value = { ...formData };
  }
};

// 清除字段错误
const clearFieldErrors = () => {
  fieldErrors.realName = [];
  fieldErrors.email = [];
  fieldErrors.phone = [];
};

// 处理返回
const handleGoBack = () => {
  if (hasChanges.value) {
    if (confirm('您有未保存的更改，确定要离开吗？')) {
      router.push('/profile');
    }
  } else {
    router.push('/profile');
  }
};

// 处理取消
const handleCancel = () => {
  handleGoBack();
};

// 处理提交
const handleSubmit = async () => {
  // 验证表单
  const { valid } = await formRef.value.validate();
  if (!valid) return;
  
  clearFieldErrors();
  
  try {
    await profileStore.updateProfile(formData);
    showMessage('个人信息更新成功', 'success');
    router.push('/profile');
  } catch (error: any) {
    console.error('更新个人信息失败:', error);
    
    // 处理字段级错误
    if (error.response?.data?.data) {
      const errors = error.response.data.data;
      Object.keys(errors).forEach(field => {
        if (fieldErrors[field as keyof typeof fieldErrors]) {
          fieldErrors[field as keyof typeof fieldErrors] = [errors[field]];
        }
      });
    } else {
      showMessage(error.message || '更新个人信息失败', 'error');
    }
  }
};

// 处理头像上传
const handleAvatarUpload = () => {
  avatarInput.value?.click();
};

// 处理头像文件选择
const handleAvatarFileChange = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  
  if (!file) return;
  
  // 验证文件
  const validation = profileStore.validateAvatarFile(file);
  if (!validation.valid) {
    showMessage(validation.message || '文件格式不正确', 'error');
    return;
  }
  
  try {
    const response = await profileStore.uploadAvatar(file);
    formData.avatar = response.data.url;
    showMessage('头像上传成功', 'success');
  } catch (error: any) {
    console.error('上传头像失败:', error);
    showMessage(error.message || '上传头像失败', 'error');
  } finally {
    // 清空文件选择器
    if (target) {
      target.value = '';
    }
  }
};

// 显示消息
const showMessage = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'success') => {
  messageStore.showMessage(message, type);
};

// 监听个人信息变化
watch(
  () => profileStore.profile,
  () => {
    initializeForm();
  },
  { immediate: true }
);

// 组件挂载
onMounted(async () => {
  if (!profileStore.hasProfile) {
    try {
      await profileStore.fetchProfile();
    } catch (error: any) {
      console.error('获取个人信息失败:', error);
      showMessage(error.message || '获取个人信息失败', 'error');
      router.push('/profile');
    }
  }
});
</script>

<style scoped>
.profile-edit-container {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
  min-height: calc(100vh - 200px);
}

.page-header {
  margin-bottom: 24px;
}

.edit-card {
  border-radius: 16px !important;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.avatar-upload-section {
  position: relative;
}

.profile-avatar {
  border: 4px solid rgba(25, 118, 210, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.profile-avatar:hover {
  border-color: rgba(25, 118, 210, 0.4);
  transform: scale(1.05);
}

.profile-avatar:hover .avatar-overlay {
  opacity: 1;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

/* 表单样式 */
:deep(.v-text-field) {
  margin-bottom: 8px;
}

:deep(.v-text-field .v-field) {
  border-radius: 12px;
}

:deep(.v-text-field--disabled) {
  opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-edit-container {
    padding: 16px;
  }

  .page-header .d-flex {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 16px;
  }

  .profile-avatar {
    width: 100px !important;
    height: 100px !important;
  }
}

@media (max-width: 600px) {
  .page-header h1 {
    font-size: 1.8rem !important;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .edit-card {
    border-color: rgba(66, 165, 245, 0.3);
  }
}

/* 动画效果 */
.edit-card {
  animation: fadeIn 0.3s ease-in-out;
  transition: all 0.3s ease;
}

.edit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(25, 118, 210, 0.15) !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单验证样式 */
:deep(.v-text-field--error) {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}
</style>
