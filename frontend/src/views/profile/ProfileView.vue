<template>
  <div class="profile-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="d-flex align-center">
        <v-btn
          icon
          variant="text"
          @click="handleGoBack"
          class="me-3"
          size="small"
        >
          <v-icon>mdi-arrow-left</v-icon>
        </v-btn>
        <div>
          <h1 class="text-h5 font-weight-bold text-primary">个人信息</h1>
          <p class="text-caption text-medium-emphasis">
            查看和管理您的个人资料
          </p>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="profileStore.isLoading" class="loading-section">
      <v-row>
        <v-col cols="12" md="4">
          <v-skeleton-loader type="card-avatar, article" />
        </v-col>
        <v-col cols="12" md="8">
          <v-skeleton-loader type="article, actions" />
        </v-col>
      </v-row>
    </div>

    <!-- 个人信息内容 -->
    <div v-else-if="profileStore.hasProfile" class="profile-content">
      <v-row class="profile-row">
        <!-- 左侧：头像和基本信息 -->
        <v-col cols="12" md="4" class="left-column">
          <v-card class="profile-card" elevation="2">
            <v-card-text class="text-center pa-4">
              <!-- 头像 -->
              <div class="avatar-section mb-3">
                <UserAvatar
                  :src="profile.avatar"
                  :username="profile.username"
                  :real-name="profile.realName"
                  size="100"
                  class="profile-avatar mb-2"
                  clickable
                  @click="handleAvatarUpload"
                />

                <div>
                  <v-btn
                    color="primary"
                    variant="outlined"
                    size="x-small"
                    prepend-icon="mdi-camera"
                    @click="handleAvatarUpload"
                    :loading="profileStore.isUploadingAvatar"
                  >
                    更换头像
                  </v-btn>
                </div>
              </div>

              <!-- 基本信息 -->
              <div class="basic-info">
                <h2 class="text-h6 font-weight-bold mb-1">
                  {{ profile.realName || profile.username }}
                </h2>
                <v-chip
                  :color="statusInfo.color"
                  size="x-small"
                  variant="tonal"
                  class="mb-1"
                >
                  {{ statusInfo.text }}
                </v-chip>
                <p class="text-caption text-medium-emphasis">
                  {{ profileStore.formatRole(profile.role) }}
                </p>
              </div>
            </v-card-text>
          </v-card>

          <!-- 统计信息卡片 -->
          <v-card class="stats-card mt-3" elevation="2">
            <v-card-title class="text-subtitle-1 font-weight-bold pa-3">
              <v-icon class="me-2" size="small">mdi-chart-box</v-icon>
              统计信息
            </v-card-title>
            <v-card-text class="pa-3">
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-number text-primary">{{ profile.borrowStats.totalBorrows }}</div>
                  <div class="stat-label">总借阅</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number text-info">{{ profile.borrowStats.activeBorrows }}</div>
                  <div class="stat-label">当前借阅</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number text-warning">{{ profile.borrowStats.overdueBorrows }}</div>
                  <div class="stat-label">逾期图书</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number text-success">{{ profile.borrowStats.totalFavorites }}</div>
                  <div class="stat-label">收藏图书</div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- 右侧：详细信息 -->
        <v-col cols="12" md="8" class="right-column">
          <v-card class="details-card" elevation="2">
            <v-card-title class="text-subtitle-1 font-weight-bold d-flex align-center justify-space-between pa-3">
              <div class="d-flex align-center">
                <v-icon class="me-2" size="small">mdi-account-details</v-icon>
                详细信息
              </div>
              <v-btn
                color="primary"
                variant="elevated"
                prepend-icon="mdi-pencil"
                size="small"
                @click="handleEditProfile"
              >
                编辑资料
              </v-btn>
            </v-card-title>

            <v-card-text class="pa-3">
              <v-row class="info-row">
                <v-col cols="12" sm="6" class="info-col">
                  <div class="info-item">
                    <div class="info-label">用户名</div>
                    <div class="info-value">{{ profile.username }}</div>
                  </div>
                </v-col>

                <v-col cols="12" sm="6" class="info-col">
                  <div class="info-item">
                    <div class="info-label">真实姓名</div>
                    <div class="info-value">{{ profile.realName || '未设置' }}</div>
                  </div>
                </v-col>

                <v-col cols="12" sm="6" class="info-col">
                  <div class="info-item">
                    <div class="info-label">邮箱地址</div>
                    <div class="info-value">{{ profile.email }}</div>
                  </div>
                </v-col>

                <v-col cols="12" sm="6" class="info-col">
                  <div class="info-item">
                    <div class="info-label">手机号码</div>
                    <div class="info-value">{{ profile.phone || '未设置' }}</div>
                  </div>
                </v-col>

                <v-col cols="12" sm="6" class="info-col">
                  <div class="info-item">
                    <div class="info-label">用户角色</div>
                    <div class="info-value">{{ profileStore.formatRole(profile.role) }}</div>
                  </div>
                </v-col>

                <v-col cols="12" sm="6" class="info-col">
                  <div class="info-item">
                    <div class="info-label">账户状态</div>
                    <div class="info-value">
                      <v-chip
                        :color="statusInfo.color"
                        size="x-small"
                        variant="tonal"
                      >
                        {{ statusInfo.text }}
                      </v-chip>
                    </div>
                  </div>
                </v-col>

                <v-col cols="12" sm="6" class="info-col">
                  <div class="info-item">
                    <div class="info-label">注册时间</div>
                    <div class="info-value">{{ formatDate(profile.createdTime) }}</div>
                  </div>
                </v-col>

                <v-col cols="12" sm="6" class="info-col">
                  <div class="info-item">
                    <div class="info-label">最后登录</div>
                    <div class="info-value">{{ profile.lastLoginTime ? formatDate(profile.lastLoginTime) : '未记录' }}</div>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>

          <!-- 操作按钮 -->
          <v-card class="actions-card mt-3" elevation="2">
            <v-card-title class="text-subtitle-1 font-weight-bold pa-3">
              <v-icon class="me-2" size="small">mdi-cog</v-icon>
              账户操作
            </v-card-title>
            <v-card-text class="pa-3">
              <div class="d-flex flex-wrap gap-2">
                <v-btn
                  color="primary"
                  variant="elevated"
                  prepend-icon="mdi-pencil"
                  size="small"
                  @click="handleEditProfile"
                >
                  编辑个人信息
                </v-btn>

                <v-btn
                  color="warning"
                  variant="outlined"
                  prepend-icon="mdi-lock-reset"
                  size="small"
                  @click="handleChangePassword"
                >
                  修改密码
                </v-btn>

                <v-btn
                  color="info"
                  variant="outlined"
                  prepend-icon="mdi-refresh"
                  size="small"
                  @click="handleRefresh"
                  :loading="profileStore.isLoading"
                >
                  刷新信息
                </v-btn>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-section">
      <v-card class="error-card" elevation="2">
        <v-card-text class="text-center pa-8">
          <v-icon size="80" color="error" class="mb-4">
            mdi-alert-circle
          </v-icon>
          <h3 class="text-h5 font-weight-bold mb-2">加载失败</h3>
          <p class="text-body-1 text-medium-emphasis mb-4">
            无法获取个人信息，请稍后重试
          </p>
          <v-btn
            color="primary"
            variant="elevated"
            prepend-icon="mdi-refresh"
            @click="handleRefresh"
          >
            重新加载
          </v-btn>
        </v-card-text>
      </v-card>
    </div>

    <!-- 头像上传文件选择器 -->
    <input
      ref="avatarInput"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleAvatarFileChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useProfileStore } from '@/stores/profile';
import { useMessageStore } from '@/stores/message';
import { formatDate } from '@/utils/date';
import UserAvatar from '@/components/UserAvatar.vue';

const router = useRouter();
const profileStore = useProfileStore();
const messageStore = useMessageStore();

// 响应式引用
const avatarInput = ref<HTMLInputElement>();

// 计算属性
const profile = computed(() => profileStore.profile);
const statusInfo = computed(() => {
  if (!profile.value) return { text: '未知', color: 'warning' };
  return profileStore.formatStatus(profile.value.status);
});

// 处理返回
const handleGoBack = () => {
  router.push('/dashboard');
};

// 处理编辑个人信息
const handleEditProfile = () => {
  router.push('/profile/edit');
};

// 处理修改密码
const handleChangePassword = () => {
  router.push('/profile/change-password');
};

// 处理刷新
const handleRefresh = async () => {
  try {
    await profileStore.fetchProfile();
    await profileStore.refreshStats();
    showMessage('信息刷新成功', 'success');
  } catch (error: any) {
    console.error('刷新个人信息失败:', error);
    showMessage(error.message || '刷新失败', 'error');
  }
};

// 处理头像上传
const handleAvatarUpload = () => {
  avatarInput.value?.click();
};

// 处理头像文件选择
const handleAvatarFileChange = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) return;

  // 验证文件
  const validation = profileStore.validateAvatarFile(file);
  if (!validation.valid) {
    showMessage(validation.message || '文件格式不正确', 'error');
    return;
  }

  try {
    await profileStore.uploadAvatar(file);
    showMessage('头像上传成功', 'success');
  } catch (error: any) {
    console.error('上传头像失败:', error);
    showMessage(error.message || '上传头像失败', 'error');
  } finally {
    // 清空文件选择器
    if (target) {
      target.value = '';
    }
  }
};

// 显示消息
const showMessage = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'success') => {
  messageStore.showMessage(message, type);
};

// 组件挂载
onMounted(async () => {
  if (!profileStore.hasProfile) {
    await handleRefresh();
  }
});
</script>

<style scoped>
.profile-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
  height: calc(100vh - 64px); /* 减去导航栏高度 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.loading-section,
.error-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.profile-row {
  flex: 1;
  margin: 0 !important;
  height: 100%;
}

.left-column,
.right-column {
  padding: 0 8px !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.left-column {
  max-height: calc(100vh - 120px);
}

.right-column {
  max-height: calc(100vh - 120px);
}

.profile-card,
.stats-card,
.details-card,
.actions-card,
.error-card {
  border-radius: 16px !important;
  border: 1px solid rgba(25, 118, 210, 0.1);
  height: fit-content;
}

.profile-card {
  flex-shrink: 0;
}

.stats-card {
  flex: 1;
  min-height: 0;
}

.details-card {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.details-card .v-card-text {
  flex: 1;
  overflow-y: auto;
}

.actions-card {
  flex-shrink: 0;
  margin-top: 12px !important;
}

.profile-avatar {
  border: 4px solid rgba(25, 118, 210, 0.2);
  transition: all 0.3s ease;
}

.profile-avatar:hover {
  border-color: rgba(25, 118, 210, 0.4);
  transform: scale(1.05);
}

.basic-info h2 {
  color: #1976d2;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  border-radius: 8px;
  background: rgba(25, 118, 210, 0.05);
}

.stat-number {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
}

.info-row {
  margin: 0 !important;
}

.info-col {
  padding: 4px 8px !important;
}

.info-item {
  margin-bottom: 8px;
}

.info-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 2px;
}

.info-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

.gap-2 {
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .profile-container {
    height: auto;
    min-height: calc(100vh - 64px);
    overflow: visible;
  }

  .profile-content {
    overflow: visible;
  }

  .left-column,
  .right-column {
    max-height: none;
    height: auto;
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 12px;
    height: auto;
    min-height: calc(100vh - 64px);
  }

  .page-header {
    margin-bottom: 8px;
  }

  .page-header .d-flex {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 8px;
  }

  .left-column,
  .right-column {
    padding: 0 4px !important;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .d-flex.flex-wrap {
    flex-direction: column;
  }

  .d-flex.flex-wrap .v-btn {
    width: 100%;
  }

  .info-col {
    padding: 2px 4px !important;
  }
}

@media (max-width: 600px) {
  .page-header h1 {
    font-size: 1.25rem !important;
  }

  .profile-avatar {
    width: 80px !important;
    height: 80px !important;
  }

  .stat-number {
    font-size: 1rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
}

/* 桌面端优化 */
@media (min-width: 1200px) {
  .profile-container {
    padding: 20px;
    height: calc(100vh - 80px); /* 为更大的导航栏留出空间 */
  }

  .left-column {
    max-height: calc(100vh - 140px);
  }

  .right-column {
    max-height: calc(100vh - 140px);
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .profile-card,
  .stats-card,
  .details-card,
  .actions-card {
    border-color: rgba(66, 165, 245, 0.3);
  }

  .stat-item {
    background: rgba(66, 165, 245, 0.1);
  }

  .stat-label {
    color: rgba(255, 255, 255, 0.6);
  }

  .info-label {
    color: rgba(255, 255, 255, 0.6);
  }

  .info-value {
    color: rgba(255, 255, 255, 0.87);
  }
}

/* 动画效果 */
.profile-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-card,
.stats-card,
.details-card,
.actions-card {
  transition: all 0.3s ease;
}

.profile-card:hover,
.stats-card:hover,
.details-card:hover,
.actions-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(25, 118, 210, 0.15) !important;
}
</style>
