<template>
  <div class="profile-container">
    <!-- 页面头部 -->
    <div class="page-header mb-6">
      <h1 class="text-h4 font-weight-bold gradient-text">个人信息</h1>
      <p class="text-body-1 text-medium-emphasis mt-2">
        管理您的个人账户信息
      </p>
    </div>

    <v-row>
      <!-- 个人信息卡片 -->
      <v-col cols="12" md="8">
        <v-card class="profile-card" elevation="2">
          <v-card-title class="card-header">
            <v-icon class="me-2">mdi-account-edit</v-icon>
            基本信息
          </v-card-title>

          <v-card-text class="pa-6">
            <v-form ref="profileFormRef" v-model="formValid">
              <v-row>
                <!-- 用户名（只读） -->
                <v-col cols="12" md="6">
                  <v-text-field
                    :model-value="userStore.userInfo?.username"
                    label="用户名"
                    prepend-inner-icon="mdi-account"
                    variant="outlined"
                    readonly
                    hint="用户名不可修改"
                    persistent-hint
                  />
                </v-col>

                <!-- 邮箱 -->
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="profileForm.email"
                    label="邮箱"
                    prepend-inner-icon="mdi-email"
                    variant="outlined"
                    :rules="emailRules"
                    required
                  />
                </v-col>

                <!-- 真实姓名 -->
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="profileForm.realName"
                    label="真实姓名"
                    prepend-inner-icon="mdi-card-account-details"
                    variant="outlined"
                    :rules="realNameRules"
                    required
                  />
                </v-col>

                <!-- 手机号 -->
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="profileForm.phone"
                    label="手机号"
                    prepend-inner-icon="mdi-phone"
                    variant="outlined"
                    :rules="phoneRules"
                  />
                </v-col>

                <!-- 角色（只读） -->
                <v-col cols="12" md="6">
                  <v-text-field
                    :model-value="getRoleDisplayName(userStore.userInfo?.role || '')"
                    label="用户角色"
                    prepend-inner-icon="mdi-shield-account"
                    variant="outlined"
                    readonly
                    hint="角色由管理员分配"
                    persistent-hint
                  />
                </v-col>

                <!-- 注册时间（只读） -->
                <v-col cols="12" md="6">
                  <v-text-field
                    :model-value="formatDateTime(userStore.userInfo?.createdTime || '')"
                    label="注册时间"
                    prepend-inner-icon="mdi-calendar"
                    variant="outlined"
                    readonly
                  />
                </v-col>
              </v-row>

              <!-- 操作按钮 -->
              <div class="d-flex justify-end gap-4 mt-4">
                <v-btn
                  variant="outlined"
                  @click="handleReset"
                  :disabled="isLoading"
                >
                  重置
                </v-btn>
                <v-btn
                  color="primary"
                  variant="elevated"
                  @click="handleUpdateProfile"
                  :loading="isLoading"
                  :disabled="!formValid || !hasChanges"
                >
                  保存修改
                </v-btn>
              </div>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 侧边栏 -->
      <v-col cols="12" md="4">
        <!-- 头像卡片 -->
        <v-card class="avatar-card mb-4" elevation="2">
          <v-card-title class="card-header">
            <v-icon class="me-2">mdi-account-circle</v-icon>
            头像
          </v-card-title>

          <v-card-text class="text-center pa-6">
            <v-avatar size="120" class="mb-4">
              <v-img
                v-if="userStore.userInfo?.avatar"
                :src="userStore.userInfo.avatar"
                :alt="userStore.userInfo.username"
              />
              <v-icon v-else size="80">mdi-account-circle</v-icon>
            </v-avatar>

            <div class="mb-4">
              <h3 class="text-h6 font-weight-bold">
                {{ userStore.userInfo?.realName || userStore.userInfo?.username }}
              </h3>
              <p class="text-body-2 text-medium-emphasis">
                @{{ userStore.userInfo?.username }}
              </p>
            </div>

            <v-btn
              variant="outlined"
              size="small"
              prepend-icon="mdi-camera"
              @click="handleUploadAvatar"
              disabled
            >
              更换头像
            </v-btn>
            <p class="text-caption text-medium-emphasis mt-2">
              头像上传功能开发中
            </p>
          </v-card-text>
        </v-card>

        <!-- 账户状态卡片 -->
        <v-card class="status-card" elevation="2">
          <v-card-title class="card-header">
            <v-icon class="me-2">mdi-information</v-icon>
            账户状态
          </v-card-title>

          <v-card-text class="pa-4">
            <div class="status-item">
              <div class="d-flex justify-space-between align-center mb-3">
                <span class="text-body-2">账户状态</span>
                <v-chip
                  :color="userStore.userInfo?.status === 1 ? 'success' : 'error'"
                  variant="tonal"
                  size="small"
                >
                  {{ userStore.userInfo?.status === 1 ? '正常' : '禁用' }}
                </v-chip>
              </div>

              <div class="d-flex justify-space-between align-center mb-3">
                <span class="text-body-2">用户角色</span>
                <v-chip
                  :color="getRoleColor(userStore.userInfo?.role || '')"
                  variant="tonal"
                  size="small"
                >
                  {{ getRoleDisplayName(userStore.userInfo?.role || '') }}
                </v-chip>
              </div>

              <div v-if="userStore.userInfo?.lastLoginTime" class="d-flex justify-space-between align-center">
                <span class="text-body-2">最后登录</span>
                <span class="text-body-2 text-medium-emphasis">
                  {{ formatDateTime(userStore.userInfo.lastLoginTime) }}
                </span>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 消息提示 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="snackbar.timeout"
      location="top"
    >
      {{ snackbar.message }}
      <template #actions>
        <v-btn variant="text" @click="snackbar.show = false">
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useUserStore } from '@/stores/user';
import { updateCurrentUser } from '@/api/user';
import { getRoleDisplayName } from '@/utils/permission';

const userStore = useUserStore();

// 响应式状态
const formValid = ref(false);
const isLoading = ref(false);
const profileFormRef = ref();

const profileForm = reactive({
  email: '',
  realName: '',
  phone: '',
});

const originalForm = reactive({
  email: '',
  realName: '',
  phone: '',
});

const snackbar = reactive({
  show: false,
  message: '',
  color: 'success',
  timeout: 3000,
});

// 计算属性
const hasChanges = computed(() => {
  return (
    profileForm.email !== originalForm.email ||
    profileForm.realName !== originalForm.realName ||
    profileForm.phone !== originalForm.phone
  );
});

// 验证规则
const emailRules = [
  (v: string) => !!v || '请输入邮箱',
  (v: string) => /.+@.+\..+/.test(v) || '请输入有效的邮箱地址',
];

const realNameRules = [
  (v: string) => !!v || '请输入真实姓名',
  (v: string) => (v && v.length <= 50) || '真实姓名长度不能超过50个字符',
];

const phoneRules = [
  (v: string) => !v || /^1[3-9]\d{9}$/.test(v) || '请输入有效的手机号',
];

// 获取角色颜色
const getRoleColor = (role: string) => {
  const colors: Record<string, string> = {
    USER: 'primary',
    ADMIN: 'warning',
    SUPER_ADMIN: 'error',
  };
  return colors[role] || 'grey';
};

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '未知';
  return new Date(dateString).toLocaleString('zh-CN');
};

// 初始化表单数据
const initializeForm = () => {
  if (userStore.userInfo) {
    const formData = {
      email: userStore.userInfo.email || '',
      realName: userStore.userInfo.realName || '',
      phone: userStore.userInfo.phone || '',
    };

    Object.assign(profileForm, formData);
    Object.assign(originalForm, formData);
  }
};

// 处理重置
const handleReset = () => {
  Object.assign(profileForm, originalForm);
  if (profileFormRef.value) {
    profileFormRef.value.resetValidation();
  }
};

// 处理更新个人信息
const handleUpdateProfile = async () => {
  if (!formValid.value) return;

  isLoading.value = true;
  try {
    const updateData = {
      email: profileForm.email,
      realName: profileForm.realName,
      phone: profileForm.phone || undefined,
    };

    await updateCurrentUser(updateData);

    // 更新本地用户信息
    await userStore.fetchCurrentUser();

    // 更新原始表单数据
    Object.assign(originalForm, profileForm);

    showMessage('个人信息更新成功', 'success');
  } catch (error: any) {
    console.error('更新个人信息失败:', error);
    showMessage(error.message || '更新失败', 'error');
  } finally {
    isLoading.value = false;
  }
};

// 处理上传头像
const handleUploadAvatar = () => {
  // TODO: 实现头像上传功能
  showMessage('头像上传功能开发中', 'info');
};

// 显示消息
const showMessage = (message: string, color: string = 'success') => {
  snackbar.message = message;
  snackbar.color = color;
  snackbar.show = true;
};

// 组件挂载
onMounted(() => {
  initializeForm();
});
</script>

<style scoped>
.profile-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.profile-card,
.avatar-card,
.status-card {
  border-radius: 16px !important;
}

.card-header {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 100%);
  font-weight: 600;
}

.status-item {
  background: rgba(25, 118, 210, 0.02);
  border-radius: 8px;
  padding: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }

  .page-header h1 {
    font-size: 1.8rem !important;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .card-header {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  }

  .status-item {
    background: rgba(66, 165, 245, 0.05);
  }
}
</style>
