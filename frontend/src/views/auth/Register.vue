<template>
  <div class="register-container">
    <v-container fluid class="fill-height">
      <v-row justify="center" align="center" class="fill-height">
        <v-col cols="12" sm="8" md="6" lg="5" xl="4">
          <v-card class="register-card" elevation="8">
            <v-card-title class="text-center pa-6">
              <div class="register-header">
                <v-icon size="48" color="primary" class="mb-4">mdi-account-plus</v-icon>
                <h1 class="text-h4 font-weight-bold gradient-text">用户注册</h1>
                <p class="text-subtitle-1 text-medium-emphasis mt-2">创建您的账户</p>
              </div>
            </v-card-title>

            <v-card-text class="pa-6">
              <v-form ref="registerFormRef" v-model="formValid" @submit.prevent="handleRegister">
                <v-text-field
                  v-model="registerForm.username"
                  label="用户名"
                  prepend-inner-icon="mdi-account"
                  variant="outlined"
                  :rules="usernameRules"
                  :error-messages="errors.username"
                  class="mb-3"
                  autocomplete="username"
                />

                <v-text-field
                  v-model="registerForm.email"
                  label="邮箱"
                  prepend-inner-icon="mdi-email"
                  variant="outlined"
                  :rules="emailRules"
                  :error-messages="errors.email"
                  class="mb-3"
                  autocomplete="email"
                />

                <v-text-field
                  v-model="registerForm.realName"
                  label="真实姓名"
                  prepend-inner-icon="mdi-card-account-details"
                  variant="outlined"
                  :rules="realNameRules"
                  :error-messages="errors.realName"
                  class="mb-3"
                  autocomplete="name"
                />

                <v-text-field
                  v-model="registerForm.phone"
                  label="手机号（可选）"
                  prepend-inner-icon="mdi-phone"
                  variant="outlined"
                  :rules="phoneRules"
                  :error-messages="errors.phone"
                  class="mb-3"
                  autocomplete="tel"
                />

                <v-text-field
                  v-model="registerForm.password"
                  label="密码"
                  prepend-inner-icon="mdi-lock"
                  variant="outlined"
                  :type="showPassword ? 'text' : 'password'"
                  :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  :rules="passwordRules"
                  :error-messages="errors.password"
                  class="mb-3"
                  autocomplete="new-password"
                  @click:append-inner="showPassword = !showPassword"
                />

                <v-text-field
                  v-model="registerForm.confirmPassword"
                  label="确认密码"
                  prepend-inner-icon="mdi-lock-check"
                  variant="outlined"
                  :type="showConfirmPassword ? 'text' : 'password'"
                  :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  :rules="confirmPasswordRules"
                  :error-messages="errors.confirmPassword"
                  class="mb-4"
                  autocomplete="new-password"
                  @click:append-inner="showConfirmPassword = !showConfirmPassword"
                />

                <v-btn
                  type="submit"
                  color="primary"
                  size="large"
                  block
                  :loading="userStore.isLoading"
                  :disabled="!formValid"
                  class="mb-4 register-btn"
                >
                  注册
                </v-btn>

                <div class="text-center">
                  <span class="text-body-2 text-medium-emphasis">已有账户？</span>
                  <v-btn
                    variant="text"
                    color="primary"
                    size="small"
                    @click="$router.push('/login')"
                  >
                    立即登录
                  </v-btn>
                </div>
              </v-form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- 成功提示 -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="5000"
      location="top"
    >
      {{ successMessage }}
      <template #actions>
        <v-btn variant="text" @click="showSuccess = false">
          关闭
        </v-btn>
      </template>
    </v-snackbar>

    <!-- 错误提示 -->
    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
      location="top"
    >
      {{ errorMessage }}
      <template #actions>
        <v-btn variant="text" @click="showError = false">
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import type { RegisterForm } from '@/types';

const router = useRouter();
const userStore = useUserStore();

// 表单数据
const registerForm = reactive<RegisterForm>({
  username: '',
  email: '',
  realName: '',
  phone: '',
  password: '',
  confirmPassword: '',
});

// 表单状态
const formValid = ref(false);
const showPassword = ref(false);
const showConfirmPassword = ref(false);
const showSuccess = ref(false);
const showError = ref(false);
const successMessage = ref('');
const errorMessage = ref('');
const registerFormRef = ref();

// 错误信息
const errors = reactive({
  username: '',
  email: '',
  realName: '',
  phone: '',
  password: '',
  confirmPassword: '',
});

// 验证规则
const usernameRules = [
  (v: string) => !!v || '请输入用户名',
  (v: string) => (v && v.length >= 3 && v.length <= 20) || '用户名长度必须在3-20个字符之间',
  (v: string) => /^[a-zA-Z0-9_]+$/.test(v) || '用户名只能包含字母、数字和下划线',
];

const emailRules = [
  (v: string) => !!v || '请输入邮箱',
  (v: string) => /.+@.+\..+/.test(v) || '请输入有效的邮箱地址',
];

const realNameRules = [
  (v: string) => !!v || '请输入真实姓名',
  (v: string) => (v && v.length <= 50) || '真实姓名长度不能超过50个字符',
];

const phoneRules = [
  (v: string) => !v || /^1[3-9]\d{9}$/.test(v) || '请输入有效的手机号',
];

const passwordRules = [
  (v: string) => !!v || '请输入密码',
  (v: string) => (v && v.length >= 6 && v.length <= 20) || '密码长度必须在6-20个字符之间',
];

const confirmPasswordRules = [
  (v: string) => !!v || '请确认密码',
  (v: string) => v === registerForm.password || '两次输入的密码不一致',
];

// 处理注册
const handleRegister = async () => {
  if (!formValid.value) return;

  try {
    // 清除之前的错误信息
    Object.keys(errors).forEach(key => {
      errors[key as keyof typeof errors] = '';
    });

    await userStore.registerAction(registerForm);

    // 注册成功
    successMessage.value = '注册成功！请登录您的账户';
    showSuccess.value = true;

    // 3秒后跳转到登录页
    setTimeout(() => {
      router.push('/login');
    }, 3000);
  } catch (error: any) {
    console.error('注册失败:', error);
    errorMessage.value = error.message || '注册失败，请稍后重试';
    showError.value = true;
  }
};
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.register-card {
  border-radius: 16px !important;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
}

.register-header {
  text-align: center;
}

.gradient-text {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.register-btn {
  border-radius: 12px !important;
  text-transform: none !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%) !important;
}

:deep(.v-field) {
  border-radius: 12px !important;
}

:deep(.v-field--focused) {
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
}
</style>
