<template>
  <div class="login-container">
    <v-container fluid class="fill-height">
      <v-row justify="center" align="center" class="fill-height">
        <v-col cols="12" sm="8" md="6" lg="4" xl="3">
          <v-card class="login-card" elevation="8">
            <v-card-title class="text-center pa-6">
              <div class="login-header">
                <v-icon size="48" color="primary" class="mb-4">mdi-book-open-page-variant</v-icon>
                <h1 class="text-h4 font-weight-bold gradient-text">图书管理系统</h1>
                <p class="text-subtitle-1 text-medium-emphasis mt-2">欢迎登录</p>
              </div>
            </v-card-title>

            <v-card-text class="pa-6">
              <v-form ref="loginFormRef" v-model="formValid" @submit.prevent="handleLogin">
                <v-text-field
                  v-model="loginForm.username"
                  label="用户名"
                  prepend-inner-icon="mdi-account"
                  variant="outlined"
                  :rules="usernameRules"
                  :error-messages="errors.username"
                  class="mb-4"
                  autocomplete="username"
                  @keyup.enter="handleLogin"
                />

                <v-text-field
                  v-model="loginForm.password"
                  label="密码"
                  prepend-inner-icon="mdi-lock"
                  variant="outlined"
                  :type="showPassword ? 'text' : 'password'"
                  :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  :rules="passwordRules"
                  :error-messages="errors.password"
                  class="mb-4"
                  autocomplete="current-password"
                  @click:append-inner="showPassword = !showPassword"
                  @keyup.enter="handleLogin"
                />

                <v-btn
                  type="submit"
                  color="primary"
                  size="large"
                  block
                  :loading="userStore.isLoading"
                  :disabled="!formValid"
                  class="mb-4 login-btn"
                >
                  登录
                </v-btn>

                <div class="text-center">
                  <span class="text-body-2 text-medium-emphasis">还没有账户？</span>
                  <v-btn
                    variant="text"
                    color="primary"
                    size="small"
                    @click="$router.push('/register')"
                  >
                    立即注册
                  </v-btn>
                </div>
              </v-form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- 错误提示 -->
    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
      location="top"
    >
      {{ errorMessage }}
      <template #actions>
        <v-btn variant="text" @click="showError = false">
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/stores/user';
import type { LoginForm } from '@/types';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 表单数据
const loginForm = reactive<LoginForm>({
  username: '',
  password: '',
});

// 表单状态
const formValid = ref(false);
const showPassword = ref(false);
const showError = ref(false);
const errorMessage = ref('');
const loginFormRef = ref();

// 错误信息
const errors = reactive({
  username: '',
  password: '',
});

// 验证规则
const usernameRules = [
  (v: string) => !!v || '请输入用户名',
  (v: string) => (v && v.length >= 3) || '用户名至少3个字符',
];

const passwordRules = [
  (v: string) => !!v || '请输入密码',
  (v: string) => (v && v.length >= 6) || '密码至少6个字符',
];

// 处理登录
const handleLogin = async () => {
  if (!formValid.value) return;

  try {
    // 清除之前的错误信息
    errors.username = '';
    errors.password = '';

    await userStore.loginAction(loginForm);

    // 登录成功，跳转到目标页面或仪表盘
    const redirectPath = (route.query.redirect as string) || '/dashboard';
    router.push(redirectPath);
  } catch (error: any) {
    console.error('登录失败:', error);
    errorMessage.value = error.message || '登录失败，请检查用户名和密码';
    showError.value = true;
  }
};
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  border-radius: 16px !important;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
}

.login-header {
  text-align: center;
}

.gradient-text {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-btn {
  border-radius: 12px !important;
  text-transform: none !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%) !important;
}

:deep(.v-field) {
  border-radius: 12px !important;
}

:deep(.v-field--focused) {
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
}
</style>
