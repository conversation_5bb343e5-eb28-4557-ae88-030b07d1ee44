<template>
  <div class="book-list-container">
    <!-- 搜索和筛选区域 -->
    <v-card class="search-card mb-6" elevation="2">
      <v-card-text>
        <v-row align="center">
          <!-- 搜索框 -->
          <v-col cols="12" md="4">
            <v-text-field
              v-model="searchForm.keyword"
              label="搜索图书"
              placeholder="输入书名、作者或ISBN"
              prepend-inner-icon="mdi-magnify"
              variant="outlined"
              density="compact"
              hide-details
              clearable
              @keyup.enter="handleSearch"
              @click:clear="handleClearSearch"
            />
          </v-col>

          <!-- 分类筛选 -->
          <v-col cols="12" md="3">
            <v-select
              v-model="searchForm.categoryId"
              :items="categoryOptions"
              label="图书分类"
              variant="outlined"
              density="compact"
              hide-details
              clearable
              @update:model-value="handleSearch"
            />
          </v-col>

          <!-- 状态筛选 -->
          <v-col cols="12" md="2">
            <v-select
              v-model="searchForm.status"
              :items="statusOptions"
              label="状态"
              variant="outlined"
              density="compact"
              hide-details
              clearable
              @update:model-value="handleSearch"
            />
          </v-col>

          <!-- 搜索按钮 -->
          <v-col cols="12" md="3">
            <v-btn
              color="primary"
              variant="elevated"
              prepend-icon="mdi-magnify"
              @click="handleSearch"
              class="me-2"
            >
              搜索
            </v-btn>
            <v-btn
              variant="outlined"
              prepend-icon="mdi-refresh"
              @click="handleReset"
            >
              重置
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 图书网格 -->
    <div class="books-grid">
      <v-row class="books-row">
        <v-col
          v-for="book in bookStore.books"
          :key="book.id"
          cols="12"
          sm="6"
          md="4"
          lg="3"
          xl="3"
          class="book-col"
        >
          <BookCard
            :book="book"
            :is-favorited="favoriteStore.isFavorited(book.id)"
            @view-detail="handleViewDetail"
            @borrow="handleBorrow"
            @toggle-favorite="handleToggleFavorite"
          />
        </v-col>
      </v-row>

      <!-- 空状态 -->
      <v-row v-if="!bookStore.isLoading && bookStore.books.length === 0">
        <v-col cols="12">
          <v-card class="text-center pa-8" variant="tonal">
            <v-icon size="64" color="grey-lighten-1" class="mb-4">
              mdi-book-search
            </v-icon>
            <h3 class="text-h6 mb-2">暂无图书</h3>
            <p class="text-body-2 text-medium-emphasis">
              {{ searchForm.keyword ? '没有找到匹配的图书，请尝试其他关键词' : '暂时没有可用的图书' }}
            </p>
            <v-btn
              v-if="searchForm.keyword"
              variant="outlined"
              @click="handleReset"
              class="mt-4"
            >
              清除搜索条件
            </v-btn>
          </v-card>
        </v-col>
      </v-row>

      <!-- 加载状态 -->
      <v-row v-if="bookStore.isLoading">
        <v-col
          v-for="n in 8"
          :key="n"
          cols="12"
          sm="6"
          md="4"
          lg="3"
          xl="2"
        >
          <BookCardSkeleton />
        </v-col>
      </v-row>
    </div>

    <!-- 分页 -->
    <div v-if="bookStore.pagination.total > 0" class="pagination-section mt-6">
      <v-pagination
        v-model="currentPage"
        :length="bookStore.pagination.pages"
        :total-visible="7"
        @update:model-value="handlePageChange"
        class="justify-center"
      />
      
      <div class="text-center mt-4 text-body-2 text-medium-emphasis">
        共 {{ bookStore.pagination.total }} 本图书，
        第 {{ bookStore.pagination.current }} / {{ bookStore.pagination.pages }} 页
      </div>
    </div>

    <!-- 借阅确认对话框 -->
    <BorrowDialog
      v-model="borrowDialog.show"
      :book="borrowDialog.book"
      @confirm="handleConfirmBorrow"
    />

    <!-- 消息提示 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="snackbar.timeout"
      location="top"
    >
      {{ snackbar.message }}
      <template #actions>
        <v-btn variant="text" @click="snackbar.show = false">
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useBookStore } from '@/stores/book';
import { useBorrowStore } from '@/stores/borrow';
import { useFavoriteStore } from '@/stores/favorite';
import { useMessageStore } from '@/stores/message';
import BookCard from '@/components/books/BookCard.vue';
import BookCardSkeleton from '@/components/books/BookCardSkeleton.vue';
import BorrowDialog from '@/components/books/BorrowDialog.vue';
import type { Book } from '@/types';

const route = useRoute();
const router = useRouter();
const bookStore = useBookStore();
const borrowStore = useBorrowStore();
const favoriteStore = useFavoriteStore();
const messageStore = useMessageStore();

// 响应式状态
const currentPage = ref(1);
const searchForm = reactive({
  keyword: '',
  categoryId: null as number | null,
  status: null as number | null,
});

const borrowDialog = reactive({
  show: false,
  book: null as Book | null,
});

const snackbar = reactive({
  show: false,
  message: '',
  color: 'success',
  timeout: 3000,
});

// 计算属性
const categoryOptions = computed(() => {
  return bookStore.categories.map(category => ({
    title: category.name,
    value: category.id,
  }));
});

const statusOptions = [
  { title: '全部', value: null },
  { title: '可借阅', value: 1 },
  { title: '已下架', value: 0 },
];

// 处理搜索
const handleSearch = async () => {
  currentPage.value = 1;
  await fetchBooks();
};

// 处理重置
const handleReset = async () => {
  searchForm.keyword = '';
  searchForm.categoryId = null;
  searchForm.status = null;
  currentPage.value = 1;
  await fetchBooks();
};

// 处理清除搜索
const handleClearSearch = async () => {
  searchForm.keyword = '';
  await handleSearch();
};

// 处理页码变化
const handlePageChange = async (page: number) => {
  currentPage.value = page;
  await fetchBooks();
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

// 获取图书列表
const fetchBooks = async () => {
  try {
    await bookStore.fetchBookList({
      current: currentPage.value,
      size: 12,
      keyword: searchForm.keyword || undefined,
      categoryId: searchForm.categoryId || undefined,
      status: searchForm.status || undefined,
    });

    // 批量检查收藏状态
    if (bookStore.books.length > 0) {
      const bookIds = bookStore.books.map(book => book.id);
      await favoriteStore.batchCheckFavoriteStatusAction(bookIds);
    }
  } catch (error) {
    console.error('获取图书列表失败:', error);
    showMessage('获取图书列表失败', 'error');
  }
};

// 处理查看详情
const handleViewDetail = (book: Book) => {
  router.push(`/books/${book.id}`);
};

// 处理借阅
const handleBorrow = (book: Book) => {
  borrowDialog.book = book;
  borrowDialog.show = true;
};

// 处理确认借阅
const handleConfirmBorrow = async (borrowData: { bookId: number; remarks?: string }) => {
  try {
    await borrowStore.borrowBookAction(borrowData);
    borrowDialog.show = false;
    showMessage('借阅申请成功', 'success');
    // 刷新图书列表
    await fetchBooks();
  } catch (error: any) {
    console.error('借阅失败:', error);
    showMessage(error.message || '借阅失败', 'error');
  }
};

// 处理收藏切换
const handleToggleFavorite = async (book: Book) => {
  try {
    const response = await favoriteStore.toggleFavoriteAction(book.id);
    showMessage(response.data.message, 'success');
  } catch (error: any) {
    console.error('收藏操作失败:', error);
    showMessage(error.message || '收藏操作失败', 'error');
  }
};

// 显示消息
const showMessage = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'success') => {
  messageStore.showMessage(message, type);
};

// 监听路由查询参数
watch(
  () => route.query,
  (newQuery) => {
    if (newQuery.keyword) {
      searchForm.keyword = newQuery.keyword as string;
    }
    if (newQuery.categoryId) {
      searchForm.categoryId = Number(newQuery.categoryId);
    }
    if (newQuery.status) {
      searchForm.status = Number(newQuery.status);
    }
  },
  { immediate: true }
);

// 组件挂载
onMounted(async () => {
  try {
    // 获取分类列表
    await bookStore.fetchAllCategories();
    // 获取图书列表
    await fetchBooks();
  } catch (error) {
    console.error('初始化失败:', error);
    showMessage('页面初始化失败', 'error');
  }
});
</script>

<style scoped>
.book-list-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  border-radius: 16px !important;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(66, 165, 245, 0.02) 100%);
}

.books-grid {
  min-height: 400px;
}

.books-row {
  margin: 0 -8px; /* 调整间距 */
}

.book-col {
  padding: 8px !important; /* 统一卡片间距 */
  display: flex;
}

.book-col > * {
  width: 100%;
}

.pagination-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .book-list-container {
    max-width: 1200px;
  }

  .books-row {
    margin: 0 -6px;
  }

  .book-col {
    padding: 6px !important;
  }
}

@media (max-width: 768px) {
  .book-list-container {
    padding: 16px;
    max-width: 100%;
  }

  .books-row {
    margin: 0 -4px;
  }

  .book-col {
    padding: 4px !important;
  }
}

@media (max-width: 480px) {
  .book-list-container {
    padding: 12px;
  }

  .books-row {
    margin: 0 -2px;
  }

  .book-col {
    padding: 2px !important;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .search-card {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.05) 0%, rgba(25, 118, 210, 0.05) 100%);
  }
}
</style>
