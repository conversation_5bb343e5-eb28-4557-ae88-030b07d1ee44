<template>
  <div class="book-detail-container">
    <!-- 加载状态 -->
    <div v-if="bookStore.isLoading" class="loading-section">
      <v-row>
        <v-col cols="12" md="4">
          <v-skeleton-loader type="image" height="400" />
        </v-col>
        <v-col cols="12" md="8">
          <v-skeleton-loader type="article" />
        </v-col>
      </v-row>
    </div>

    <!-- 图书详情 -->
    <div v-else-if="book" class="book-detail-content">
      <!-- 返回按钮 -->
      <div class="back-section mb-4">
        <v-btn
          variant="outlined"
          prepend-icon="mdi-arrow-left"
          @click="handleGoBack"
        >
          返回图书列表
        </v-btn>
      </div>

      <v-row>
        <!-- 图书封面 -->
        <v-col cols="12" md="4">
          <v-card class="cover-card" elevation="4">
            <v-img
              :src="book.coverUrl || defaultCoverUrl"
              :alt="book.title"
              aspect-ratio="3/5"
              :cover="false"
              contain
              class="book-cover-large"
            >
              <template #placeholder>
                <div class="cover-placeholder-large">
                  <v-icon size="80" color="grey-lighten-2">mdi-book</v-icon>
                </div>
              </template>
            </v-img>
            
            <!-- 操作按钮 -->
            <v-card-actions class="cover-actions">
              <v-btn
                v-if="isAvailable"
                color="primary"
                variant="elevated"
                size="large"
                prepend-icon="mdi-book-plus"
                @click="handleBorrow"
                block
              >
                借阅图书
              </v-btn>
              <v-btn
                v-else
                color="grey"
                variant="outlined"
                size="large"
                disabled
                block
              >
                暂不可借
              </v-btn>
              
              <v-btn
                icon
                variant="outlined"
                size="large"
                @click="handleToggleFavorite"
                class="ml-2"
              >
                <v-icon :color="isFavorited ? 'red' : 'grey'">
                  {{ isFavorited ? 'mdi-heart' : 'mdi-heart-outline' }}
                </v-icon>
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-col>

        <!-- 图书信息 -->
        <v-col cols="12" md="8">
          <div class="book-info-section">
            <!-- 基本信息 -->
            <div class="basic-info mb-6">
              <h1 class="book-title text-h3 font-weight-bold mb-3">
                {{ book.title }}
              </h1>
              
              <div class="book-meta mb-4">
                <v-chip
                  :color="statusColor"
                  variant="elevated"
                  size="large"
                  class="mb-2 me-2"
                >
                  {{ statusText }}
                </v-chip>
                
                <v-chip
                  v-if="book.categoryName"
                  color="primary"
                  variant="tonal"
                  size="large"
                  class="mb-2"
                >
                  {{ book.categoryName }}
                </v-chip>
              </div>

              <!-- 详细信息表格 -->
              <v-table class="info-table mb-4">
                <tbody>
                  <tr>
                    <td class="info-label">作者</td>
                    <td class="info-value">{{ book.author }}</td>
                  </tr>
                  <tr v-if="book.publisher">
                    <td class="info-label">出版社</td>
                    <td class="info-value">{{ book.publisher }}</td>
                  </tr>
                  <tr v-if="book.publishDate">
                    <td class="info-label">出版日期</td>
                    <td class="info-value">{{ formatDate(book.publishDate) }}</td>
                  </tr>
                  <tr v-if="book.isbn">
                    <td class="info-label">ISBN</td>
                    <td class="info-value">{{ book.isbn }}</td>
                  </tr>
                  <tr v-if="book.price">
                    <td class="info-label">价格</td>
                    <td class="info-value">
                      <span class="text-h6 font-weight-bold text-primary">
                        ¥{{ book.price }}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">库存</td>
                    <td class="info-value">
                      <div class="d-flex align-center">
                        <span class="me-2">{{ book.availableQuantity }}/{{ book.totalQuantity }}</span>
                        <v-progress-linear
                          :model-value="stockPercentage"
                          :color="stockColor"
                          height="8"
                          rounded
                          class="flex-grow-1"
                          style="max-width: 200px;"
                        />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </v-table>
            </div>

            <!-- 图书描述 -->
            <div v-if="book.description" class="description-section">
              <h2 class="text-h5 font-weight-bold mb-3">图书简介</h2>
              <v-card variant="tonal" class="description-card">
                <v-card-text>
                  <p class="text-body-1 line-height-relaxed">
                    {{ book.description }}
                  </p>
                </v-card-text>
              </v-card>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-section">
      <v-card class="text-center pa-8" variant="tonal" color="error">
        <v-icon size="64" color="error" class="mb-4">
          mdi-book-remove
        </v-icon>
        <h3 class="text-h6 mb-2">图书不存在</h3>
        <p class="text-body-2 text-medium-emphasis mb-4">
          抱歉，您查找的图书不存在或已被删除
        </p>
        <v-btn
          variant="outlined"
          prepend-icon="mdi-arrow-left"
          @click="handleGoBack"
        >
          返回图书列表
        </v-btn>
      </v-card>
    </div>

    <!-- 借阅对话框 -->
    <BorrowDialog
      v-model="borrowDialog.show"
      :book="borrowDialog.book"
      @confirm="handleConfirmBorrow"
    />

    <!-- 消息提示 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="snackbar.timeout"
      location="top"
    >
      {{ snackbar.message }}
      <template #actions>
        <v-btn variant="text" @click="snackbar.show = false">
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useBookStore } from '@/stores/book';
import { useBorrowStore } from '@/stores/borrow';
import { useFavoriteStore } from '@/stores/favorite';
import { useMessageStore } from '@/stores/message';
import BorrowDialog from '@/components/books/BorrowDialog.vue';
import type { Book } from '@/types';

const route = useRoute();
const router = useRouter();
const bookStore = useBookStore();
const borrowStore = useBorrowStore();
const favoriteStore = useFavoriteStore();
const messageStore = useMessageStore();

// 响应式状态
const borrowDialog = reactive({
  show: false,
  book: null as Book | null,
});

const snackbar = reactive({
  show: false,
  message: '',
  color: 'success',
  timeout: 3000,
});

// 计算属性
const book = computed(() => bookStore.currentBook);

const isAvailable = computed(() => {
  return book.value && book.value.status === 1 && book.value.availableQuantity > 0;
});

const statusColor = computed(() => {
  if (!book.value) return 'grey';
  return book.value.status === 1 ? 'success' : 'error';
});

const statusText = computed(() => {
  if (!book.value) return '未知';
  return book.value.status === 1 ? '可借阅' : '已下架';
});

const stockColor = computed(() => {
  if (!book.value) return 'grey';
  const { availableQuantity, totalQuantity } = book.value;
  const ratio = availableQuantity / totalQuantity;
  
  if (ratio === 0) return 'error';
  if (ratio <= 0.2) return 'warning';
  if (ratio <= 0.5) return 'info';
  return 'success';
});

const stockPercentage = computed(() => {
  if (!book.value) return 0;
  const { availableQuantity, totalQuantity } = book.value;
  return (availableQuantity / totalQuantity) * 100;
});

const defaultCoverUrl = computed(() => {
  if (!book.value) return '';
  return `https://via.placeholder.com/400x667/1976d2/ffffff?text=${encodeURIComponent(book.value.title)}`;
});

const isFavorited = computed(() => {
  return book.value ? favoriteStore.isFavorited(book.value.id) : false;
});

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN');
};

// 处理返回
const handleGoBack = () => {
  router.push('/books');
};

// 处理借阅
const handleBorrow = () => {
  if (!book.value) return;
  borrowDialog.book = book.value;
  borrowDialog.show = true;
};

// 处理确认借阅
const handleConfirmBorrow = async (borrowData: { bookId: number; remarks?: string }) => {
  try {
    await borrowStore.borrowBookAction(borrowData);
    borrowDialog.show = false;
    showMessage('借阅申请成功', 'success');
    // 刷新图书信息
    await fetchBookDetail();
  } catch (error: any) {
    console.error('借阅失败:', error);
    showMessage(error.message || '借阅失败', 'error');
  }
};

// 处理收藏切换
const handleToggleFavorite = async () => {
  try {
    if (!book.value) return;
    const response = await favoriteStore.toggleFavoriteAction(book.value.id);
    showMessage(response.data.message, 'success');
  } catch (error: any) {
    console.error('收藏操作失败:', error);
    showMessage(error.message || '收藏操作失败', 'error');
  }
};

// 获取图书详情
const fetchBookDetail = async () => {
  const bookId = Number(route.params.id);
  if (!bookId) {
    showMessage('无效的图书ID', 'error');
    return;
  }

  try {
    await bookStore.fetchBookById(bookId);
    // 检查收藏状态
    if (bookStore.currentBook) {
      await favoriteStore.checkFavoriteStatus(bookStore.currentBook.id);
    }
  } catch (error: any) {
    console.error('获取图书详情失败:', error);
    showMessage(error.message || '获取图书详情失败', 'error');
  }
};

// 显示消息
const showMessage = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'success') => {
  messageStore.showMessage(message, type);
};

// 组件挂载
onMounted(async () => {
  await fetchBookDetail();
});
</script>

<style scoped>
.book-detail-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.loading-section,
.error-section {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-section {
  margin-bottom: 24px;
}

.cover-card {
  border-radius: 16px !important;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.book-cover-large {
  border-radius: 16px 16px 0 0 !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.cover-placeholder-large {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px 16px 0 0;
}

.cover-actions {
  padding: 16px !important;
  background: rgba(25, 118, 210, 0.02);
}

.book-info-section {
  padding: 0 16px;
}

.book-title {
  line-height: 1.2;
  color: #1976d2;
}

.book-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.info-table {
  border-radius: 12px !important;
  overflow: hidden;
}

.info-label {
  font-weight: 600;
  color: rgba(0, 0, 0, 0.7);
  width: 120px;
  padding: 12px 16px !important;
  background: rgba(25, 118, 210, 0.05);
}

.info-value {
  padding: 12px 16px !important;
}

.description-section {
  margin-top: 32px;
}

.description-card {
  border-radius: 12px !important;
}

.line-height-relaxed {
  line-height: 1.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .book-detail-container {
    padding: 16px;
  }
  
  .book-title {
    font-size: 1.8rem !important;
  }
  
  .info-label {
    width: 100px;
    font-size: 14px;
  }
  
  .cover-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .cover-actions .v-btn {
    width: 100%;
    margin: 0 !important;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .cover-placeholder-large {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
  }
  
  .cover-actions {
    background: rgba(66, 165, 245, 0.05);
  }
  
  .info-label {
    color: rgba(255, 255, 255, 0.7);
    background: rgba(66, 165, 245, 0.1);
  }
}
</style>
