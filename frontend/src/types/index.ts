// 通用类型定义
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp?: number;
  success?: boolean;
}

export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  realName: string;
  phone?: string;
  role: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  status: number;
  avatar?: string;
  lastLoginTime?: string;
  createdTime?: string;
  updatedTime?: string;
}

export interface LoginForm {
  username: string;
  password: string;
}

export interface RegisterForm {
  username: string;
  password: string;
  confirmPassword: string;
  email: string;
  realName: string;
  phone?: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}

// 图书相关类型
export interface Book {
  id: number;
  isbn?: string;
  title: string;
  author: string;
  publisher?: string;
  publishDate?: string;
  categoryId?: number;
  categoryName?: string;
  description?: string;
  coverUrl?: string;
  price?: number;
  totalQuantity: number;
  availableQuantity: number;
  status: number;
  createdTime?: string;
  updatedTime?: string;
}

export interface BookForm {
  isbn?: string;
  title: string;
  author: string;
  publisher?: string;
  publishDate?: string;
  categoryId: number;
  description?: string;
  coverUrl?: string;
  price?: number;
  totalQuantity: number;
  availableQuantity: number;
  status?: number;
}

// 图书分类类型
export interface BookCategory {
  id: number;
  name: string;
  description?: string;
  parentId?: number;
  children?: BookCategory[];
}

// 借阅记录类型
export interface BorrowRecord {
  id: number;
  userId: number;
  bookId: number;
  // 后端字段名
  borrowTime: string;
  dueTime: string;
  returnTime?: string;
  // 兼容前端字段名
  borrowDate?: string;
  dueDate?: string;
  returnDate?: string;
  status: 'BORROWED' | 'RETURNED' | 'OVERDUE' | 'RENEWED';
  renewCount?: number;
  overdueDays?: number;
  fineAmount?: number;
  fine?: number;
  remarks?: string;
  // 用户信息
  username?: string;
  userRealName?: string;
  user?: User;
  // 图书信息
  bookTitle?: string;
  bookAuthor?: string;
  bookIsbn?: string;
  bookCoverUrl?: string;
  book?: Book;
  createdTime?: string;
  updatedTime?: string;
}

export interface BorrowForm {
  bookId: number;
  remarks?: string;
}

// 收藏相关类型
export interface UserFavorite {
  id: number;
  userId: number;
  username: string;
  userRealName: string;
  bookId: number;
  bookTitle: string;
  bookAuthor: string;
  bookIsbn?: string;
  bookCategory?: string;
  bookCoverUrl?: string;
  bookStatus: number;
  bookAvailableQuantity: number;
  favoriteTime: string;
  createdTime?: string;
}

export interface FavoriteForm {
  bookId: number;
  action?: boolean;
}

// 路由相关类型
export interface RouteItem {
  path: string;
  name: string;
  component?: any;
  meta?: {
    title?: string;
    icon?: string;
    roles?: string[];
    requireAuth?: boolean;
  };
  children?: RouteItem[];
}

// 菜单项类型
export interface MenuItem {
  title: string;
  icon: string;
  to?: string;
  children?: MenuItem[];
  roles?: string[];
}

// 表格查询参数类型
export interface QueryParams {
  current: number;
  size: number;
  keyword?: string;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  [key: string]: any;
}

// 用户查询参数
export interface UserQueryParams extends QueryParams {
  role?: string;
  status?: number;
  startDate?: string;
  endDate?: string;
}

// 图书查询参数
export interface BookQueryParams extends QueryParams {
  categoryId?: number;
  status?: number;
  author?: string;
  publisher?: string;
  isbn?: string;
  priceMin?: number;
  priceMax?: number;
}

// 借阅查询参数
export interface BorrowQueryParams extends QueryParams {
  userId?: number;
  bookId?: number;
  status?: string;
  startDate?: string;
  endDate?: string;
  overdue?: boolean;
}

// 统计数据类型
export interface DashboardStats {
  totalBooks: number;
  totalUsers: number;
  totalBorrows: number;
  overdueBooks: number;
}

// 详细统计数据
export interface Statistics {
  totalUsers: number;
  totalBooks: number;
  totalBorrows: number;
  activeBorrows: number;
  overdueBorrows: number;
  todayBorrows: number;
  todayReturns: number;
  popularBooks: Book[];
  recentActivities: Activity[];
}

// 活动记录类型
export interface Activity {
  id: number;
  type: 'BORROW' | 'RETURN' | 'RENEW' | 'REGISTER' | 'LOGIN';
  userId: number;
  userName: string;
  description: string;
  timestamp: string;
  details?: any;
}

// 文件上传响应
export interface UploadResponse {
  url: string;
  filename: string;
  size: number;
  type: string;
}

// 验证码响应
export interface CaptchaResponse {
  key: string;
  image: string; // base64编码的图片
}

// 密码重置请求
export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// 修改密码请求
export interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 借阅请求参数
export interface BorrowRequest {
  bookId: number;
  remarks?: string;
}

// 归还请求参数
export interface ReturnRequest {
  borrowId: number;
  remarks?: string;
}

// 续借请求参数
export interface RenewRequest {
  borrowId: number;
  remarks?: string;
}

// 分类类型别名
export interface Category extends BookCategory {}
