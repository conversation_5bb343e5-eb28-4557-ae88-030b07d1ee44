// 系统配置文件

// 环境变量类型定义
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_APP_VERSION: string;
  readonly VITE_API_BASE_URL: string;
  readonly VITE_API_TIMEOUT: string;
  readonly VITE_ENABLE_MOCK: string;
  readonly VITE_ENABLE_DEVTOOLS: string;
  readonly VITE_LOG_LEVEL: string;
}

// 应用配置
export const appConfig = {
  // 应用信息
  title: import.meta.env.VITE_APP_TITLE || '图书管理系统',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  description: '基于Vue 3 + TypeScript + Vuetify的现代化图书管理系统',
  
  // 开发者信息
  author: 'Library Management Team',
  email: '<EMAIL>',
  
  // 功能开关
  features: {
    enableMock: import.meta.env.VITE_ENABLE_MOCK === 'true',
    enableDevTools: import.meta.env.VITE_ENABLE_DEVTOOLS === 'true' || import.meta.env.DEV,
    enablePWA: false,
    enableI18n: false,
  },
  
  // 主题配置
  theme: {
    defaultTheme: 'light',
    primaryColor: '#1976d2',
    secondaryColor: '#42a5f5',
    accentColor: '#82B1FF',
    borderRadius: '16px',
  },
  
  // 布局配置
  layout: {
    sidebarWidth: 280,
    sidebarMiniWidth: 64,
    headerHeight: 64,
    footerHeight: 48,
  },
  
  // 分页配置
  pagination: {
    defaultPageSize: 10,
    pageSizeOptions: [5, 10, 20, 50, 100],
    showSizeChanger: true,
    showQuickJumper: true,
  },
  
  // 表格配置
  table: {
    defaultPageSize: 10,
    showSelection: true,
    showIndex: true,
    stripe: true,
    border: false,
  },
  
  // 上传配置
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
    maxFiles: 5,
  },
  
  // 缓存配置
  cache: {
    tokenKey: 'library_token',
    userKey: 'library_user',
    settingsKey: 'library_settings',
    themeKey: 'library_theme',
    expireTime: 7 * 24 * 60 * 60 * 1000, // 7天
  },
};

// API配置
export const apiConfig = {
  // 基础URL
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  
  // 超时时间
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
  
  // 重试配置
  retry: {
    times: 3,
    delay: 1000,
  },
  
  // 请求头
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // 状态码配置
  statusCodes: {
    success: 200,
    created: 201,
    noContent: 204,
    badRequest: 400,
    unauthorized: 401,
    forbidden: 403,
    notFound: 404,
    conflict: 409,
    internalError: 500,
    serviceUnavailable: 503,
  },
};

// 日志配置
export const logConfig = {
  level: import.meta.env.VITE_LOG_LEVEL || (import.meta.env.DEV ? 'debug' : 'warn'),
  enableConsole: import.meta.env.DEV,
  enableRemote: import.meta.env.PROD,
  remoteURL: '/api/logs',
  maxLogSize: 1000,
  levels: {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3,
  },
};

// 业务配置
export const businessConfig = {
  // 借阅配置
  borrow: {
    maxBorrowDays: 30, // 最大借阅天数
    maxRenewTimes: 1, // 最大续借次数
    maxBorrowBooks: 5, // 最大借阅数量
    finePerDay: 1.0, // 每日罚金
    renewDays: 30, // 续借天数
  },
  
  // 用户配置
  user: {
    defaultRole: 'USER',
    passwordMinLength: 6,
    passwordMaxLength: 20,
    usernameMinLength: 3,
    usernameMaxLength: 20,
    realNameMaxLength: 50,
    phonePattern: /^1[3-9]\d{9}$/,
    emailPattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  
  // 图书配置
  book: {
    titleMaxLength: 100,
    authorMaxLength: 50,
    publisherMaxLength: 50,
    descriptionMaxLength: 1000,
    isbnPattern: /^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/,
    priceMin: 0,
    priceMax: 9999.99,
    quantityMin: 0,
    quantityMax: 9999,
  },
  
  // 搜索配置
  search: {
    minKeywordLength: 1,
    maxKeywordLength: 50,
    debounceDelay: 300,
    highlightClass: 'search-highlight',
  },
};

// 路由配置
export const routeConfig = {
  // 默认路由
  defaultRoute: '/dashboard',
  loginRoute: '/login',
  
  // 角色路由映射
  roleRoutes: {
    USER: '/dashboard',
    ADMIN: '/admin/dashboard',
    SUPER_ADMIN: '/admin/dashboard',
  },
  
  // 公开路由（无需认证）
  publicRoutes: [
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password',
    '/404',
  ],
  
  // 管理员路由
  adminRoutes: [
    '/admin',
    '/admin/dashboard',
    '/admin/books',
    '/admin/borrows',
    '/admin/users',
  ],
};

// 消息配置
export const messageConfig = {
  // 默认超时时间
  defaultTimeout: 3000,
  
  // 不同类型的超时时间
  timeouts: {
    success: 3000,
    info: 3000,
    warning: 4000,
    error: 5000,
  },
  
  // 最大消息数量
  maxMessages: 5,
  
  // 位置配置
  position: 'top',
  
  // 动画配置
  animation: {
    enter: 'slide-down',
    leave: 'slide-up',
    duration: 300,
  },
};

// 开发配置
export const devConfig = {
  // 是否启用开发工具
  enableDevTools: import.meta.env.DEV,
  
  // 是否启用热重载
  enableHMR: import.meta.env.DEV,
  
  // 是否显示性能信息
  showPerformance: import.meta.env.DEV,
  
  // 是否启用调试模式
  enableDebug: import.meta.env.DEV,
  
  // Mock数据配置
  mock: {
    enabled: import.meta.env.VITE_ENABLE_MOCK === 'true',
    delay: 500, // 模拟网络延迟
    errorRate: 0.1, // 错误率
  },
};

// 导出所有配置
export default {
  app: appConfig,
  api: apiConfig,
  log: logConfig,
  business: businessConfig,
  route: routeConfig,
  message: messageConfig,
  dev: devConfig,
};

// 配置验证函数
export function validateConfig(): boolean {
  try {
    // 验证必要的环境变量
    const requiredEnvVars = ['VITE_API_BASE_URL'];
    
    for (const envVar of requiredEnvVars) {
      if (!import.meta.env[envVar]) {
        console.warn(`Missing required environment variable: ${envVar}`);
      }
    }
    
    // 验证API配置
    if (!apiConfig.baseURL) {
      console.error('API base URL is not configured');
      return false;
    }
    
    if (apiConfig.timeout <= 0) {
      console.error('API timeout must be greater than 0');
      return false;
    }
    
    // 验证业务配置
    if (businessConfig.borrow.maxBorrowDays <= 0) {
      console.error('Max borrow days must be greater than 0');
      return false;
    }
    
    if (businessConfig.borrow.maxBorrowBooks <= 0) {
      console.error('Max borrow books must be greater than 0');
      return false;
    }
    
    console.log('Configuration validation passed');
    return true;
  } catch (error) {
    console.error('Configuration validation failed:', error);
    return false;
  }
}

// 获取运行时配置
export function getRuntimeConfig() {
  return {
    isDevelopment: import.meta.env.DEV,
    isProduction: import.meta.env.PROD,
    mode: import.meta.env.MODE,
    baseUrl: import.meta.env.BASE_URL,
    timestamp: Date.now(),
  };
}
