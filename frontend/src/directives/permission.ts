import type { App, DirectiveBinding } from 'vue';
import { useUserStore } from '@/stores/user';
import { hasPermission } from '@/utils/permission';
import type { RoleType } from '@/utils/permission';

interface PermissionBinding {
  roles?: RoleType[];
  requireActive?: boolean;
}

/**
 * 权限指令
 * 用法：
 * v-permission="['ADMIN', 'SUPER_ADMIN']"
 * v-permission="{ roles: ['ADMIN'], requireActive: true }"
 */
const permissionDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding<RoleType[] | PermissionBinding>) {
    checkPermission(el, binding);
  },
  updated(el: HTMLElement, binding: DirectiveBinding<RoleType[] | PermissionBinding>) {
    checkPermission(el, binding);
  },
};

function checkPermission(el: HTMLElement, binding: DirectiveBinding<RoleType[] | PermissionBinding>) {
  const userStore = useUserStore();
  
  let roles: RoleType[] = [];
  let requireActive = true;
  
  // 解析指令参数
  if (Array.isArray(binding.value)) {
    roles = binding.value;
  } else if (binding.value && typeof binding.value === 'object') {
    roles = binding.value.roles || [];
    requireActive = binding.value.requireActive !== false;
  }
  
  // 检查权限
  const hasAuth = hasPermission(userStore.userInfo, roles, requireActive);
  
  if (!hasAuth) {
    // 没有权限，隐藏元素
    el.style.display = 'none';
    el.setAttribute('data-permission-hidden', 'true');
  } else {
    // 有权限，显示元素
    if (el.getAttribute('data-permission-hidden') === 'true') {
      el.style.display = '';
      el.removeAttribute('data-permission-hidden');
    }
  }
}

/**
 * 角色指令
 * 用法：
 * v-role="'ADMIN'"
 * v-role="['ADMIN', 'SUPER_ADMIN']"
 */
const roleDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding<RoleType | RoleType[]>) {
    checkRole(el, binding);
  },
  updated(el: HTMLElement, binding: DirectiveBinding<RoleType | RoleType[]>) {
    checkRole(el, binding);
  },
};

function checkRole(el: HTMLElement, binding: DirectiveBinding<RoleType | RoleType[]>) {
  const userStore = useUserStore();
  
  const roles = Array.isArray(binding.value) ? binding.value : [binding.value];
  const hasAuth = hasPermission(userStore.userInfo, roles);
  
  if (!hasAuth) {
    el.style.display = 'none';
    el.setAttribute('data-role-hidden', 'true');
  } else {
    if (el.getAttribute('data-role-hidden') === 'true') {
      el.style.display = '';
      el.removeAttribute('data-role-hidden');
    }
  }
}

/**
 * 安装权限指令
 */
export function setupPermissionDirectives(app: App) {
  app.directive('permission', permissionDirective);
  app.directive('role', roleDirective);
}

export { permissionDirective, roleDirective };
