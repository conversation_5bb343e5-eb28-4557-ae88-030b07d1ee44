import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createVuetify } from 'vuetify'
import { aliases, mdi } from 'vuetify/iconsets/mdi'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'

// Vuetify样式
import 'vuetify/styles'
import '@mdi/font/css/materialdesignicons.css'

// 自定义样式
import './styles/global.css'

import App from './App.vue'
import router from './router'
import { setupPermissionDirectives } from './directives/permission'
import { defaultThemes, applyCSSVariables } from './styles/theme'
import { useUserStore } from './stores/user'

// 创建Vuetify实例
const vuetify = createVuetify({
  components,
  directives,
  icons: {
    defaultSet: 'mdi',
    aliases,
    sets: {
      mdi,
    },
  },
  theme: {
    defaultTheme: 'light',
    themes: defaultThemes,
  },
})

// 创建Pinia实例
const pinia = createPinia()

// 创建应用实例
const app = createApp(App)

app.use(pinia)
app.use(router)
app.use(vuetify)

// 注册权限指令
setupPermissionDirectives(app)

// 应用CSS变量
applyCSSVariables()

// 初始化用户状态
const initializeApp = async () => {
  const userStore = useUserStore()

  try {
    console.log('开始初始化应用...')

    // 初始化认证状态
    await userStore.initializeAuth()

    console.log('应用初始化完成')
  } catch (error) {
    console.error('应用初始化失败:', error)
    // 即使初始化失败也要挂载应用，让路由守卫处理后续逻辑
  }

  // 挂载应用
  app.mount('#app')
}

// 启动应用
initializeApp()
