<template>
  <v-card
    class="book-card"
    :class="{ 'book-card-unavailable': !isAvailable }"
    elevation="2"
    @click="handleCardClick"
  >
    <!-- 图书封面 -->
    <div class="book-cover-container">
      <v-img
        :src="book.coverUrl || defaultCoverUrl"
        :alt="book.title"
        class="book-cover"
        aspect-ratio="3/5"
        :cover="false"
        contain
      >
        <template #placeholder>
          <div class="cover-placeholder">
            <v-icon size="48" color="grey-lighten-2">mdi-book</v-icon>
          </div>
        </template>
        
        <!-- 状态标签 -->
        <div class="status-overlay">
          <v-chip
            v-if="!isAvailable"
            color="error"
            size="small"
            variant="elevated"
            class="status-chip"
          >
            不可借
          </v-chip>
          <v-chip
            v-else-if="book.availableQuantity <= 3"
            color="warning"
            size="small"
            variant="elevated"
            class="status-chip"
          >
            仅剩{{ book.availableQuantity }}本
          </v-chip>
        </div>

        <!-- 收藏按钮 -->
        <div class="favorite-overlay">
          <v-btn
            icon
            size="small"
            variant="elevated"
            color="white"
            class="favorite-btn"
            @click.stop="handleToggleFavorite"
          >
            <v-icon :color="isFavorited ? 'red' : 'grey'">
              {{ isFavorited ? 'mdi-heart' : 'mdi-heart-outline' }}
            </v-icon>
          </v-btn>
        </div>
      </v-img>
    </div>

    <!-- 图书信息 -->
    <v-card-text class="book-info">
      <!-- 主要信息区域 -->
      <div class="book-main-info">
        <!-- 标题 -->
        <h3 class="book-title text-h6 font-weight-bold mb-2">
          {{ book.title }}
        </h3>

        <!-- 作者 -->
        <p class="book-author text-body-2 text-medium-emphasis mb-1">
          <v-icon size="16" class="me-1">mdi-account</v-icon>
          {{ book.author }}
        </p>

        <!-- 出版社 -->
        <p v-if="book.publisher" class="book-publisher text-body-2 text-medium-emphasis mb-1">
          <v-icon size="16" class="me-1">mdi-domain</v-icon>
          {{ book.publisher }}
        </p>

        <!-- 分类 -->
        <p v-if="book.categoryName" class="book-category text-body-2 text-medium-emphasis mb-2">
          <v-icon size="16" class="me-1">mdi-tag</v-icon>
          {{ book.categoryName }}
        </p>
      </div>

      <!-- 底部信息区域 -->
      <div class="book-bottom-info mt-auto">
        <!-- 价格和库存 -->
        <div class="book-meta d-flex justify-space-between align-center mb-2">
          <div v-if="book.price" class="book-price">
            <span class="text-h6 font-weight-bold text-primary">
              ¥{{ book.price }}
            </span>
          </div>
          <div class="book-stock">
            <v-chip
              :color="stockColor"
              size="small"
              variant="tonal"
            >
              库存: {{ book.availableQuantity }}/{{ book.totalQuantity }}
            </v-chip>
          </div>
        </div>

        <!-- 描述 -->
        <p v-if="book.description" class="book-description text-body-2 text-ellipsis-2">
          {{ book.description }}
        </p>
      </div>
    </v-card-text>

    <!-- 操作按钮 -->
    <v-card-actions class="book-actions pa-4">
      <v-btn
        variant="outlined"
        size="small"
        prepend-icon="mdi-eye"
        @click.stop="handleViewDetail"
        class="flex-grow-1"
      >
        查看详情
      </v-btn>
      
      <v-btn
        v-if="isAvailable"
        color="primary"
        variant="elevated"
        size="small"
        prepend-icon="mdi-book-plus"
        @click.stop="handleBorrow"
        class="flex-grow-1 ml-2"
      >
        借阅
      </v-btn>
      
      <v-btn
        v-else
        color="grey"
        variant="outlined"
        size="small"
        disabled
        class="flex-grow-1 ml-2"
      >
        不可借
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { Book } from '@/types';

interface Props {
  book: Book;
  isFavorited?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isFavorited: false,
});

interface Emits {
  (e: 'view-detail', book: Book): void;
  (e: 'borrow', book: Book): void;
  (e: 'toggle-favorite', book: Book): void;
}

const emit = defineEmits<Emits>();

// 计算属性
const isAvailable = computed(() => {
  return props.book.status === 1 && props.book.availableQuantity > 0;
});

const stockColor = computed(() => {
  const { availableQuantity, totalQuantity } = props.book;
  const ratio = availableQuantity / totalQuantity;
  
  if (ratio === 0) return 'error';
  if (ratio <= 0.2) return 'warning';
  if (ratio <= 0.5) return 'info';
  return 'success';
});

const defaultCoverUrl = computed(() => {
  return `https://via.placeholder.com/300x500/1976d2/ffffff?text=${encodeURIComponent(props.book.title)}`;
});

// 事件处理
const handleCardClick = () => {
  handleViewDetail();
};

const handleViewDetail = () => {
  emit('view-detail', props.book);
};

const handleBorrow = () => {
  emit('borrow', props.book);
};

const handleToggleFavorite = () => {
  emit('toggle-favorite', props.book);
};
</script>

<style scoped>
.book-card {
  border-radius: 16px !important;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  min-height: 480px; /* 确保最小高度 */
  display: flex;
  flex-direction: column;
}

.book-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(25, 118, 210, 0.2) !important;
}

.book-card-unavailable {
  opacity: 0.7;
}

.book-cover-container {
  position: relative;
  overflow: hidden;
  height: 200px; /* 固定封面高度 */
  flex-shrink: 0; /* 防止压缩 */
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
}

.book-cover {
  border-radius: 16px 16px 0 0 !important;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.cover-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px 16px 0 0;
}

.status-overlay {
  position: absolute;
  top: 8px;
  left: 8px;
}

.status-chip {
  backdrop-filter: blur(10px);
}

.favorite-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.book-card:hover .favorite-overlay {
  opacity: 1;
}

.favorite-btn {
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.book-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许内容压缩 */
  padding: 16px !important; /* 确保内边距 */
}

.book-title {
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 2.6em;
}

.book-author,
.book-publisher,
.book-category {
  display: flex;
  align-items: center;
  line-height: 1.4;
}

.book-main-info {
  flex: 0 0 auto; /* 不伸缩，保持内容大小 */
}

.book-bottom-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  min-height: 0;
}

.book-meta {
  margin-top: auto;
}

.book-price {
  display: flex;
  align-items: center;
}

.book-description {
  flex: 1;
  line-height: 1.4;
  min-height: 2.8em; /* 确保描述区域最小高度 */
  margin-bottom: 8px;
}

.book-actions {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  background: rgba(25, 118, 210, 0.02);
  flex-shrink: 0; /* 防止按钮区域被压缩 */
  min-height: 64px; /* 确保按钮区域最小高度 */
  padding: 12px 16px !important; /* 确保内边距 */
}

/* 文字省略 */
.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .book-card {
    min-height: 460px;
  }

  .book-cover-container {
    height: 180px;
  }
}

@media (max-width: 768px) {
  .book-card {
    margin-bottom: 16px;
    min-height: 420px;
  }

  .book-cover-container {
    height: 160px;
  }

  .book-actions {
    flex-direction: column;
    gap: 8px;
    min-height: 80px;
  }

  .book-actions .v-btn {
    width: 100%;
    margin: 0 !important;
  }

  .book-title {
    font-size: 1.1rem !important;
  }
}

@media (max-width: 480px) {
  .book-card {
    min-height: 380px;
  }

  .book-cover-container {
    height: 140px;
  }

  .book-info {
    padding: 12px !important;
  }

  .book-actions {
    padding: 8px 12px !important;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .book-actions {
    border-top-color: rgba(255, 255, 255, 0.12);
    background: rgba(66, 165, 245, 0.05);
  }

  .book-cover-container {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
  }

  .book-cover {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
  }

  .cover-placeholder {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
  }
}
</style>
