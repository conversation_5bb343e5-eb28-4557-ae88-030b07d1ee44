<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    max-width="500"
    persistent
  >
    <v-card class="borrow-dialog">
      <!-- 对话框头部 -->
      <v-card-title class="dialog-header">
        <v-icon class="me-2" color="primary">mdi-book-plus</v-icon>
        借阅图书
      </v-card-title>

      <v-divider />

      <!-- 对话框内容 -->
      <v-card-text class="dialog-content">
        <!-- 图书信息 -->
        <div v-if="book" class="book-info-section mb-4">
          <div class="d-flex">
            <!-- 图书封面 -->
            <BookCover
              :src="book.coverUrl || defaultCoverUrl"
              :alt="book.title"
              :title="book.title"
              width="80px"
              aspect-ratio="3/5"
              :icon-size="24"
            />

            <!-- 图书详情 -->
            <div class="book-details ml-4 flex-grow-1">
              <h3 class="text-h6 font-weight-bold mb-1">{{ book.title }}</h3>
              <p class="text-body-2 text-medium-emphasis mb-1">
                <v-icon size="16" class="me-1">mdi-account</v-icon>
                {{ book.author }}
              </p>
              <p v-if="book.publisher" class="text-body-2 text-medium-emphasis mb-1">
                <v-icon size="16" class="me-1">mdi-domain</v-icon>
                {{ book.publisher }}
              </p>
              <div class="d-flex align-center">
                <v-chip
                  :color="stockColor"
                  size="small"
                  variant="tonal"
                >
                  可借: {{ book.availableQuantity }}本
                </v-chip>
              </div>
            </div>
          </div>
        </div>

        <!-- 借阅表单 -->
        <v-form ref="borrowFormRef" v-model="formValid">
          <!-- 借阅备注 -->
          <v-textarea
            v-model="borrowForm.remarks"
            label="借阅备注（可选）"
            placeholder="请输入借阅备注..."
            variant="outlined"
            rows="3"
            counter="200"
            :rules="remarksRules"
            hide-details="auto"
            class="mb-4"
          />

          <!-- 借阅须知 -->
          <v-card variant="tonal" color="info" class="mb-4">
            <v-card-text>
              <div class="d-flex align-start">
                <v-icon color="info" class="me-2 mt-1">mdi-information</v-icon>
                <div>
                  <h4 class="text-subtitle-2 font-weight-bold mb-2">借阅须知</h4>
                  <ul class="text-body-2 pl-4">
                    <li>借阅期限为30天，请按时归还</li>
                    <li>逾期将产生每日1元的罚金</li>
                    <li>图书损坏或丢失需要赔偿</li>
                    <li>每人最多可借阅5本图书</li>
                  </ul>
                </div>
              </div>
            </v-card-text>
          </v-card>

          <!-- 借阅限制检查 -->
          <div v-if="borrowCheckResult" class="borrow-check-section mb-4">
            <!-- 当前借阅状态 -->
            <div v-if="currentBorrowCount !== null" class="borrow-status mb-3">
              <div class="d-flex justify-space-between align-center">
                <span class="text-body-2">当前借阅数量：</span>
                <v-chip
                  :color="currentBorrowCount >= 5 ? 'error' : 'success'"
                  size="small"
                  variant="tonal"
                >
                  {{ currentBorrowCount }}/5 本
                </v-chip>
              </div>
            </div>

            <!-- 当前借阅状态检查 -->
            <div v-if="borrowCheckResult.hasCurrentlyBorrowed" class="current-borrow-check mb-3">
              <v-alert
                type="error"
                variant="tonal"
                density="compact"
              >
                <div class="alert-content">
                  <div class="font-weight-bold mb-1">无法借阅</div>
                  <div>您当前正在借阅这本图书，无法重复借阅。</div>
                </div>
              </v-alert>
            </div>



            <!-- 其他限制提醒 -->
            <div v-if="!canBorrow" class="restriction-alert">
              <v-alert
                type="error"
                variant="tonal"
                density="compact"
              >
                <div class="alert-content">
                  <div class="font-weight-bold mb-1">无法借阅</div>
                  <div>{{ borrowRestrictionReason }}</div>
                </div>
              </v-alert>
            </div>
          </div>
        </v-form>
      </v-card-text>

      <!-- 对话框操作 -->
      <v-card-actions class="dialog-actions">
        <v-spacer />
        <v-btn
          variant="outlined"
          @click="handleCancel"
          :disabled="isLoading"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          @click="handleConfirm"
          :loading="isLoading"
          :disabled="!canBorrow || !formValid"
        >
          确认借阅
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useBorrowStore } from '@/stores/borrow';
import { borrowApi } from '@/api/borrow';
import { formatDate } from '@/utils/date';
import type { Book } from '@/types';
import BookCover from '@/components/common/BookCover.vue';

interface Props {
  modelValue: boolean;
  book: Book | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm', data: { bookId: number; remarks?: string }): void;
}

const emit = defineEmits<Emits>();

const borrowStore = useBorrowStore();

// 响应式状态
const formValid = ref(false);
const isLoading = ref(false);
const currentBorrowCount = ref<number | null>(null);
const borrowFormRef = ref();
const borrowCheckResult = ref<{
  hasCurrentlyBorrowed: boolean;
  hasHistoryBorrowed: boolean;
  lastBorrowDate?: string;
  canBorrowAgain: boolean;
  reason?: string;
} | null>(null);

const borrowForm = reactive({
  remarks: '',
});

// 计算属性
const stockColor = computed(() => {
  if (!props.book) return 'grey';
  const { availableQuantity, totalQuantity } = props.book;
  const ratio = availableQuantity / totalQuantity;
  
  if (ratio === 0) return 'error';
  if (ratio <= 0.2) return 'warning';
  if (ratio <= 0.5) return 'info';
  return 'success';
});

const defaultCoverUrl = computed(() => {
  if (!props.book) return '';
  return `https://via.placeholder.com/120x200/1976d2/ffffff?text=${encodeURIComponent(props.book.title)}`;
});

// 借阅限制检查
const canBorrow = computed(() => {
  // 检查当前借阅数量
  if (currentBorrowCount.value !== null && currentBorrowCount.value >= 5) {
    return false;
  }

  // 检查是否当前正在借阅该图书（修复：只检查当前借阅状态）
  if (borrowCheckResult.value?.hasCurrentlyBorrowed) {
    return false;
  }

  // 检查图书是否可用
  if (!props.book || props.book.availableQuantity <= 0) {
    return false;
  }

  return true;
});

const borrowRestrictionReason = computed(() => {
  if (currentBorrowCount.value !== null && currentBorrowCount.value >= 5) {
    return '您已达到最大借阅数量限制（5本）';
  }

  if (borrowCheckResult.value?.hasCurrentlyBorrowed) {
    return '您当前正在借阅这本图书，无法重复借阅';
  }

  if (!props.book || props.book.availableQuantity <= 0) {
    return '图书库存不足，暂时无法借阅';
  }

  return '暂时无法借阅';
});

// 验证规则
const remarksRules = [
  (v: string) => !v || v.length <= 200 || '备注不能超过200个字符',
];

// 处理取消
const handleCancel = () => {
  emit('update:modelValue', false);
  resetForm();
};

// 处理确认
const handleConfirm = async () => {
  if (!props.book || !formValid.value) return;

  isLoading.value = true;
  try {
    emit('confirm', {
      bookId: props.book.id,
      remarks: borrowForm.remarks || undefined,
    });
  } finally {
    isLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  borrowForm.remarks = '';
  if (borrowFormRef.value) {
    borrowFormRef.value.resetValidation();
  }
};

// 获取当前借阅数量
const fetchCurrentBorrowCount = async () => {
  try {
    await borrowStore.fetchCurrentBorrowCount();
    currentBorrowCount.value = borrowStore.currentBorrowCount;
  } catch (error) {
    console.error('获取当前借阅数量失败:', error);
  }
};

// 检查用户借阅历史
const checkUserBookHistory = async () => {
  if (!props.book) return;

  try {
    const response = await borrowApi.checkUserBookHistory(props.book.id);
    borrowCheckResult.value = response.data;
  } catch (error) {
    console.error('检查借阅历史失败:', error);
    // 如果检查失败，默认允许借阅
    borrowCheckResult.value = {
      hasCurrentlyBorrowed: false,
      hasHistoryBorrowed: false,
      canBorrowAgain: true,
    };
  }
};

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (show) => {
    if (show) {
      resetForm();
      fetchCurrentBorrowCount();
      checkUserBookHistory();
    }
  }
);

// 监听图书变化
watch(
  () => props.book,
  (newBook) => {
    if (newBook && props.modelValue) {
      checkUserBookHistory();
    }
  }
);

// 组件挂载
onMounted(() => {
  fetchCurrentBorrowCount();
});
</script>

<style scoped>
.borrow-dialog {
  border-radius: 16px !important;
}

.dialog-header {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 100%);
  font-weight: 600;
}

.dialog-content {
  padding: 24px !important;
}

.book-info-section {
  background: rgba(25, 118, 210, 0.02);
  border-radius: 12px;
  padding: 16px;
}



.book-details {
  min-width: 0;
}

.borrow-status {
  background: rgba(76, 175, 80, 0.05);
  border-radius: 8px;
  padding: 12px;
}

.dialog-actions {
  padding: 16px 24px !important;
  background: rgba(0, 0, 0, 0.02);
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .dialog-header {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  }
  
  .book-info-section {
    background: rgba(66, 165, 245, 0.05);
  }
  
  .cover-placeholder-mini {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
  }
  
  .borrow-status {
    background: rgba(76, 175, 80, 0.1);
  }
  
  .dialog-actions {
    background: rgba(255, 255, 255, 0.02);
  }
}
</style>
