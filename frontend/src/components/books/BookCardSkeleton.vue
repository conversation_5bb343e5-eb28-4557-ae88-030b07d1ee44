<template>
  <v-card class="book-card-skeleton" elevation="2">
    <!-- 封面骨架 -->
    <v-skeleton-loader
      type="image"
      class="cover-skeleton"
      height="240"
    />

    <!-- 内容骨架 -->
    <v-card-text class="content-skeleton">
      <!-- 标题骨架 -->
      <v-skeleton-loader
        type="heading"
        class="mb-2"
      />
      
      <!-- 作者骨架 -->
      <v-skeleton-loader
        type="text"
        class="mb-1"
        width="60%"
      />
      
      <!-- 出版社骨架 -->
      <v-skeleton-loader
        type="text"
        class="mb-1"
        width="70%"
      />
      
      <!-- 分类骨架 -->
      <v-skeleton-loader
        type="text"
        class="mb-2"
        width="50%"
      />
      
      <!-- 价格和库存骨架 -->
      <div class="meta-skeleton d-flex justify-space-between align-center mb-3">
        <v-skeleton-loader
          type="text"
          width="60px"
        />
        <v-skeleton-loader
          type="chip"
          width="80px"
        />
      </div>
      
      <!-- 描述骨架 -->
      <v-skeleton-loader
        type="paragraph"
        class="mb-3"
      />
    </v-card-text>

    <!-- 按钮骨架 -->
    <v-card-actions class="actions-skeleton">
      <v-skeleton-loader
        type="button"
        class="flex-grow-1"
      />
      <v-skeleton-loader
        type="button"
        class="flex-grow-1 ml-2"
      />
    </v-card-actions>
  </v-card>
</template>

<script setup lang="ts">
// 无需额外逻辑，纯展示组件
</script>

<style scoped>
.book-card-skeleton {
  border-radius: 16px !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cover-skeleton {
  border-radius: 16px 16px 0 0 !important;
}

.content-skeleton {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.meta-skeleton {
  margin-top: auto;
}

.actions-skeleton {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  background: rgba(0, 0, 0, 0.02);
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .actions-skeleton {
    border-top-color: rgba(255, 255, 255, 0.12);
    background: rgba(255, 255, 255, 0.02);
  }
}
</style>
