<template>
  <v-avatar
    :size="size"
    :class="avatarClass"
    :style="avatarStyle"
  >
    <v-img
      v-if="displayUrl"
      :src="displayUrl"
      :alt="altText"
      :loading="loading"
      @error="handleImageError"
      @load="handleImageLoad"
      cover
    >
      <template #placeholder>
        <div class="d-flex align-center justify-center fill-height">
          <v-progress-circular
            v-if="isLoading"
            indeterminate
            size="24"
            color="primary"
          />
          <v-icon
            v-else
            :size="iconSize"
            color="grey-lighten-1"
          >
            mdi-account
          </v-icon>
        </div>
      </template>
    </v-img>
    
    <!-- 默认头像 -->
    <v-img
      v-else
      :src="defaultUrl"
      :alt="altText"
      cover
    />
  </v-avatar>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

interface Props {
  /** 头像URL */
  src?: string;
  /** 用户名（用于生成默认头像） */
  username?: string;
  /** 真实姓名（用于生成默认头像） */
  realName?: string;
  /** 头像尺寸 */
  size?: string | number;
  /** 自定义CSS类 */
  class?: string;
  /** 自定义样式 */
  style?: string | object;
  /** 是否显示加载状态 */
  loading?: boolean;
  /** 是否启用点击事件 */
  clickable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  src: '',
  username: '',
  realName: '',
  size: 40,
  class: '',
  style: '',
  loading: false,
  clickable: false,
});

const emit = defineEmits<{
  click: [event: MouseEvent];
  error: [error: Event];
  load: [event: Event];
}>();

// 响应式状态
const isLoading = ref(props.loading);
const hasError = ref(false);

// 计算属性
const displayName = computed(() => {
  return props.realName || props.username || 'User';
});

const altText = computed(() => {
  return `${displayName.value}的头像`;
});

const iconSize = computed(() => {
  const size = typeof props.size === 'number' ? props.size : parseInt(props.size || '40');
  return Math.max(16, Math.floor(size * 0.6));
});

const avatarClass = computed(() => {
  const classes = ['user-avatar'];
  
  if (props.class) {
    classes.push(props.class);
  }
  
  if (props.clickable) {
    classes.push('user-avatar--clickable');
  }
  
  if (isLoading.value) {
    classes.push('user-avatar--loading');
  }
  
  return classes.join(' ');
});

const avatarStyle = computed(() => {
  if (typeof props.style === 'string') {
    return props.style;
  }
  return props.style || {};
});

// 处理头像URL
const processedUrl = computed(() => {
  if (!props.src) return '';

  const src = props.src.trim();
  if (!src) return '';

  // 如果已经是完整URL，直接返回
  if (src.startsWith('http://') || src.startsWith('https://')) {
    return src;
  }

  // 获取基础URL，确保不包含/api后缀
  let baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 如果baseUrl以/api结尾，移除它
  if (baseUrl.endsWith('/api')) {
    baseUrl = baseUrl.slice(0, -4);
  }

  // 如果路径以/开头，直接拼接（后端返回的格式：/api/files/avatars/filename）
  if (src.startsWith('/')) {
    return `${baseUrl}${src}`;
  }

  // 如果只是文件名，添加完整路径前缀
  return `${baseUrl}/api/files/avatars/${src}`;
});

const displayUrl = computed(() => {
  return hasError.value ? '' : processedUrl.value;
});

const defaultUrl = computed(() => {
  const name = encodeURIComponent(displayName.value);
  const size = typeof props.size === 'number' ? props.size : parseInt(props.size || '40');
  const actualSize = Math.max(40, size * 2); // 使用2倍尺寸以获得更清晰的图片
  
  return `https://ui-avatars.com/api/?name=${name}&size=${actualSize}&background=1976d2&color=ffffff&bold=true&format=png`;
});

// 事件处理
const handleImageError = (event: Event) => {
  console.warn('头像加载失败:', processedUrl.value);
  hasError.value = true;
  isLoading.value = false;
  emit('error', event);
};

const handleImageLoad = (event: Event) => {
  hasError.value = false;
  isLoading.value = false;
  emit('load', event);
};

const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event);
  }
};

// 监听props变化
watch(() => props.src, () => {
  hasError.value = false;
  isLoading.value = props.loading;
});

watch(() => props.loading, (newLoading) => {
  isLoading.value = newLoading;
});
</script>

<style scoped>
.user-avatar {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.user-avatar--clickable {
  cursor: pointer;
}

.user-avatar--clickable:hover {
  border-color: rgba(25, 118, 210, 0.3);
  transform: scale(1.05);
}

.user-avatar--loading {
  opacity: 0.7;
}

/* 确保图片完全填充头像容器 */
.user-avatar :deep(.v-img__img) {
  object-fit: cover;
}

/* 占位符样式 */
.user-avatar :deep(.v-img__placeholder) {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>
