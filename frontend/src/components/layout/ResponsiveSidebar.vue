<template>
  <v-navigation-drawer
    v-model="drawerModel"
    :rail="railModel"
    :permanent="!mobile"
    :temporary="mobile"
    :width="drawerWidth"
    :class="sidebarClass"
    location="left"
  >
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <slot name="header">
        <div class="default-header" @click="toggleRail">
          <v-icon :size="railModel ? 24 : 32" color="primary">
            {{ headerIcon }}
          </v-icon>
          <v-fade-transition>
            <span v-show="!railModel" class="header-text gradient-text">
              {{ headerTitle }}
            </span>
          </v-fade-transition>
        </div>
      </slot>
    </div>

    <v-divider />

    <!-- 侧边栏内容 -->
    <div class="sidebar-content">
      <slot name="content">
        <!-- 默认菜单列表 -->
        <v-list nav class="navigation-list">
          <template v-for="item in menuItems" :key="item.title">
            <!-- 分组标题 -->
            <v-list-subheader v-if="item.type === 'subheader' && !railModel">
              {{ item.title }}
            </v-list-subheader>

            <!-- 分隔线 -->
            <v-divider v-else-if="item.type === 'divider'" class="my-2" />

            <!-- 有子菜单的项 -->
            <v-list-group v-else-if="item.children && !railModel" :value="item.title">
              <template #activator="{ props }">
                <v-list-item
                  v-bind="props"
                  :prepend-icon="item.icon"
                  :title="item.title"
                  class="menu-item"
                />
              </template>
              
              <v-list-item
                v-for="child in item.children"
                :key="child.title"
                :to="child.to"
                :prepend-icon="child.icon"
                :title="child.title"
                class="menu-item child-item"
              />
            </v-list-group>

            <!-- 普通菜单项 -->
            <v-list-item
              v-else-if="item.to"
              :to="item.to"
              :prepend-icon="item.icon"
              :title="railModel ? '' : item.title"
              class="menu-item"
              :class="{ 'rail-item': railModel }"
            >
              <!-- 徽章 -->
              <template v-if="item.badge && !railModel" #append>
                <v-badge
                  :content="item.badge"
                  :color="item.badgeColor || 'error'"
                  inline
                />
              </template>

              <!-- Rail模式下的提示 -->
              <v-tooltip
                v-if="railModel"
                activator="parent"
                location="end"
              >
                {{ item.title }}
                <span v-if="item.badge" class="ml-1">({{ item.badge }})</span>
              </v-tooltip>
            </v-list-item>
          </template>
        </v-list>
      </slot>
    </div>

    <!-- 侧边栏底部 -->
    <template #append>
      <div class="sidebar-footer">
        <slot name="footer">
          <!-- 默认切换按钮 -->
          <div class="toggle-section">
            <v-btn
              v-if="!railModel && !mobile"
              variant="text"
              size="small"
              @click="toggleRail"
              class="toggle-btn"
            >
              <v-icon>mdi-chevron-left</v-icon>
              收起
            </v-btn>
            <v-btn
              v-else-if="!mobile"
              variant="text"
              size="small"
              icon
              @click="toggleRail"
              class="toggle-btn"
            >
              <v-icon>mdi-chevron-right</v-icon>
            </v-btn>
          </div>
        </slot>
      </div>
    </template>
  </v-navigation-drawer>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { useDisplay } from 'vuetify';
import type { MenuItem } from '@/types';

interface Props {
  // 抽屉显示状态
  drawer: boolean;
  // Rail模式状态
  rail?: boolean;
  // 菜单项
  menuItems?: MenuItem[];
  // 头部图标
  headerIcon?: string;
  // 头部标题
  headerTitle?: string;
  // 自定义样式类
  sidebarClass?: string;
  // 抽屉宽度
  width?: number;
  // 是否允许切换Rail模式
  allowRailToggle?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  rail: false,
  menuItems: () => [],
  headerIcon: 'mdi-menu',
  headerTitle: '导航菜单',
  sidebarClass: '',
  width: 280,
  allowRailToggle: true,
});

interface Emits {
  (e: 'update:drawer', value: boolean): void;
  (e: 'update:rail', value: boolean): void;
  (e: 'toggle-rail'): void;
}

const emit = defineEmits<Emits>();

const { mobile } = useDisplay();

// 计算属性
const drawerModel = computed({
  get: () => props.drawer,
  set: (value) => emit('update:drawer', value),
});

const railModel = computed({
  get: () => props.rail && !mobile.value,
  set: (value) => emit('update:rail', value),
});

const drawerWidth = computed(() => {
  if (railModel.value) return 64;
  return props.width;
});

// 切换Rail模式
const toggleRail = () => {
  if (!props.allowRailToggle || mobile.value) return;
  
  railModel.value = !railModel.value;
  emit('toggle-rail');
};

// 监听移动端状态变化
watch(mobile, (isMobile) => {
  if (isMobile) {
    // 移动端时关闭Rail模式
    railModel.value = false;
  }
});
</script>

<style scoped>
.sidebar-header {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 64px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.default-header {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 8px;
}

.default-header:hover {
  background: rgba(25, 118, 210, 0.08);
  transform: scale(1.02);
}

.header-text {
  font-size: 18px;
  font-weight: 600;
  white-space: nowrap;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.navigation-list {
  padding: 0 8px;
}

.menu-item {
  border-radius: 12px !important;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background: rgba(25, 118, 210, 0.08) !important;
  transform: translateX(4px);
}

.menu-item.router-link-active {
  background: rgba(25, 118, 210, 0.12) !important;
  color: #1976d2 !important;
}

.child-item {
  margin-left: 16px;
  font-size: 14px;
}

.rail-item {
  justify-content: center !important;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.toggle-section {
  display: flex;
  justify-content: center;
}

.toggle-btn {
  border-radius: 8px !important;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background: rgba(25, 118, 210, 0.08) !important;
}

/* 自定义滚动条 */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(25, 118, 210, 0.3);
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(25, 118, 210, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-header {
    padding: 12px;
    min-height: 56px;
  }
  
  .header-text {
    font-size: 16px;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .sidebar-header {
    border-bottom-color: rgba(255, 255, 255, 0.12);
  }
  
  .sidebar-footer {
    border-top-color: rgba(255, 255, 255, 0.12);
  }
  
  .default-header:hover {
    background: rgba(66, 165, 245, 0.08);
  }
  
  .menu-item:hover {
    background: rgba(66, 165, 245, 0.08) !important;
  }
  
  .menu-item.router-link-active {
    background: rgba(66, 165, 245, 0.12) !important;
    color: #42a5f5 !important;
  }
}
</style>
