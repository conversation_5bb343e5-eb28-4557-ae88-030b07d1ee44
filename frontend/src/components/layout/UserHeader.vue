<template>
  <div class="user-header">
    <!-- 搜索区域 -->
    <div v-if="showSearch" class="search-section">
      <v-text-field
        v-model="searchQuery"
        :placeholder="searchPlaceholder"
        prepend-inner-icon="mdi-magnify"
        variant="outlined"
        density="compact"
        hide-details
        class="search-field"
        :class="{ 'search-field-dark': isDark }"
        @keyup.enter="handleSearch"
        @click:prepend-inner="handleSearch"
      />
    </div>

    <!-- 快捷操作区域 -->
    <div class="actions-section">
      <!-- 自定义操作按钮 -->
      <slot name="actions" />

      <!-- 通知按钮 -->
      <v-btn
        v-if="showNotifications"
        icon
        variant="text"
        class="action-btn"
        @click="handleNotificationClick"
      >
        <v-badge
          v-if="notificationCount > 0"
          :content="notificationCount > 99 ? '99+' : notificationCount.toString()"
          color="error"
        >
          <v-icon>mdi-bell</v-icon>
        </v-badge>
        <v-icon v-else>mdi-bell-outline</v-icon>
      </v-btn>

      <!-- 主题切换按钮 -->
      <v-btn
        v-if="showThemeToggle"
        icon
        variant="text"
        class="action-btn"
        @click="toggleTheme"
      >
        <v-icon>{{ isDark ? 'mdi-weather-sunny' : 'mdi-weather-night' }}</v-icon>
      </v-btn>

      <!-- 全屏按钮 -->
      <v-btn
        v-if="showFullscreen"
        icon
        variant="text"
        class="action-btn"
        @click="toggleFullscreen"
      >
        <v-icon>{{ isFullscreen ? 'mdi-fullscreen-exit' : 'mdi-fullscreen' }}</v-icon>
      </v-btn>

      <!-- 管理员入口 -->
      <v-btn
        v-if="showAdminEntry && userStore.isAdmin"
        icon
        variant="text"
        class="action-btn"
        @click="goToAdmin"
      >
        <v-icon>mdi-cog</v-icon>
        <v-tooltip activator="parent" location="bottom">
          管理后台
        </v-tooltip>
      </v-btn>
    </div>

    <!-- 用户菜单 -->
    <div class="user-menu-section">
      <v-menu offset-y>
        <template #activator="{ props }">
          <v-btn
            v-bind="props"
            variant="text"
            class="user-menu-btn"
          >
            <!-- 用户头像 -->
            <v-avatar :size="avatarSize" class="me-2">
              <v-img
                v-if="userStore.userInfo?.avatar"
                :src="userStore.userInfo.avatar"
                :alt="userStore.userInfo.username"
              />
              <v-icon v-else :size="avatarSize - 8">mdi-account-circle</v-icon>
            </v-avatar>

            <!-- 用户名（桌面端显示） -->
            <span v-if="showUserName" class="user-name">
              {{ displayName }}
            </span>

            <!-- 下拉箭头 -->
            <v-icon size="16">mdi-chevron-down</v-icon>
          </v-btn>
        </template>

        <!-- 用户菜单内容 -->
        <v-list class="user-menu" min-width="220">
          <!-- 用户信息 -->
          <v-list-item class="user-info-item">
            <template #prepend>
              <v-avatar :size="40">
                <v-img
                  v-if="userStore.userInfo?.avatar"
                  :src="userStore.userInfo.avatar"
                  :alt="userStore.userInfo.username"
                />
                <v-icon v-else size="24">mdi-account-circle</v-icon>
              </v-avatar>
            </template>
            <v-list-item-title class="font-weight-medium">
              {{ userStore.userInfo?.username }}
            </v-list-item-title>
            <v-list-item-subtitle>
              {{ getRoleDisplayName(userStore.userInfo?.role || '') }}
            </v-list-item-subtitle>
          </v-list-item>
          
          <v-divider />
          
          <!-- 菜单项 -->
          <v-list-item
            v-for="item in menuItems"
            :key="item.title"
            :prepend-icon="item.icon"
            :title="item.title"
            @click="item.action"
            class="menu-item"
          />
          
          <v-divider />
          
          <!-- 退出登录 -->
          <v-list-item
            prepend-icon="mdi-logout"
            title="退出登录"
            @click="handleLogout"
            class="menu-item logout-item"
          />
        </v-list>
      </v-menu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useTheme } from 'vuetify';
import { useDisplay } from 'vuetify';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { getRoleDisplayName } from '@/utils/permission';

interface MenuItem {
  title: string;
  icon: string;
  action: () => void;
}

interface Props {
  // 是否显示搜索框
  showSearch?: boolean;
  // 搜索框占位符
  searchPlaceholder?: string;
  // 是否显示通知按钮
  showNotifications?: boolean;
  // 通知数量
  notificationCount?: number;
  // 是否显示主题切换按钮
  showThemeToggle?: boolean;
  // 是否显示全屏按钮
  showFullscreen?: boolean;
  // 是否显示管理员入口
  showAdminEntry?: boolean;
  // 是否显示用户名
  showUserName?: boolean;
  // 头像大小
  avatarSize?: number;
  // 自定义菜单项
  customMenuItems?: MenuItem[];
}

const props = withDefaults(defineProps<Props>(), {
  showSearch: true,
  searchPlaceholder: '搜索...',
  showNotifications: true,
  notificationCount: 0,
  showThemeToggle: true,
  showFullscreen: false,
  showAdminEntry: true,
  showUserName: true,
  avatarSize: 32,
  customMenuItems: () => [],
});

interface Emits {
  (e: 'search', query: string): void;
  (e: 'notification-click'): void;
}

const emit = defineEmits<Emits>();

const router = useRouter();
const userStore = useUserStore();
const theme = useTheme();
const { mobile } = useDisplay();

// 响应式状态
const searchQuery = ref('');
const isFullscreen = ref(false);

// 计算属性
const isDark = computed(() => theme.global.current.value.dark);
const displayName = computed(() => 
  userStore.userInfo?.realName || userStore.userInfo?.username || '用户'
);

// 菜单项
const menuItems = computed((): MenuItem[] => {
  const defaultItems: MenuItem[] = [
    {
      title: '个人信息',
      icon: 'mdi-account',
      action: () => router.push('/profile'),
    },
    {
      title: '我的借阅',
      icon: 'mdi-book-clock',
      action: () => router.push('/my-borrows'),
    },
    {
      title: '我的收藏',
      icon: 'mdi-heart',
      action: () => router.push('/my-favorites'),
    },
  ];

  // 添加管理员菜单项
  if (userStore.isAdmin) {
    defaultItems.push({
      title: '管理后台',
      icon: 'mdi-cog',
      action: () => router.push('/admin'),
    });
  }

  // 合并自定义菜单项
  return [...defaultItems, ...props.customMenuItems];
});

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    emit('search', searchQuery.value.trim());
  }
};

// 处理通知点击
const handleNotificationClick = () => {
  emit('notification-click');
};

// 切换主题
const toggleTheme = () => {
  theme.global.name.value = isDark.value ? 'light' : 'dark';
};

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen();
    isFullscreen.value = true;
  } else {
    document.exitFullscreen();
    isFullscreen.value = false;
  }
};

// 前往管理后台
const goToAdmin = () => {
  router.push('/admin');
};

// 处理退出登录
const handleLogout = () => {
  userStore.logout();
  router.push('/login');
};

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 生命周期
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange);
});

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
});
</script>

<style scoped>
.user-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 0 16px;
}

.search-section {
  flex: 1;
  max-width: 400px;
}

.search-field {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.search-field-dark {
  background: rgba(0, 0, 0, 0.1);
}

:deep(.search-field .v-field) {
  color: white;
}

:deep(.search-field .v-field__input) {
  color: white;
}

:deep(.search-field .v-field__input::placeholder) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.search-field-dark .v-field__input::placeholder) {
  color: rgba(0, 0, 0, 0.6);
}

.actions-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  color: inherit !important;
}

.user-menu-section {
  display: flex;
  align-items: center;
}

.user-menu-btn {
  color: inherit !important;
  text-transform: none !important;
}

.user-name {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.user-menu {
  border-radius: 12px !important;
}

.user-info-item {
  padding: 16px !important;
  background: rgba(25, 118, 210, 0.05);
}

.menu-item {
  border-radius: 8px !important;
  margin: 4px 8px !important;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background: rgba(25, 118, 210, 0.08) !important;
}

.logout-item {
  color: #f44336 !important;
}

.logout-item:hover {
  background: rgba(244, 67, 54, 0.08) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-header {
    gap: 8px;
    padding: 0 8px;
  }
  
  .search-section {
    display: none;
  }
  
  .user-name {
    display: none;
  }
  
  .actions-section {
    gap: 4px;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .user-info-item {
    background: rgba(66, 165, 245, 0.05);
  }
  
  .menu-item:hover {
    background: rgba(66, 165, 245, 0.08) !important;
  }
}
</style>
