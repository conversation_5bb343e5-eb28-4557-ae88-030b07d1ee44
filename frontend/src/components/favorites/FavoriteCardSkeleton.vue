<template>
  <v-card class="favorite-card-skeleton" elevation="2">
    <!-- 选择框骨架 -->
    <div class="selection-skeleton">
      <v-skeleton-loader
        type="avatar"
        width="24"
        height="24"
      />
    </div>

    <!-- 封面骨架 -->
    <v-skeleton-loader
      type="image"
      class="cover-skeleton"
      height="240"
    />

    <!-- 内容骨架 -->
    <v-card-text class="content-skeleton">
      <!-- 标题骨架 -->
      <v-skeleton-loader
        type="heading"
        class="mb-2"
      />
      
      <!-- 作者骨架 -->
      <div class="info-row-skeleton mb-1">
        <v-skeleton-loader
          type="avatar"
          width="14"
          height="14"
          class="me-1"
        />
        <v-skeleton-loader
          type="text"
          width="60%"
        />
      </div>
      
      <!-- 分类骨架 -->
      <div class="info-row-skeleton mb-2">
        <v-skeleton-loader
          type="avatar"
          width="14"
          height="14"
          class="me-1"
        />
        <v-skeleton-loader
          type="text"
          width="50%"
        />
      </div>
      
      <!-- 收藏时间骨架 -->
      <div class="info-row-skeleton mb-3">
        <v-skeleton-loader
          type="avatar"
          width="12"
          height="12"
          class="me-1"
        />
        <v-skeleton-loader
          type="text"
          width="70%"
        />
      </div>
      
      <!-- 库存信息骨架 -->
      <div class="stock-skeleton d-flex justify-space-between align-center mb-3">
        <v-skeleton-loader
          type="text"
          width="60px"
        />
        <v-skeleton-loader
          type="chip"
          width="80px"
        />
      </div>
    </v-card-text>

    <!-- 操作按钮骨架 -->
    <v-card-actions class="actions-skeleton pa-4">
      <v-skeleton-loader
        type="button"
        class="flex-grow-1"
      />
      <div class="ml-2 flex-grow-1">
        <v-skeleton-loader
          type="button"
        />
      </div>
    </v-card-actions>
  </v-card>
</template>

<script setup lang="ts">
// 骨架组件，无需逻辑
</script>

<style scoped>
.favorite-card-skeleton {
  border-radius: 16px !important;
  height: 100%;
  min-height: 520px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.selection-skeleton {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: 4px;
  backdrop-filter: blur(4px);
}

.cover-skeleton {
  border-radius: 16px 16px 0 0 !important;
  flex-shrink: 0;
}

.content-skeleton {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.info-row-skeleton {
  display: flex;
  align-items: center;
}

.stock-skeleton {
  margin-top: auto;
}

.actions-skeleton {
  flex-shrink: 0;
  padding-top: 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .favorite-card-skeleton {
    min-height: 480px;
  }
  
  .actions-skeleton {
    flex-direction: column;
    gap: 8px;
  }
  
  .actions-skeleton .v-skeleton-loader {
    width: 100% !important;
  }
  
  .actions-skeleton .ml-2 {
    margin-left: 0 !important;
    width: 100%;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .selection-skeleton {
    background: rgba(33, 33, 33, 0.9);
  }
}

/* 动画效果 */
.favorite-card-skeleton {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}
</style>
