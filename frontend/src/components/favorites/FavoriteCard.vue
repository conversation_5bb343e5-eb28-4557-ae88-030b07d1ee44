<template>
  <v-card
    class="favorite-card"
    :class="{ 'favorite-card-selected': selected }"
    elevation="2"
    @click="handleCardClick"
  >
    <!-- 选择框 -->
    <div class="selection-overlay">
      <v-checkbox
        :model-value="selected"
        @update:model-value="handleSelect"
        @click.stop
        hide-details
        color="primary"
        class="selection-checkbox"
      />
    </div>

    <!-- 图书封面 -->
    <div class="book-cover-container">
      <v-img
        :src="favorite.bookCoverUrl || defaultCoverUrl"
        :alt="favorite.bookTitle"
        class="book-cover"
        aspect-ratio="3/5"
        :cover="false"
        contain
      >
        <template #placeholder>
          <div class="cover-placeholder">
            <v-icon size="48" color="grey-lighten-2">mdi-book</v-icon>
          </div>
        </template>
        
        <!-- 状态标签 -->
        <div class="status-overlay">
          <v-chip
            v-if="!isAvailable"
            color="error"
            size="small"
            variant="elevated"
            class="status-chip"
          >
            不可借
          </v-chip>
          <v-chip
            v-else-if="favorite.bookAvailableQuantity <= 3"
            color="warning"
            size="small"
            variant="elevated"
            class="status-chip"
          >
            仅剩{{ favorite.bookAvailableQuantity }}本
          </v-chip>
        </div>
      </v-img>
    </div>

    <!-- 图书信息 -->
    <v-card-text class="book-info pa-4">
      <!-- 标题 -->
      <h3 class="book-title text-h6 font-weight-bold mb-2">
        {{ favorite.bookTitle }}
      </h3>
      
      <!-- 作者 -->
      <p class="book-author text-body-2 text-medium-emphasis mb-1">
        <v-icon size="14" class="me-1">mdi-account</v-icon>
        {{ favorite.bookAuthor || '未知作者' }}
      </p>
      
      <!-- 分类 -->
      <p class="book-category text-body-2 text-medium-emphasis mb-2">
        <v-icon size="14" class="me-1">mdi-tag</v-icon>
        {{ favorite.bookCategory || '未分类' }}
      </p>
      
      <!-- 收藏时间 -->
      <p class="favorite-time text-caption text-medium-emphasis mb-3">
        <v-icon size="12" class="me-1">mdi-heart</v-icon>
        收藏于 {{ formatDate(favorite.favoriteTime) }}
      </p>
      
      <!-- 库存信息 -->
      <div class="stock-info mb-3">
        <div class="d-flex align-center justify-space-between">
          <span class="text-caption">库存状态</span>
          <v-chip
            :color="stockColor"
            size="small"
            variant="tonal"
          >
            {{ stockText }}
          </v-chip>
        </div>
      </div>
    </v-card-text>

    <!-- 操作按钮 -->
    <v-card-actions class="book-actions pa-4">
      <v-btn
        variant="outlined"
        size="small"
        prepend-icon="mdi-eye"
        @click.stop="handleViewDetail"
        class="flex-grow-1"
      >
        查看详情
      </v-btn>
      
      <v-btn
        color="error"
        variant="outlined"
        size="small"
        prepend-icon="mdi-heart-remove"
        @click.stop="handleRemove"
        class="flex-grow-1 ml-2"
      >
        取消收藏
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { formatDate } from '@/utils/date';
import type { UserFavorite } from '@/types';

interface Props {
  favorite: UserFavorite;
  selected?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
});

interface Emits {
  (e: 'select', favoriteId: number, selected: boolean): void;
  (e: 'view-detail', favorite: UserFavorite): void;
  (e: 'remove', favorite: UserFavorite): void;
}

const emit = defineEmits<Emits>();

// 计算属性
const isAvailable = computed(() => {
  return props.favorite.bookStatus === 1 && props.favorite.bookAvailableQuantity > 0;
});

const stockColor = computed(() => {
  const quantity = props.favorite.bookAvailableQuantity;
  
  if (quantity === 0) return 'error';
  if (quantity <= 3) return 'warning';
  if (quantity <= 10) return 'info';
  return 'success';
});

const stockText = computed(() => {
  const quantity = props.favorite.bookAvailableQuantity;
  
  if (quantity === 0) return '无库存';
  if (quantity <= 3) return `仅剩${quantity}本`;
  return `库存充足`;
});

const defaultCoverUrl = computed(() => {
  return `https://via.placeholder.com/300x500/1976d2/ffffff?text=${encodeURIComponent(props.favorite.bookTitle)}`;
});

// 事件处理
const handleCardClick = () => {
  handleViewDetail();
};

const handleSelect = (selected: boolean) => {
  emit('select', props.favorite.id, selected);
};

const handleViewDetail = () => {
  emit('view-detail', props.favorite);
};

const handleRemove = () => {
  emit('remove', props.favorite);
};
</script>

<style scoped>
.favorite-card {
  border-radius: 16px !important;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  min-height: 520px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.favorite-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(25, 118, 210, 0.2) !important;
}

.favorite-card-selected {
  border: 2px solid #1976d2;
  box-shadow: 0 4px 20px rgba(25, 118, 210, 0.3) !important;
}

.selection-overlay {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  backdrop-filter: blur(4px);
}

.selection-checkbox {
  margin: 0;
}

.book-cover-container {
  position: relative;
  flex-shrink: 0;
}

.book-cover {
  border-radius: 16px 16px 0 0 !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.cover-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.status-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1;
}

.status-chip {
  backdrop-filter: blur(4px);
}

.book-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.book-title {
  line-height: 1.3;
  color: #1976d2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.book-author,
.book-category {
  display: flex;
  align-items: center;
}

.favorite-time {
  display: flex;
  align-items: center;
  font-style: italic;
}

.stock-info {
  margin-top: auto;
}

.book-actions {
  flex-shrink: 0;
  padding-top: 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .favorite-card {
    min-height: 480px;
  }
  
  .book-title {
    font-size: 1.1rem !important;
  }
  
  .book-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .book-actions .v-btn {
    width: 100%;
    margin: 0 !important;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .cover-placeholder {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
  }
  
  .selection-overlay {
    background: rgba(33, 33, 33, 0.9);
  }
}
</style>
