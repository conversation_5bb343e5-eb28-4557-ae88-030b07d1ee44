<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    max-width="600"
  >
    <v-card class="borrow-detail-dialog">
      <!-- 对话框头部 -->
      <v-card-title class="dialog-header">
        <v-icon class="me-2" color="info">mdi-book-clock</v-icon>
        借阅详情
      </v-card-title>

      <v-divider />

      <!-- 对话框内容 -->
      <v-card-text v-if="record" class="dialog-content">
        <!-- 图书信息 -->
        <div class="book-info-section mb-4">
          <h3 class="text-h6 font-weight-bold mb-3">图书信息</h3>
          <div class="d-flex">
            <!-- 图书封面 -->
            <BookCover
              :src="getBookCoverUrl()"
              :alt="getBookTitle()"
              :title="getBookTitle()"
              width="100px"
              aspect-ratio="3/5"
              :icon-size="32"
              show-loading-text
            />

            <!-- 图书详情 -->
            <div class="book-details ml-4 flex-grow-1">
              <h4 class="text-h6 font-weight-bold mb-2">{{ getBookTitle() }}</h4>
              <p class="text-body-1 mb-1">
                <strong>作者：</strong>{{ getBookAuthor() }}
              </p>
              <p v-if="record.book?.publisher" class="text-body-1 mb-1">
                <strong>出版社：</strong>{{ record.book?.publisher }}
              </p>
              <p v-if="getBookIsbn()" class="text-body-1 mb-1">
                <strong>ISBN：</strong>{{ getBookIsbn() }}
              </p>
              <div class="d-flex align-center mt-2">
                <v-chip
                  :color="statusColor"
                  variant="tonal"
                  size="small"
                >
                  {{ statusText }}
                </v-chip>
                <v-chip
                  v-if="isOverdue"
                  color="error"
                  variant="tonal"
                  size="small"
                  class="ml-2"
                >
                  逾期
                </v-chip>
              </div>
            </div>
          </div>
        </div>

        <!-- 借阅信息 -->
        <div class="borrow-info-section">
          <h3 class="text-h6 font-weight-bold mb-3">借阅信息</h3>
          <v-table class="detail-table">
            <tbody>
              <tr>
                <td class="detail-label">借阅编号</td>
                <td class="detail-value">{{ record.id }}</td>
              </tr>
              <tr>
                <td class="detail-label">借阅状态</td>
                <td class="detail-value">
                  <v-chip
                    :color="statusColor"
                    variant="tonal"
                    size="small"
                  >
                    {{ statusText }}
                  </v-chip>
                </td>
              </tr>
              <tr>
                <td class="detail-label">借阅日期</td>
                <td class="detail-value">{{ formatBorrowTime(getBorrowDate(record)) }}</td>
              </tr>
              <tr>
                <td class="detail-label">应还日期</td>
                <td class="detail-value" :class="{ 'text-error': isOverdue }">
                  {{ formatBorrowTime(getDueDate(record)) }}
                  <v-icon v-if="isOverdue" size="16" color="error" class="ml-1">
                    mdi-alert-circle
                  </v-icon>
                </td>
              </tr>
              <tr v-if="getReturnDate(record)">
                <td class="detail-label">归还日期</td>
                <td class="detail-value">{{ formatBorrowTime(getReturnDate(record)) }}</td>
              </tr>
              <tr v-if="record.renewCount > 0">
                <td class="detail-label">续借次数</td>
                <td class="detail-value">{{ record.renewCount }}</td>
              </tr>
              <tr v-if="record.remarks">
                <td class="detail-label">借阅备注</td>
                <td class="detail-value">{{ record.remarks }}</td>
              </tr>
              <tr v-if="record.fine && record.fine > 0">
                <td class="detail-label">罚金</td>
                <td class="detail-value text-error">
                  ¥{{ record.fine.toFixed(2) }}
                </td>
              </tr>
            </tbody>
          </v-table>
        </div>

        <!-- 时间线（如果有续借记录） -->
        <div v-if="record.renewCount > 0" class="timeline-section mt-4">
          <h3 class="text-h6 font-weight-bold mb-3">借阅时间线</h3>
          <v-timeline density="compact" class="custom-timeline">
            <v-timeline-item
              dot-color="primary"
              size="small"
            >
              <template #opposite>
                <span class="text-body-2">{{ formatBorrowTime(getBorrowDate(record)) }}</span>
              </template>
              <div>
                <strong>图书借阅</strong>
                <div class="text-body-2 text-medium-emphasis">开始借阅此图书</div>
              </div>
            </v-timeline-item>

            <v-timeline-item
              v-for="n in record.renewCount"
              :key="n"
              dot-color="warning"
              size="small"
            >
              <template #opposite>
                <span class="text-body-2">续借记录 {{ n }}</span>
              </template>
              <div>
                <strong>图书续借</strong>
                <div class="text-body-2 text-medium-emphasis">延长借阅期限</div>
              </div>
            </v-timeline-item>

            <v-timeline-item
              v-if="record.returnTime || record.returnDate"
              dot-color="success"
              size="small"
            >
              <template #opposite>
                <span class="text-body-2">{{ formatBorrowTime(getReturnDate(record)) }}</span>
              </template>
              <div>
                <strong>图书归还</strong>
                <div class="text-body-2 text-medium-emphasis">完成借阅流程</div>
              </div>
            </v-timeline-item>
          </v-timeline>
        </div>
      </v-card-text>

      <!-- 对话框操作 -->
      <v-card-actions class="dialog-actions">
        <v-spacer />
        <v-btn
          variant="outlined"
          @click="$emit('update:modelValue', false)"
        >
          关闭
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { BorrowRecord } from '@/types';
import { formatDateTime, formatBorrowTime, getBorrowDate, getDueDate, getReturnDate, isDateOverdue } from '@/utils/date';
import BookCover from '@/components/common/BookCover.vue';

interface Props {
  modelValue: boolean;
  record?: BorrowRecord | null;
}

const props = withDefaults(defineProps<Props>(), {
  record: null,
});

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
}

const emit = defineEmits<Emits>();

// 计算属性
const isOverdue = computed(() => {
  if (!props.record || props.record.status === 'RETURNED') return false;
  const dueDateStr = getDueDate(props.record);
  return isDateOverdue(dueDateStr);
});

const statusColor = computed(() => {
  if (!props.record) return 'grey';
  const colors: Record<string, string> = {
    BORROWED: 'success',
    RETURNED: 'info',
    OVERDUE: 'error',
    RENEWED: 'warning',
  };
  return colors[props.record.status] || 'grey';
});

const statusText = computed(() => {
  if (!props.record) return '未知';
  const texts: Record<string, string> = {
    BORROWED: '借阅中',
    RETURNED: '已归还',
    OVERDUE: '逾期',
    RENEWED: '续借',
  };
  return texts[props.record.status] || '未知';
});

// 获取图书信息的方法（兼容不同数据结构）
const getBookTitle = () => {
  return props.record?.bookTitle || props.record?.book?.title || '未知图书';
};

const getBookAuthor = () => {
  return props.record?.bookAuthor || props.record?.book?.author || '未知作者';
};

const getBookIsbn = () => {
  return props.record?.bookIsbn || props.record?.book?.isbn || '';
};

const getBookCoverUrl = () => {
  // 优先使用后端返回的图书封面URL
  const coverUrl = props.record?.bookCoverUrl || props.record?.book?.coverUrl;
  if (coverUrl && coverUrl.trim()) {
    // 检查是否是豆瓣图片链接，如果是则可能需要处理跨域问题
    if (coverUrl.includes('doubanio.com')) {
      // 对于豆瓣图片，我们仍然尝试加载，但会有错误处理
      return coverUrl;
    }
    return coverUrl;
  }

  // 生成默认封面图片
  const title = getBookTitle();
  return `https://via.placeholder.com/120x200/1976d2/ffffff?text=${encodeURIComponent(title)}`;
};

const defaultCoverUrl = computed(() => {
  return getBookCoverUrl();
});

</script>

<style scoped>
.borrow-detail-dialog {
  border-radius: 16px !important;
}

.dialog-header {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 100%);
  font-weight: 600;
}

.dialog-content {
  padding: 24px !important;
  max-height: 70vh;
  overflow-y: auto;
}

.book-info-section {
  background: rgba(25, 118, 210, 0.02);
  border-radius: 12px;
  padding: 16px;
}



.book-details {
  min-width: 0;
}

.detail-table {
  border-radius: 12px !important;
  overflow: hidden;
}

.detail-label {
  font-weight: 600;
  color: rgba(0, 0, 0, 0.7);
  width: 120px;
  padding: 12px 16px !important;
  background: rgba(25, 118, 210, 0.05);
}

.detail-value {
  padding: 12px 16px !important;
}

.custom-timeline {
  padding-left: 16px;
}

.dialog-actions {
  padding: 16px 24px !important;
  background: rgba(0, 0, 0, 0.02);
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .dialog-header {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  }
  
  .book-info-section {
    background: rgba(66, 165, 245, 0.05);
  }
  
  .detail-label {
    color: rgba(255, 255, 255, 0.7);
    background: rgba(66, 165, 245, 0.1);
  }
  
  .dialog-actions {
    background: rgba(255, 255, 255, 0.02);
  }
}
</style>
