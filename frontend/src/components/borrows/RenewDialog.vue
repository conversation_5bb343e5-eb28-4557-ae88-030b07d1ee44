<template>
  <v-dialog
    v-model="dialogVisible"
    max-width="600"
    persistent
    class="renew-dialog"
  >
    <v-card class="renew-dialog-card" elevation="8">
      <!-- 对话框标题 -->
      <v-card-title class="dialog-header pa-6">
        <v-icon class="me-3" color="info">mdi-book-refresh</v-icon>
        <span class="text-h5 font-weight-bold">续借确认</span>
        <v-spacer />
        <v-btn
          icon="mdi-close"
          variant="text"
          size="small"
          @click="handleCancel"
        />
      </v-card-title>

      <v-divider />

      <v-card-text class="dialog-content pa-6">
        <!-- 图书信息 -->
        <div v-if="record" class="book-info-section mb-6">
          <h3 class="text-h6 font-weight-bold mb-3">图书信息</h3>
          <div class="d-flex">
            <!-- 图书封面 -->
            <BookCover
              :src="getBookCoverUrl()"
              :alt="getBookTitle()"
              :title="getBookTitle()"
              width="100px"
              aspect-ratio="3/5"
              :icon-size="32"
              show-loading-text
            />

            <!-- 图书详情 -->
            <div class="book-details ml-4 flex-grow-1">
              <h4 class="text-h6 font-weight-bold mb-2">{{ getBookTitle() }}</h4>
              <p class="text-body-2 text-medium-emphasis mb-1">
                作者：{{ getBookAuthor() }}
              </p>
              <p v-if="getBookIsbn()" class="text-body-2 text-medium-emphasis mb-1">
                ISBN：{{ getBookIsbn() }}
              </p>
              <p v-if="record.book?.publisher" class="text-body-2 text-medium-emphasis">
                出版社：{{ record.book?.publisher }}
              </p>
            </div>
          </div>
        </div>

        <!-- 续借信息 -->
        <div class="renew-info-section mb-6">
          <h3 class="text-h6 font-weight-bold mb-3">续借信息</h3>
          
          <v-row>
            <v-col cols="12" md="6">
              <div class="info-item">
                <span class="info-label">当前到期日期：</span>
                <span class="info-value">{{ formatDate(record?.dueDate || '') }}</span>
              </div>
            </v-col>
            <v-col cols="12" md="6">
              <div class="info-item">
                <span class="info-label">续借后到期日期：</span>
                <span class="info-value text-success">{{ formatDate(newDueDate) }}</span>
              </div>
            </v-col>
            <v-col cols="12" md="6">
              <div class="info-item">
                <span class="info-label">已续借次数：</span>
                <span class="info-value">{{ record?.renewCount || 0 }}</span>
              </div>
            </v-col>
            <v-col cols="12" md="6">
              <div class="info-item">
                <span class="info-label">剩余续借次数：</span>
                <span class="info-value text-warning">{{ remainingRenewCount }}</span>
              </div>
            </v-col>
          </v-row>
        </div>

        <!-- 续借条件检查 -->
        <div class="renew-conditions mb-6">
          <h3 class="text-h6 font-weight-bold mb-3">续借条件检查</h3>
          
          <div class="condition-list">
            <div class="condition-item" :class="{ 'condition-pass': canRenewByTime, 'condition-fail': !canRenewByTime }">
              <v-icon :color="canRenewByTime ? 'success' : 'error'" class="me-2">
                {{ canRenewByTime ? 'mdi-check-circle' : 'mdi-close-circle' }}
              </v-icon>
              <span>距离到期日期在5天内</span>
            </div>
            
            <div class="condition-item" :class="{ 'condition-pass': canRenewByCount, 'condition-fail': !canRenewByCount }">
              <v-icon :color="canRenewByCount ? 'success' : 'error'" class="me-2">
                {{ canRenewByCount ? 'mdi-check-circle' : 'mdi-close-circle' }}
              </v-icon>
              <span>续借次数未超过限制（最多2次）</span>
            </div>
            
            <div class="condition-item" :class="{ 'condition-pass': canRenewByDuration, 'condition-fail': !canRenewByDuration }">
              <v-icon :color="canRenewByDuration ? 'success' : 'error'" class="me-2">
                {{ canRenewByDuration ? 'mdi-check-circle' : 'mdi-close-circle' }}
              </v-icon>
              <span>总借阅时间未超过3个月</span>
            </div>
            
            <div class="condition-item" :class="{ 'condition-pass': !isOverdue, 'condition-fail': isOverdue }">
              <v-icon :color="!isOverdue ? 'success' : 'error'" class="me-2">
                {{ !isOverdue ? 'mdi-check-circle' : 'mdi-close-circle' }}
              </v-icon>
              <span>图书未逾期</span>
            </div>
          </div>
        </div>

        <!-- 续借说明 -->
        <v-alert
          v-if="!canRenew"
          type="warning"
          variant="tonal"
          class="mb-4"
        >
          <div class="alert-title font-weight-bold mb-2">无法续借</div>
          <div>{{ renewFailReason }}</div>
        </v-alert>

        <v-alert
          v-else
          type="info"
          variant="tonal"
          class="mb-4"
        >
          <div class="alert-title font-weight-bold mb-2">续借说明</div>
          <ul class="renew-rules">
            <li>续借将延长1个月的归还期限</li>
            <li>每本图书最多可续借2次</li>
            <li>续借后请按时归还，避免产生逾期费用</li>
          </ul>
        </v-alert>

        <!-- 备注输入 -->
        <v-textarea
          v-model="remarks"
          label="续借备注（可选）"
          placeholder="请输入续借原因或备注信息..."
          rows="3"
          variant="outlined"
          :disabled="!canRenew"
        />
      </v-card-text>

      <!-- 操作按钮 -->
      <v-card-actions class="dialog-actions pa-6">
        <v-spacer />
        <v-btn
          variant="outlined"
          @click="handleCancel"
        >
          取消
        </v-btn>
        <v-btn
          color="info"
          variant="elevated"
          :disabled="!canRenew"
          :loading="isLoading"
          @click="handleConfirm"
        >
          确认续借
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { BorrowRecord } from '@/types';
import { formatDate, isDateOverdue } from '@/utils/date';
import BookCover from '@/components/common/BookCover.vue';

interface Props {
  modelValue: boolean;
  record: BorrowRecord | null;
  isLoading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
});

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm', data: { borrowId: number; remarks?: string }): void;
}

const emit = defineEmits<Emits>();

// 响应式状态
const remarks = ref('');

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

// 获取图书信息
const getBookTitle = () => {
  return props.record?.bookTitle || props.record?.book?.title || '未知图书';
};

const getBookAuthor = () => {
  return props.record?.bookAuthor || props.record?.book?.author || '未知作者';
};

const getBookIsbn = () => {
  return props.record?.bookIsbn || props.record?.book?.isbn || '';
};

const getBookCoverUrl = () => {
  const coverUrl = props.record?.bookCoverUrl || props.record?.book?.coverUrl;
  if (coverUrl && coverUrl.trim()) {
    return coverUrl;
  }
  const title = getBookTitle();
  return `https://via.placeholder.com/120x200/1976d2/ffffff?text=${encodeURIComponent(title)}`;
};

// 续借条件检查
const isOverdue = computed(() => {
  if (!props.record) return false;
  const dueDateStr = props.record.dueTime || props.record.dueDate || '';
  return isDateOverdue(dueDateStr);
});

const canRenewByTime = computed(() => {
  const dueDateStr = props.record?.dueTime || props.record?.dueDate || '';
  if (!dueDateStr) return false;

  // 处理后端时间格式
  let dueDate: Date;
  if (dueDateStr.includes('T')) {
    dueDate = new Date(dueDateStr);
  } else if (dueDateStr.includes(' ')) {
    dueDate = new Date(dueDateStr.replace(' ', 'T'));
  } else {
    dueDate = new Date(dueDateStr);
  }

  const today = new Date();

  // 只比较日期部分
  const dueDateOnly = new Date(dueDate.getFullYear(), dueDate.getMonth(), dueDate.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  const diffDays = Math.ceil((dueDateOnly.getTime() - todayOnly.getTime()) / (1000 * 60 * 60 * 24));

  return diffDays <= 5 && diffDays >= 0; // 距离到期日5天内且未逾期可以续借
});

const canRenewByCount = computed(() => {
  if (!props.record) return false;
  return (props.record.renewCount || 0) < 2; // 最多续借2次
});

const canRenewByDuration = computed(() => {
  if (!props.record) return false;

  const borrowDateStr = props.record.borrowTime || props.record.borrowDate || '';
  if (!borrowDateStr) return false;

  // 处理后端时间格式
  let borrowDate: Date;
  if (borrowDateStr.includes('T')) {
    borrowDate = new Date(borrowDateStr);
  } else if (borrowDateStr.includes(' ')) {
    borrowDate = new Date(borrowDateStr.replace(' ', 'T'));
  } else {
    borrowDate = new Date(borrowDateStr);
  }

  const today = new Date();
  const diffMonths = (today.getFullYear() - borrowDate.getFullYear()) * 12 +
                    (today.getMonth() - borrowDate.getMonth());
  return diffMonths < 3; // 总借阅时间不超过3个月
});

const canRenew = computed(() => {
  return canRenewByTime.value && 
         canRenewByCount.value && 
         canRenewByDuration.value && 
         !isOverdue.value;
});

const remainingRenewCount = computed(() => {
  return Math.max(0, 2 - (props.record?.renewCount || 0));
});

const newDueDate = computed(() => {
  const dueDateStr = props.record?.dueTime || props.record?.dueDate || '';
  if (!dueDateStr) return '';

  // 处理后端时间格式
  let currentDue: Date;
  if (dueDateStr.includes('T')) {
    currentDue = new Date(dueDateStr);
  } else if (dueDateStr.includes(' ')) {
    currentDue = new Date(dueDateStr.replace(' ', 'T'));
  } else {
    currentDue = new Date(dueDateStr);
  }

  const newDue = new Date(currentDue);
  newDue.setMonth(newDue.getMonth() + 1); // 延长1个月
  return newDue.toISOString().split('T')[0];
});

const renewFailReason = computed(() => {
  if (!canRenewByTime.value) {
    return '只能在距离到期日5天内申请续借';
  }
  if (!canRenewByCount.value) {
    return '已达到最大续借次数限制（2次）';
  }
  if (!canRenewByDuration.value) {
    return '总借阅时间已超过3个月限制';
  }
  if (isOverdue.value) {
    return '图书已逾期，无法续借';
  }
  return '不满足续借条件';
});

// 事件处理
const handleConfirm = () => {
  if (!props.record || !canRenew.value) return;
  
  emit('confirm', {
    borrowId: props.record.id,
    remarks: remarks.value || undefined,
  });
};

const handleCancel = () => {
  dialogVisible.value = false;
};

// 监听对话框打开，重置表单
watch(dialogVisible, (newValue) => {
  if (newValue) {
    remarks.value = '';
  }
});
</script>

<style scoped>
.renew-dialog-card {
  border-radius: 16px !important;
}

.dialog-header {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.05) 0%, rgba(3, 169, 244, 0.05) 100%);
  font-weight: 600;
}

.book-info-section {
  background: rgba(33, 150, 243, 0.02);
  border-radius: 12px;
  padding: 16px;
}



.book-details {
  min-width: 0;
}

.renew-info-section {
  background: rgba(76, 175, 80, 0.02);
  border-radius: 12px;
  padding: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.info-label {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
}

.info-value {
  font-weight: 600;
}

.renew-conditions {
  background: rgba(255, 193, 7, 0.02);
  border-radius: 12px;
  padding: 16px;
}

.condition-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.condition-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.condition-pass {
  background: rgba(76, 175, 80, 0.1);
  color: #2e7d32;
}

.condition-fail {
  background: rgba(244, 67, 54, 0.1);
  color: #c62828;
}

.alert-title {
  font-size: 1rem;
}

.renew-rules {
  margin: 0;
  padding-left: 20px;
}

.renew-rules li {
  margin-bottom: 4px;
}

.dialog-actions {
  background: rgba(0, 0, 0, 0.02);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-content {
    padding: 16px !important;
  }
  
  .book-cover {
    width: 80px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .dialog-header {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(3, 169, 244, 0.1) 100%);
  }
  
  .book-info-section {
    background: rgba(33, 150, 243, 0.05);
  }
  
  .renew-info-section {
    background: rgba(76, 175, 80, 0.05);
  }
  
  .renew-conditions {
    background: rgba(255, 193, 7, 0.05);
  }

  .info-label {
    color: rgba(255, 255, 255, 0.7);
  }

  .dialog-actions {
    background: rgba(255, 255, 255, 0.02);
  }
}
</style>
