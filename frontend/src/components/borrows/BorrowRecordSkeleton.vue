<template>
  <v-card class="borrow-record-skeleton" elevation="2">
    <!-- 状态标签骨架 -->
    <div class="status-skeleton">
      <v-skeleton-loader
        type="chip"
        width="80px"
      />
    </div>

    <!-- 图书信息骨架 -->
    <div class="book-section-skeleton">
      <div class="d-flex">
        <!-- 封面骨架 -->
        <v-skeleton-loader
          type="image"
          width="80px"
          height="107px"
          class="cover-skeleton"
        />

        <!-- 详情骨架 -->
        <div class="book-details-skeleton ml-3 flex-grow-1">
          <v-skeleton-loader
            type="heading"
            class="mb-2"
          />
          <v-skeleton-loader
            type="text"
            width="60%"
            class="mb-2"
          />
          <v-skeleton-loader
            type="text"
            width="70%"
          />
        </div>
      </div>
    </div>

    <v-divider />

    <!-- 借阅信息骨架 -->
    <div class="borrow-info-skeleton pa-4">
      <div class="info-row-skeleton mb-2">
        <v-skeleton-loader
          type="text"
          width="80%"
        />
      </div>
      <div class="info-row-skeleton mb-2">
        <v-skeleton-loader
          type="text"
          width="75%"
        />
      </div>
      <div class="info-row-skeleton">
        <v-skeleton-loader
          type="text"
          width="70%"
        />
      </div>
    </div>

    <!-- 操作按钮骨架 -->
    <div class="actions-skeleton pa-4">
      <v-skeleton-loader
        type="button"
        class="flex-grow-1"
      />
      <v-skeleton-loader
        type="button"
        class="flex-grow-1 ml-2"
      />
    </div>
  </v-card>
</template>

<script setup lang="ts">
// 无需额外逻辑，纯展示组件
</script>

<style scoped>
.borrow-record-skeleton {
  border-radius: 16px !important;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.status-skeleton {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1;
}

.book-section-skeleton {
  padding: 16px;
  padding-top: 48px;
}

.cover-skeleton {
  border-radius: 8px !important;
}

.book-details-skeleton {
  min-width: 0;
}

.borrow-info-skeleton {
  flex: 1;
}

.actions-skeleton {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  background: rgba(0, 0, 0, 0.02);
  display: flex;
  gap: 8px;
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .actions-skeleton {
    border-top-color: rgba(255, 255, 255, 0.12);
    background: rgba(255, 255, 255, 0.02);
  }
}
</style>
