<template>
  <v-card
    class="borrow-record-card"
    :class="{ 'overdue-card': isOverdue }"
    elevation="2"
    @click="handleCardClick"
  >
    <!-- 状态标签 -->
    <div class="status-overlay">
      <v-chip
        :color="statusColor"
        variant="elevated"
        size="small"
        class="status-chip"
      >
        {{ statusText }}
      </v-chip>
      <v-chip
        v-if="isOverdue"
        color="error"
        variant="elevated"
        size="small"
        class="overdue-chip ml-1"
      >
        逾期
      </v-chip>
    </div>

    <!-- 图书信息 -->
    <div class="book-section">
      <div class="d-flex">
        <!-- 图书封面 -->
        <BookCover
          :src="getBookCoverUrl()"
          :alt="getBookTitle()"
          :title="getBookTitle()"
          width="80px"
          aspect-ratio="3/5"
          :icon-size="24"
        />

        <!-- 图书详情 -->
        <div class="book-details ml-3 flex-grow-1">
          <h3 class="book-title text-h6 font-weight-bold mb-1">
            {{ getBookTitle() }}
          </h3>
          <p class="book-author text-body-2 text-medium-emphasis mb-2">
            作者：{{ getBookAuthor() }}
          </p>
          <div v-if="getBookIsbn()" class="book-isbn text-body-2 text-medium-emphasis mb-2">
            <v-icon size="14" class="me-1">mdi-barcode</v-icon>
            ISBN：{{ getBookIsbn() }}
          </div>
          <div v-if="record.book?.publisher" class="book-publisher text-body-2 text-medium-emphasis mb-2">
            出版社：{{ record.book?.publisher }}
          </div>
        </div>
      </div>
    </div>

    <v-divider />

    <!-- 借阅信息 -->
    <v-card-text class="borrow-info pa-4">
      <div class="info-row mb-2">
        <v-icon size="16" class="me-2">mdi-calendar</v-icon>
        <span class="text-body-2">借阅日期：{{ formatBorrowDate(getBorrowDate(record)) }}</span>
      </div>

      <div class="info-row mb-2">
        <v-icon size="16" class="me-2" :color="dueDateColor">mdi-calendar-clock</v-icon>
        <span class="text-body-2" :class="{ 'text-error': isOverdue }">
          应还日期：{{ formatBorrowDate(getDueDate(record)) }}
        </span>
      </div>

      <div v-if="getReturnDate(record)" class="info-row mb-2">
        <v-icon size="16" class="me-2" color="success">mdi-calendar-check</v-icon>
        <span class="text-body-2">归还日期：{{ formatBorrowDate(getReturnDate(record)) }}</span>
      </div>

      <div v-if="record.renewCount > 0" class="info-row mb-2">
        <v-icon size="16" class="me-2" color="warning">mdi-book-refresh</v-icon>
        <span class="text-body-2">续借次数：{{ record.renewCount }}/2</span>
      </div>

      <div v-if="canShowRenewHint" class="info-row mb-2">
        <v-icon size="16" class="me-2" color="info">mdi-information</v-icon>
        <span class="text-body-2 text-info">{{ renewHintText }}</span>
      </div>

      <div v-if="record.remarks" class="info-row">
        <v-icon size="16" class="me-2">mdi-note-text</v-icon>
        <span class="text-body-2">备注：{{ record.remarks }}</span>
      </div>
    </v-card-text>

    <!-- 操作按钮 -->
    <v-card-actions class="card-actions pa-4">
      <v-btn
        variant="outlined"
        size="small"
        prepend-icon="mdi-eye"
        @click.stop="handleViewDetail"
        class="flex-grow-1"
      >
        查看详情
      </v-btn>

      <v-btn
        v-if="canReturn"
        color="success"
        variant="elevated"
        size="small"
        prepend-icon="mdi-book-arrow-left"
        @click.stop="handleReturn"
        class="flex-grow-1 ml-2"
      >
        归还
      </v-btn>

      <v-btn
        v-if="canRenew"
        color="info"
        variant="elevated"
        size="small"
        prepend-icon="mdi-book-refresh"
        @click.stop="handleRenew"
        class="flex-grow-1 ml-2"
      >
        续借 ({{ 2 - (record.renewCount || 0) }}次)
      </v-btn>

      <v-btn
        v-else-if="record.status === 'BORROWED' && !isOverdue"
        color="grey"
        variant="outlined"
        size="small"
        disabled
        class="flex-grow-1 ml-2"
        :title="renewDisabledReason"
      >
        {{ renewButtonText }}
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { BorrowRecord } from '@/types';
import { formatDate, formatBorrowDate, formatBorrowTime, getBorrowDate, getDueDate, getReturnDate, isDateOverdue } from '@/utils/date';
import BookCover from '@/components/common/BookCover.vue';

interface Props {
  record: BorrowRecord;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'return', record: BorrowRecord): void;
  (e: 'renew', record: BorrowRecord): void;
  (e: 'view-detail', record: BorrowRecord): void;
}

const emit = defineEmits<Emits>();

// 计算属性
const isOverdue = computed(() => {
  if (props.record.status === 'RETURNED' || props.record.status === 'OVERDUE') {
    return props.record.status === 'OVERDUE';
  }

  const dueDateStr = getDueDate(props.record);
  return isDateOverdue(dueDateStr);
});

const canReturn = computed(() => {
  return props.record.status === 'BORROWED' || props.record.status === 'OVERDUE';
});

const canRenew = computed(() => {
  if (props.record.status !== 'BORROWED' || isOverdue.value) {
    return false;
  }

  // 检查续借次数限制（最多2次）
  if ((props.record.renewCount || 0) >= 2) {
    return false;
  }

  // 检查是否在续借时间窗口内（距离到期日5天内）
  const dueDateStr = props.record.dueTime || props.record.dueDate || '';
  if (!dueDateStr) return false;

  // 处理后端时间格式
  let dueDate: Date;
  if (dueDateStr.includes('T')) {
    dueDate = new Date(dueDateStr);
  } else if (dueDateStr.includes(' ')) {
    dueDate = new Date(dueDateStr.replace(' ', 'T'));
  } else {
    dueDate = new Date(dueDateStr);
  }

  const today = new Date();

  // 只比较日期部分，忽略时间
  const dueDateOnly = new Date(dueDate.getFullYear(), dueDate.getMonth(), dueDate.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  const diffDays = Math.ceil((dueDateOnly.getTime() - todayOnly.getTime()) / (1000 * 60 * 60 * 24));

  // 调试信息（生产环境可移除）
  if (process.env.NODE_ENV === 'development') {
    console.log('续借时间检查:', {
      dueDateStr,
      dueDate: dueDateOnly.toDateString(),
      today: todayOnly.toDateString(),
      diffDays,
      canRenewByTime: diffDays <= 5 && diffDays >= 0
    });
  }

  if (diffDays > 5 || diffDays < 0) {
    return false;
  }

  // 检查总借阅时间是否超过3个月
  const borrowDate = new Date(props.record.borrowTime || props.record.borrowDate || '');
  const diffMonths = (today.getFullYear() - borrowDate.getFullYear()) * 12 +
                    (today.getMonth() - borrowDate.getMonth());
  if (diffMonths >= 3) {
    return false;
  }

  return true;
});

const statusColor = computed(() => {
  const colors: Record<string, string> = {
    BORROWED: 'success',
    RETURNED: 'info',
    OVERDUE: 'error',
    RENEWED: 'warning',
  };
  return colors[props.record.status] || 'grey';
});

const statusText = computed(() => {
  const texts: Record<string, string> = {
    BORROWED: '借阅中',
    RETURNED: '已归还',
    OVERDUE: '逾期',
    RENEWED: '续借',
  };
  return texts[props.record.status] || '未知';
});

const dueDateColor = computed(() => {
  if (isOverdue.value) return 'error';

  const dueDate = new Date(props.record.dueDate);
  const today = new Date();
  const diffDays = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

  if (diffDays <= 3) return 'warning';
  return 'default';
});

// 续借提示相关
const canShowRenewHint = computed(() => {
  if (props.record.status !== 'BORROWED' || isOverdue.value) return false;

  const dueDateStr = props.record.dueTime || props.record.dueDate || '';
  if (!dueDateStr) return false;

  // 处理后端时间格式
  let dueDate: Date;
  if (dueDateStr.includes('T')) {
    dueDate = new Date(dueDateStr);
  } else if (dueDateStr.includes(' ')) {
    dueDate = new Date(dueDateStr.replace(' ', 'T'));
  } else {
    dueDate = new Date(dueDateStr);
  }

  const today = new Date();

  // 只比较日期部分
  const dueDateOnly = new Date(dueDate.getFullYear(), dueDate.getMonth(), dueDate.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  const diffDays = Math.ceil((dueDateOnly.getTime() - todayOnly.getTime()) / (1000 * 60 * 60 * 24));

  return diffDays <= 5 && diffDays >= 0 && (props.record.renewCount || 0) < 2;
});

const renewHintText = computed(() => {
  const dueDateStr = props.record.dueTime || props.record.dueDate || '';
  if (!dueDateStr) return '';

  // 处理后端时间格式
  let dueDate: Date;
  if (dueDateStr.includes('T')) {
    dueDate = new Date(dueDateStr);
  } else if (dueDateStr.includes(' ')) {
    dueDate = new Date(dueDateStr.replace(' ', 'T'));
  } else {
    dueDate = new Date(dueDateStr);
  }

  const today = new Date();

  // 只比较日期部分
  const dueDateOnly = new Date(dueDate.getFullYear(), dueDate.getMonth(), dueDate.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  const diffDays = Math.ceil((dueDateOnly.getTime() - todayOnly.getTime()) / (1000 * 60 * 60 * 24));

  if (diffDays <= 5 && diffDays >= 0) {
    return `还有${diffDays}天到期，可申请续借`;
  }
  return '';
});

const renewDisabledReason = computed(() => {
  if ((props.record.renewCount || 0) >= 2) {
    return '已达到最大续借次数';
  }

  const dueDateStr = props.record.dueTime || props.record.dueDate || '';
  if (!dueDateStr) return '无法获取到期日期';

  // 处理后端时间格式
  let dueDate: Date;
  if (dueDateStr.includes('T')) {
    dueDate = new Date(dueDateStr);
  } else if (dueDateStr.includes(' ')) {
    dueDate = new Date(dueDateStr.replace(' ', 'T'));
  } else {
    dueDate = new Date(dueDateStr);
  }

  const today = new Date();

  // 只比较日期部分
  const dueDateOnly = new Date(dueDate.getFullYear(), dueDate.getMonth(), dueDate.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  const diffDays = Math.ceil((dueDateOnly.getTime() - todayOnly.getTime()) / (1000 * 60 * 60 * 24));

  if (diffDays > 5) {
    return `还需等待${diffDays - 5}天才能续借`;
  }

  if (diffDays < 0) {
    return '图书已逾期，无法续借';
  }

  const borrowDateStr = props.record.borrowTime || props.record.borrowDate || '';
  if (borrowDateStr) {
    let borrowDate: Date;
    if (borrowDateStr.includes('T')) {
      borrowDate = new Date(borrowDateStr);
    } else if (borrowDateStr.includes(' ')) {
      borrowDate = new Date(borrowDateStr.replace(' ', 'T'));
    } else {
      borrowDate = new Date(borrowDateStr);
    }

    const diffMonths = (today.getFullYear() - borrowDate.getFullYear()) * 12 +
                      (today.getMonth() - borrowDate.getMonth());
    if (diffMonths >= 3) {
      return '借阅时间已超过3个月限制';
    }
  }

  return '暂时无法续借';
});

const renewButtonText = computed(() => {
  if ((props.record.renewCount || 0) >= 2) {
    return '已达上限';
  }

  const dueDateStr = props.record.dueTime || props.record.dueDate || '';
  if (!dueDateStr) return '无法续借';

  // 处理后端时间格式
  let dueDate: Date;
  if (dueDateStr.includes('T')) {
    dueDate = new Date(dueDateStr);
  } else if (dueDateStr.includes(' ')) {
    dueDate = new Date(dueDateStr.replace(' ', 'T'));
  } else {
    dueDate = new Date(dueDateStr);
  }

  const today = new Date();

  // 只比较日期部分
  const dueDateOnly = new Date(dueDate.getFullYear(), dueDate.getMonth(), dueDate.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  const diffDays = Math.ceil((dueDateOnly.getTime() - todayOnly.getTime()) / (1000 * 60 * 60 * 24));

  if (diffDays > 5) {
    return `${diffDays - 5}天后可续借`;
  }

  return '续借';
});

// 获取图书信息的方法（兼容不同数据结构）
const getBookTitle = () => {
  return props.record.bookTitle || props.record.book?.title || '未知图书';
};

const getBookAuthor = () => {
  return props.record.bookAuthor || props.record.book?.author || '未知作者';
};

const getBookIsbn = () => {
  return props.record.bookIsbn || props.record.book?.isbn || '';
};

const getBookCoverUrl = () => {
  // 优先使用后端返回的图书封面URL
  const coverUrl = props.record.bookCoverUrl || props.record.book?.coverUrl;
  if (coverUrl && coverUrl.trim()) {
    return coverUrl;
  }

  // 生成默认封面图片
  const title = getBookTitle();
  return `https://via.placeholder.com/120x200/1976d2/ffffff?text=${encodeURIComponent(title)}`;
};

const defaultCoverUrl = computed(() => {
  return getBookCoverUrl();
});



// 事件处理
const handleCardClick = () => {
  handleViewDetail();
};

const handleViewDetail = () => {
  emit('view-detail', props.record);
};

const handleReturn = () => {
  emit('return', props.record);
};

const handleRenew = () => {
  emit('renew', props.record);
};
</script>

<style scoped>
.borrow-record-card {
  border-radius: 16px !important;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.borrow-record-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(25, 118, 210, 0.2) !important;
}

.overdue-card {
  border: 2px solid rgba(244, 67, 54, 0.3);
}

.status-overlay {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1;
  display: flex;
}

.status-chip,
.overdue-chip {
  backdrop-filter: blur(10px);
}

.book-section {
  padding: 16px;
  padding-top: 48px; /* 为状态标签留空间 */
}



.book-details {
  min-width: 0;
}

.book-title {
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.borrow-info {
  flex: 1;
}

.info-row {
  display: flex;
  align-items: center;
}

.card-actions {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  background: rgba(25, 118, 210, 0.02);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .card-actions .v-btn {
    width: 100%;
    margin: 0 !important;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .card-actions {
    border-top-color: rgba(255, 255, 255, 0.12);
    background: rgba(66, 165, 245, 0.05);
  }

  .overdue-card {
    border-color: rgba(239, 83, 80, 0.3);
  }
}
</style>
