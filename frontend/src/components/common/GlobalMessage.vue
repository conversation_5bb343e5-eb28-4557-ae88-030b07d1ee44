<template>
  <!-- 全局消息提示 -->
  <v-snackbar
    v-model="messageStore.show"
    :color="messageStore.type"
    :timeout="messageStore.timeout"
    :location="messageStore.position"
    :multi-line="messageStore.multiLine"
    :vertical="messageStore.vertical"
    class="global-message"
  >
    <div class="message-content">
      <v-icon
        v-if="messageIcon"
        :icon="messageIcon"
        class="me-2"
        size="20"
      />
      <span class="message-text">{{ messageStore.message }}</span>
    </div>

    <template #actions>
      <v-btn
        v-if="messageStore.action"
        variant="text"
        size="small"
        @click="handleAction"
      >
        {{ messageStore.action.text }}
      </v-btn>
      <v-btn
        variant="text"
        size="small"
        icon
        @click="messageStore.hide()"
      >
        <v-icon>mdi-close</v-icon>
      </v-btn>
    </template>
  </v-snackbar>

  <!-- 全局确认对话框 -->
  <v-dialog
    v-model="confirmStore.show"
    max-width="400"
    persistent
  >
    <v-card class="confirm-dialog">
      <v-card-title class="dialog-header">
        <v-icon
          :icon="confirmIcon"
          :color="confirmColor"
          class="me-2"
        />
        {{ confirmStore.title }}
      </v-card-title>

      <v-divider />

      <v-card-text class="dialog-content">
        <p class="text-body-1">{{ confirmStore.message }}</p>
        <div v-if="confirmStore.details" class="details mt-3">
          <v-expansion-panels variant="accordion">
            <v-expansion-panel>
              <v-expansion-panel-title>
                <v-icon class="me-2">mdi-information</v-icon>
                详细信息
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <pre class="details-text">{{ confirmStore.details }}</pre>
              </v-expansion-panel-text>
            </v-expansion-panel>
          </v-expansion-panels>
        </div>
      </v-card-text>

      <v-card-actions class="dialog-actions">
        <v-spacer />
        <v-btn
          variant="outlined"
          @click="handleConfirmCancel"
          :disabled="confirmStore.loading"
        >
          {{ confirmStore.cancelText }}
        </v-btn>
        <v-btn
          :color="confirmColor"
          variant="elevated"
          @click="handleConfirmOk"
          :loading="confirmStore.loading"
        >
          {{ confirmStore.confirmText }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>

  <!-- 全局加载遮罩 -->
  <v-overlay
    v-model="loadingStore.show"
    class="global-loading"
    persistent
  >
    <div class="loading-content">
      <v-progress-circular
        :size="60"
        :width="4"
        color="primary"
        indeterminate
        class="mb-4"
      />
      <div class="loading-text">
        {{ loadingStore.message }}
      </div>
      <div v-if="loadingStore.progress !== null" class="loading-progress mt-3">
        <v-progress-linear
          :model-value="loadingStore.progress"
          color="primary"
          height="4"
          rounded
        />
        <div class="text-caption text-center mt-1">
          {{ Math.round(loadingStore.progress) }}%
        </div>
      </div>
    </div>
  </v-overlay>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useMessageStore } from '@/stores/message';
import { useConfirmStore } from '@/stores/confirm';
import { useLoadingStore } from '@/stores/loading';

const messageStore = useMessageStore();
const confirmStore = useConfirmStore();
const loadingStore = useLoadingStore();

// 消息图标
const messageIcon = computed(() => {
  const icons: Record<string, string> = {
    success: 'mdi-check-circle',
    error: 'mdi-alert-circle',
    warning: 'mdi-alert',
    info: 'mdi-information',
  };
  return icons[messageStore.type] || null;
});

// 确认对话框图标和颜色
const confirmIcon = computed(() => {
  const icons: Record<string, string> = {
    success: 'mdi-check-circle',
    error: 'mdi-alert-circle',
    warning: 'mdi-alert',
    info: 'mdi-information',
    question: 'mdi-help-circle',
  };
  return icons[confirmStore.type] || 'mdi-help-circle';
});

const confirmColor = computed(() => {
  const colors: Record<string, string> = {
    success: 'success',
    error: 'error',
    warning: 'warning',
    info: 'info',
    question: 'primary',
  };
  return colors[confirmStore.type] || 'primary';
});

// 处理消息操作
const handleAction = () => {
  if (messageStore.action?.handler) {
    messageStore.action.handler();
  }
  messageStore.hide();
};

// 处理确认对话框
const handleConfirmOk = async () => {
  if (confirmStore.onConfirm) {
    confirmStore.loading = true;
    try {
      await confirmStore.onConfirm();
      confirmStore.hide();
    } catch (error) {
      console.error('Confirm action failed:', error);
    } finally {
      confirmStore.loading = false;
    }
  } else {
    confirmStore.hide();
  }
};

const handleConfirmCancel = () => {
  if (confirmStore.onCancel) {
    confirmStore.onCancel();
  }
  confirmStore.hide();
};
</script>

<style scoped>
.global-message {
  z-index: 9999;
}

.message-content {
  display: flex;
  align-items: center;
}

.message-text {
  flex: 1;
  word-break: break-word;
}

.confirm-dialog {
  border-radius: 16px !important;
}

.dialog-header {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 100%);
  font-weight: 600;
}

.dialog-content {
  padding: 24px !important;
}

.details-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
  background: rgba(0, 0, 0, 0.05);
  padding: 12px;
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.dialog-actions {
  padding: 16px 24px !important;
  background: rgba(0, 0, 0, 0.02);
}

.global-loading {
  z-index: 10000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  color: #1976d2;
}

.loading-progress {
  width: 200px;
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .dialog-header {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  }
  
  .dialog-actions {
    background: rgba(255, 255, 255, 0.02);
  }
  
  .details-text {
    background: rgba(255, 255, 255, 0.05);
    color: #e0e0e0;
  }
  
  .loading-content {
    background: rgba(30, 30, 30, 0.95);
    color: white;
  }
  
  .loading-text {
    color: #42a5f5;
  }
}
</style>
