<template>
  <div class="book-cover-wrapper" :style="{ width: width, height: height }">
    <v-img
      :src="currentImageUrl"
      :alt="alt"
      :aspect-ratio="aspectRatio"
      :cover="false"
      contain
      class="book-cover-image"
      @error="handleImageError"
      @load="handleImageLoad"
    >
      <template #placeholder>
        <div class="cover-placeholder">
          <v-icon :size="iconSize" color="grey-lighten-2">mdi-book</v-icon>
          <div v-if="showLoadingText" class="text-caption mt-1">加载中...</div>
        </div>
      </template>
      <template #error>
        <div class="cover-placeholder">
          <v-icon :size="iconSize" color="grey-lighten-2">mdi-book</v-icon>
          <div class="text-caption mt-1 text-center px-2">{{ fallbackText }}</div>
        </div>
      </template>
    </v-img>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

interface Props {
  src?: string;
  alt?: string;
  title?: string;
  width?: string;
  height?: string;
  aspectRatio?: string;
  iconSize?: string | number;
  showLoadingText?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  src: '',
  alt: '图书封面',
  title: '未知图书',
  width: '100px',
  height: 'auto',
  aspectRatio: '3/5',
  iconSize: 24,
  showLoadingText: false,
});

const currentImageUrl = ref('');
const imageLoadFailed = ref(false);

// 计算属性
const fallbackText = computed(() => {
  if (props.title && props.title.length > 0) {
    // 如果标题太长，只显示前几个字符
    return props.title.length > 8 ? props.title.substring(0, 8) + '...' : props.title;
  }
  return '暂无封面';
});

// 图片URL处理
const processImageUrl = (url: string) => {
  if (!url || !url.trim()) {
    return generatePlaceholderUrl();
  }

  // 检查是否是外部图片链接
  if (url.startsWith('http')) {
    // 对于豆瓣等外部图片，可能存在跨域问题
    // 这里我们仍然尝试加载，但会有错误处理
    return url;
  }

  return url;
};

// 生成占位图片URL
const generatePlaceholderUrl = () => {
  const text = encodeURIComponent(props.title || '图书');
  return `https://via.placeholder.com/120x200/1976d2/ffffff?text=${text}`;
};

// 事件处理
const handleImageLoad = () => {
  imageLoadFailed.value = false;
};

const handleImageError = () => {
  imageLoadFailed.value = true;
  
  // 如果当前URL不是占位图片，则尝试使用占位图片
  if (!currentImageUrl.value.includes('via.placeholder.com')) {
    console.warn('图书封面加载失败，使用占位图片:', currentImageUrl.value);
    currentImageUrl.value = generatePlaceholderUrl();
  }
};

// 监听src变化
watch(
  () => props.src,
  (newSrc) => {
    imageLoadFailed.value = false;
    currentImageUrl.value = processImageUrl(newSrc || '');
  },
  { immediate: true }
);

// 监听title变化，更新占位图片
watch(
  () => props.title,
  () => {
    if (imageLoadFailed.value || !props.src) {
      currentImageUrl.value = generatePlaceholderUrl();
    }
  }
);
</script>

<style scoped>
.book-cover-wrapper {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.book-cover-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.cover-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  padding: 8px;
  box-sizing: border-box;
}

.cover-placeholder .text-caption {
  font-size: 0.75rem;
  line-height: 1.2;
  word-break: break-all;
  text-align: center;
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .book-cover-wrapper {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
  }
  
  .cover-placeholder {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
  }
}
</style>
