<template>
  <div v-if="hasPermission">
    <slot />
  </div>
  <div v-else-if="showFallback">
    <slot name="fallback">
      <v-alert
        v-if="showNoPermissionAlert"
        type="warning"
        variant="tonal"
        class="ma-4"
      >
        <v-alert-title>权限不足</v-alert-title>
        您没有权限访问此内容
      </v-alert>
    </slot>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useUserStore } from '@/stores/user';
import { hasPermission as checkPermission } from '@/utils/permission';
import type { RoleType } from '@/utils/permission';

interface Props {
  // 需要的角色权限
  roles?: RoleType[];
  // 是否需要账户激活
  requireActive?: boolean;
  // 是否显示无权限时的后备内容
  showFallback?: boolean;
  // 是否显示无权限警告
  showNoPermissionAlert?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  roles: () => [],
  requireActive: true,
  showFallback: false,
  showNoPermissionAlert: false,
});

const userStore = useUserStore();

// 检查是否有权限
const hasPermission = computed(() => {
  return checkPermission(userStore.userInfo, props.roles, props.requireActive);
});
</script>
