<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    max-width="500"
  >
    <v-card class="user-detail-dialog">
      <!-- 对话框头部 -->
      <v-card-title class="dialog-header">
        <v-icon class="me-2" color="info">mdi-account-details</v-icon>
        用户详情
      </v-card-title>

      <v-divider />

      <!-- 对话框内容 -->
      <v-card-text v-if="user" class="dialog-content">
        <!-- 用户头像和基本信息 -->
        <div class="user-profile-section mb-4">
          <div class="d-flex align-center">
            <v-avatar size="80" class="me-4">
              <v-img
                v-if="user.avatar"
                :src="user.avatar"
                :alt="user.username"
              />
              <v-icon v-else size="48">mdi-account-circle</v-icon>
            </v-avatar>
            
            <div class="flex-grow-1">
              <h3 class="text-h6 font-weight-bold mb-1">{{ user.realName }}</h3>
              <p class="text-body-2 text-medium-emphasis mb-1">@{{ user.username }}</p>
              <div class="d-flex align-center gap-2">
                <v-chip
                  :color="getRoleColor(user.role)"
                  variant="tonal"
                  size="small"
                >
                  {{ getRoleDisplayName(user.role) }}
                </v-chip>
                <v-chip
                  :color="user.status === 1 ? 'success' : 'error'"
                  variant="tonal"
                  size="small"
                >
                  {{ user.status === 1 ? '正常' : '禁用' }}
                </v-chip>
              </div>
            </div>
          </div>
        </div>

        <!-- 详细信息表格 -->
        <v-table class="detail-table">
          <tbody>
            <tr>
              <td class="detail-label">用户ID</td>
              <td class="detail-value">{{ user.id }}</td>
            </tr>
            <tr>
              <td class="detail-label">用户名</td>
              <td class="detail-value">{{ user.username }}</td>
            </tr>
            <tr>
              <td class="detail-label">真实姓名</td>
              <td class="detail-value">{{ user.realName }}</td>
            </tr>
            <tr>
              <td class="detail-label">邮箱</td>
              <td class="detail-value">
                <a :href="`mailto:${user.email}`" class="text-primary">
                  {{ user.email }}
                </a>
              </td>
            </tr>
            <tr v-if="user.phone">
              <td class="detail-label">手机号</td>
              <td class="detail-value">
                <a :href="`tel:${user.phone}`" class="text-primary">
                  {{ user.phone }}
                </a>
              </td>
            </tr>
            <tr>
              <td class="detail-label">用户角色</td>
              <td class="detail-value">
                <v-chip
                  :color="getRoleColor(user.role)"
                  variant="tonal"
                  size="small"
                >
                  {{ getRoleDisplayName(user.role) }}
                </v-chip>
              </td>
            </tr>
            <tr>
              <td class="detail-label">账户状态</td>
              <td class="detail-value">
                <v-chip
                  :color="user.status === 1 ? 'success' : 'error'"
                  variant="tonal"
                  size="small"
                >
                  {{ user.status === 1 ? '正常' : '禁用' }}
                </v-chip>
              </td>
            </tr>
            <tr v-if="user.lastLoginTime">
              <td class="detail-label">最后登录</td>
              <td class="detail-value">{{ formatDateTime(user.lastLoginTime) }}</td>
            </tr>
            <tr v-if="user.createdTime">
              <td class="detail-label">注册时间</td>
              <td class="detail-value">{{ formatDateTime(user.createdTime) }}</td>
            </tr>
            <tr v-if="user.updatedTime">
              <td class="detail-label">更新时间</td>
              <td class="detail-value">{{ formatDateTime(user.updatedTime) }}</td>
            </tr>
          </tbody>
        </v-table>
      </v-card-text>

      <!-- 对话框操作 -->
      <v-card-actions class="dialog-actions">
        <v-spacer />
        <v-btn
          variant="outlined"
          @click="$emit('update:modelValue', false)"
        >
          关闭
        </v-btn>
        <v-btn
          v-if="user && canEdit"
          color="primary"
          variant="elevated"
          prepend-icon="mdi-pencil"
          @click="handleEdit"
        >
          编辑
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useUserStore } from '@/stores/user';
import { getRoleDisplayName, canManageUser } from '@/utils/permission';
import type { User } from '@/types';

interface Props {
  modelValue: boolean;
  user?: User | null;
}

const props = withDefaults(defineProps<Props>(), {
  user: null,
});

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'edit', user: User): void;
}

const emit = defineEmits<Emits>();

const userStore = useUserStore();

// 计算属性
const canEdit = computed(() => {
  if (!props.user || !userStore.userInfo) return false;
  return canManageUser(userStore.userInfo, props.user);
});

// 获取角色颜色
const getRoleColor = (role: string) => {
  const colors: Record<string, string> = {
    USER: 'primary',
    ADMIN: 'warning',
    SUPER_ADMIN: 'error',
  };
  return colors[role] || 'grey';
};

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

// 处理编辑
const handleEdit = () => {
  if (props.user) {
    emit('edit', props.user);
  }
};
</script>

<style scoped>
.user-detail-dialog {
  border-radius: 16px !important;
}

.dialog-header {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 100%);
  font-weight: 600;
}

.dialog-content {
  padding: 24px !important;
}

.user-profile-section {
  background: rgba(25, 118, 210, 0.02);
  border-radius: 12px;
  padding: 16px;
}

.detail-table {
  border-radius: 12px !important;
  overflow: hidden;
}

.detail-label {
  font-weight: 600;
  color: rgba(0, 0, 0, 0.7);
  width: 120px;
  padding: 12px 16px !important;
  background: rgba(25, 118, 210, 0.05);
}

.detail-value {
  padding: 12px 16px !important;
}

.dialog-actions {
  padding: 16px 24px !important;
  background: rgba(0, 0, 0, 0.02);
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .dialog-header {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  }
  
  .user-profile-section {
    background: rgba(66, 165, 245, 0.05);
  }
  
  .detail-label {
    color: rgba(255, 255, 255, 0.7);
    background: rgba(66, 165, 245, 0.1);
  }
  
  .dialog-actions {
    background: rgba(255, 255, 255, 0.02);
  }
}
</style>
