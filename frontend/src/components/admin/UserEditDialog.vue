<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    max-width="600"
    persistent
  >
    <v-card class="user-edit-dialog">
      <!-- 对话框头部 -->
      <v-card-title class="dialog-header">
        <v-icon class="me-2" :color="isEdit ? 'warning' : 'primary'">
          {{ isEdit ? 'mdi-account-edit' : 'mdi-account-plus' }}
        </v-icon>
        {{ isEdit ? '编辑用户' : '添加用户' }}
      </v-card-title>

      <v-divider />

      <!-- 对话框内容 -->
      <v-card-text class="dialog-content">
        <v-form ref="userFormRef" v-model="formValid">
          <v-row>
            <!-- 用户名 -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model="userForm.username"
                label="用户名"
                prepend-inner-icon="mdi-account"
                variant="outlined"
                :rules="usernameRules"
                :readonly="isEdit"
                :hint="isEdit ? '用户名不可修改' : ''"
                persistent-hint
                required
              />
            </v-col>

            <!-- 邮箱 -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model="userForm.email"
                label="邮箱"
                prepend-inner-icon="mdi-email"
                variant="outlined"
                :rules="emailRules"
                required
              />
            </v-col>

            <!-- 真实姓名 -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model="userForm.realName"
                label="真实姓名"
                prepend-inner-icon="mdi-card-account-details"
                variant="outlined"
                :rules="realNameRules"
                required
              />
            </v-col>

            <!-- 手机号 -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model="userForm.phone"
                label="手机号"
                prepend-inner-icon="mdi-phone"
                variant="outlined"
                :rules="phoneRules"
              />
            </v-col>

            <!-- 角色 -->
            <v-col cols="12" md="6">
              <v-select
                v-model="userForm.role"
                :items="roleOptions"
                label="用户角色"
                prepend-inner-icon="mdi-shield-account"
                variant="outlined"
                :rules="roleRules"
                required
              />
            </v-col>

            <!-- 状态 -->
            <v-col cols="12" md="6">
              <v-select
                v-model="userForm.status"
                :items="statusOptions"
                label="账户状态"
                prepend-inner-icon="mdi-account-check"
                variant="outlined"
                :rules="statusRules"
                required
              />
            </v-col>

            <!-- 密码（仅新增时显示） -->
            <v-col v-if="!isEdit" cols="12" md="6">
              <v-text-field
                v-model="userForm.password"
                label="密码"
                prepend-inner-icon="mdi-lock"
                variant="outlined"
                :type="showPassword ? 'text' : 'password'"
                :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                :rules="passwordRules"
                @click:append-inner="showPassword = !showPassword"
                required
              />
            </v-col>

            <!-- 确认密码（仅新增时显示） -->
            <v-col v-if="!isEdit" cols="12" md="6">
              <v-text-field
                v-model="userForm.confirmPassword"
                label="确认密码"
                prepend-inner-icon="mdi-lock-check"
                variant="outlined"
                :type="showConfirmPassword ? 'text' : 'password'"
                :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                :rules="confirmPasswordRules"
                @click:append-inner="showConfirmPassword = !showConfirmPassword"
                required
              />
            </v-col>
          </v-row>

          <!-- 权限说明 -->
          <v-card variant="tonal" color="info" class="mt-4">
            <v-card-text>
              <div class="d-flex align-start">
                <v-icon color="info" class="me-2 mt-1">mdi-information</v-icon>
                <div>
                  <h4 class="text-subtitle-2 font-weight-bold mb-2">角色权限说明</h4>
                  <ul class="text-body-2 pl-4">
                    <li><strong>普通用户：</strong>可以浏览图书、借阅图书、管理个人信息</li>
                    <li><strong>管理员：</strong>可以管理图书、查看借阅记录、处理借阅业务</li>
                    <li><strong>超级管理员：</strong>拥有所有权限，包括用户管理</li>
                  </ul>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-form>
      </v-card-text>

      <!-- 对话框操作 -->
      <v-card-actions class="dialog-actions">
        <v-spacer />
        <v-btn
          variant="outlined"
          @click="handleCancel"
          :disabled="isLoading"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          @click="handleConfirm"
          :loading="isLoading"
          :disabled="!formValid"
        >
          {{ isEdit ? '保存' : '创建' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import type { User, RegisterForm } from '@/types';

interface Props {
  modelValue: boolean;
  user?: User | null;
  isEdit?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  user: null,
  isEdit: false,
});

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm', data: { userForm: RegisterForm | Partial<User>; isEdit: boolean }): void;
}

const emit = defineEmits<Emits>();

// 响应式状态
const formValid = ref(false);
const isLoading = ref(false);
const showPassword = ref(false);
const showConfirmPassword = ref(false);
const userFormRef = ref();

const userForm = reactive<RegisterForm & { status?: number; role?: string }>({
  username: '',
  email: '',
  realName: '',
  phone: '',
  password: '',
  confirmPassword: '',
  role: 'USER',
  status: 1,
});

// 选项配置
const roleOptions = [
  { title: '普通用户', value: 'USER' },
  { title: '管理员', value: 'ADMIN' },
  { title: '超级管理员', value: 'SUPER_ADMIN' },
];

const statusOptions = [
  { title: '正常', value: 1 },
  { title: '禁用', value: 0 },
];

// 验证规则
const usernameRules = [
  (v: string) => !!v || '请输入用户名',
  (v: string) => (v && v.length >= 3 && v.length <= 20) || '用户名长度必须在3-20个字符之间',
  (v: string) => /^[a-zA-Z0-9_]+$/.test(v) || '用户名只能包含字母、数字和下划线',
];

const emailRules = [
  (v: string) => !!v || '请输入邮箱',
  (v: string) => /.+@.+\..+/.test(v) || '请输入有效的邮箱地址',
];

const realNameRules = [
  (v: string) => !!v || '请输入真实姓名',
  (v: string) => (v && v.length <= 50) || '真实姓名长度不能超过50个字符',
];

const phoneRules = [
  (v: string) => !v || /^1[3-9]\d{9}$/.test(v) || '请输入有效的手机号',
];

const roleRules = [
  (v: string) => !!v || '请选择用户角色',
];

const statusRules = [
  (v: number) => v !== undefined && v !== null || '请选择账户状态',
];

const passwordRules = computed(() => {
  if (props.isEdit) return [];
  return [
    (v: string) => !!v || '请输入密码',
    (v: string) => (v && v.length >= 6 && v.length <= 20) || '密码长度必须在6-20个字符之间',
  ];
});

const confirmPasswordRules = computed(() => {
  if (props.isEdit) return [];
  return [
    (v: string) => !!v || '请确认密码',
    (v: string) => v === userForm.password || '两次输入的密码不一致',
  ];
});

// 处理取消
const handleCancel = () => {
  emit('update:modelValue', false);
  resetForm();
};

// 处理确认
const handleConfirm = async () => {
  if (!formValid.value) return;

  isLoading.value = true;
  try {
    const formData = props.isEdit 
      ? {
          email: userForm.email,
          realName: userForm.realName,
          phone: userForm.phone,
          role: userForm.role,
          status: userForm.status,
        }
      : { ...userForm };

    emit('confirm', {
      userForm: formData,
      isEdit: props.isEdit,
    });
  } finally {
    isLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(userForm, {
    username: '',
    email: '',
    realName: '',
    phone: '',
    password: '',
    confirmPassword: '',
    role: 'USER',
    status: 1,
  });
  
  if (userFormRef.value) {
    userFormRef.value.resetValidation();
  }
};

// 填充表单数据
const fillForm = (user: User) => {
  Object.assign(userForm, {
    username: user.username,
    email: user.email,
    realName: user.realName,
    phone: user.phone || '',
    role: user.role,
    status: user.status,
    password: '',
    confirmPassword: '',
  });
};

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (show) => {
    if (show) {
      if (props.isEdit && props.user) {
        fillForm(props.user);
      } else {
        resetForm();
      }
    }
  }
);
</script>

<style scoped>
.user-edit-dialog {
  border-radius: 16px !important;
}

.dialog-header {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 100%);
  font-weight: 600;
}

.dialog-content {
  padding: 24px !important;
}

.dialog-actions {
  padding: 16px 24px !important;
  background: rgba(0, 0, 0, 0.02);
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .dialog-header {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  }
  
  .dialog-actions {
    background: rgba(255, 255, 255, 0.02);
  }
}
</style>
