<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    max-width="400"
    persistent
  >
    <v-card class="delete-confirm-dialog">
      <!-- 对话框头部 -->
      <v-card-title class="dialog-header">
        <v-icon class="me-2" color="error">mdi-alert-circle</v-icon>
        确认删除
      </v-card-title>

      <v-divider />

      <!-- 对话框内容 -->
      <v-card-text class="dialog-content">
        <div class="text-center">
          <v-icon size="64" color="error" class="mb-4">
            mdi-delete-alert
          </v-icon>
          
          <h3 class="text-h6 font-weight-bold mb-2">
            {{ title || '确认删除操作' }}
          </h3>
          
          <p class="text-body-1 text-medium-emphasis mb-4">
            {{ message || '此操作不可撤销，请确认是否继续？' }}
          </p>

          <!-- 额外信息 -->
          <div v-if="extraInfo" class="extra-info">
            <v-card variant="tonal" color="warning" class="mb-4">
              <v-card-text>
                <div class="d-flex align-start">
                  <v-icon color="warning" class="me-2 mt-1">mdi-information</v-icon>
                  <div class="text-left">
                    <div v-for="(info, index) in extraInfo" :key="index" class="text-body-2">
                      {{ info }}
                    </div>
                  </div>
                </div>
              </v-card-text>
            </v-card>
          </div>

          <!-- 确认输入 -->
          <div v-if="requireConfirmText" class="confirm-input mb-4">
            <p class="text-body-2 mb-2">
              请输入 <strong>{{ confirmText }}</strong> 来确认删除：
            </p>
            <v-text-field
              v-model="inputText"
              variant="outlined"
              density="compact"
              hide-details
              :placeholder="confirmText"
            />
          </div>
        </div>
      </v-card-text>

      <!-- 对话框操作 -->
      <v-card-actions class="dialog-actions">
        <v-spacer />
        <v-btn
          variant="outlined"
          @click="handleCancel"
          :disabled="isLoading"
        >
          取消
        </v-btn>
        <v-btn
          color="error"
          variant="elevated"
          @click="handleConfirm"
          :loading="isLoading"
          :disabled="requireConfirmText && inputText !== confirmText"
        >
          确认删除
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

interface Props {
  modelValue: boolean;
  title?: string;
  message?: string;
  extraInfo?: string[];
  requireConfirmText?: boolean;
  confirmText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  message: '',
  extraInfo: () => [],
  requireConfirmText: false,
  confirmText: 'DELETE',
});

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm'): void;
}

const emit = defineEmits<Emits>();

// 响应式状态
const isLoading = ref(false);
const inputText = ref('');

// 处理取消
const handleCancel = () => {
  emit('update:modelValue', false);
  resetForm();
};

// 处理确认
const handleConfirm = async () => {
  if (props.requireConfirmText && inputText.value !== props.confirmText) {
    return;
  }

  isLoading.value = true;
  try {
    emit('confirm');
  } finally {
    isLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  inputText.value = '';
};

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (show) => {
    if (show) {
      resetForm();
    }
  }
);
</script>

<style scoped>
.delete-confirm-dialog {
  border-radius: 16px !important;
}

.dialog-header {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.05) 0%, rgba(255, 87, 34, 0.05) 100%);
  color: #f44336;
  font-weight: 600;
}

.dialog-content {
  padding: 24px !important;
}

.extra-info {
  text-align: left;
}

.confirm-input {
  text-align: left;
}

.dialog-actions {
  padding: 16px 24px !important;
  background: rgba(0, 0, 0, 0.02);
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .dialog-header {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(255, 87, 34, 0.1) 100%);
    color: #ef5350;
  }
  
  .dialog-actions {
    background: rgba(255, 255, 255, 0.02);
  }
}
</style>
