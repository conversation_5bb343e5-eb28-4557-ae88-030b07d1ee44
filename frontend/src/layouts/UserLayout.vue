<template>
  <v-app>
    <!-- 用户导航抽屉 -->
    <v-navigation-drawer
      v-model="drawer"
      :rail="rail"
      permanent
      class="user-drawer"
      :width="drawerWidth"
    >
      <!-- 抽屉头部 -->
      <div class="drawer-header">
        <div class="logo-section" @click="toggleRail">
          <v-icon size="32" color="primary">mdi-book-open-page-variant</v-icon>
          <v-fade-transition>
            <span v-show="!rail" class="logo-text gradient-text">图书管理</span>
          </v-fade-transition>
        </div>
      </div>

      <v-divider />

      <!-- 用户信息卡片 -->
      <div v-if="!rail" class="user-info-card">
        <v-card variant="tonal" class="ma-3">
          <v-card-text class="text-center">
            <UserAvatar
              :src="userStore.userInfo?.avatar"
              :username="userStore.userInfo?.username"
              :real-name="userStore.userInfo?.realName"
              size="48"
              class="mb-2"
            />
            <div class="text-subtitle-2 font-weight-medium">
              {{ userStore.userInfo?.realName || userStore.userInfo?.username }}
            </div>
            <div class="text-caption text-medium-emphasis">
              {{ getRoleDisplayName(userStore.userInfo?.role || '') }}
            </div>
          </v-card-text>
        </v-card>
      </div>

      <!-- 导航菜单 -->
      <v-list nav class="navigation-list">
        <template v-for="item in userMenuItems" :key="item.title">
          <v-list-item
            :to="item.to"
            :prepend-icon="item.icon"
            :title="item.title"
            class="menu-item"
          >
            <template v-if="item.badge" #append>
              <v-badge
                :content="item.badge"
                color="error"
                inline
              />
            </template>
          </v-list-item>
        </template>
      </v-list>

      <!-- 抽屉底部 -->
      <template #append>
        <div class="drawer-footer">
          <v-btn
            v-if="!rail"
            variant="text"
            size="small"
            @click="toggleRail"
            class="toggle-btn"
          >
            <v-icon>mdi-chevron-left</v-icon>
            收起
          </v-btn>
          <v-btn
            v-else
            variant="text"
            size="small"
            icon
            @click="toggleRail"
            class="toggle-btn"
          >
            <v-icon>mdi-chevron-right</v-icon>
          </v-btn>
        </div>
      </template>
    </v-navigation-drawer>

    <!-- 应用栏 -->
    <v-app-bar
      :order="-1"
      class="user-app-bar"
      elevation="1"
      height="64"
    >
      <!-- 移动端菜单按钮 -->
      <v-app-bar-nav-icon
        v-if="mobile"
        @click="drawer = !drawer"
        class="d-lg-none"
      />

      <!-- 页面标题 -->
      <v-app-bar-title class="page-title">
        {{ pageTitle }}
      </v-app-bar-title>

      <v-spacer />

      <!-- 搜索框 -->
      <div class="search-section">
        <v-text-field
          v-model="searchQuery"
          placeholder="搜索图书..."
          prepend-inner-icon="mdi-magnify"
          variant="outlined"
          density="compact"
          hide-details
          class="search-field"
          @keyup.enter="handleSearch"
        />
      </div>

      <!-- 用户操作区域 -->
      <div class="user-actions">
        <!-- 通知按钮 -->
        <v-btn
          icon
          variant="text"
          class="action-btn"
        >
          <v-badge
            v-if="hasNotifications"
            color="error"
            content="3"
          >
            <v-icon>mdi-bell</v-icon>
          </v-badge>
          <v-icon v-else>mdi-bell-outline</v-icon>
        </v-btn>

        <!-- 管理员入口 -->
        <v-btn
          v-if="userStore.isAdmin"
          icon
          variant="text"
          @click="$router.push('/admin')"
          class="action-btn"
        >
          <v-icon>mdi-cog</v-icon>
        </v-btn>

        <!-- 用户菜单 -->
        <v-menu offset-y>
          <template #activator="{ props }">
            <v-btn
              v-bind="props"
              variant="text"
              class="user-menu-btn"
            >
              <UserAvatar
                :src="userStore.userInfo?.avatar"
                :username="userStore.userInfo?.username"
                :real-name="userStore.userInfo?.realName"
                size="32"
              />
              <v-icon>mdi-chevron-down</v-icon>
            </v-btn>
          </template>

          <v-list class="user-menu">
            <v-list-item>
              <v-list-item-title>{{ userStore.userInfo?.username }}</v-list-item-title>
              <v-list-item-subtitle>{{ getRoleDisplayName(userStore.userInfo?.role || '') }}</v-list-item-subtitle>
            </v-list-item>
            
            <v-divider />
            
            <v-list-item
              prepend-icon="mdi-account"
              title="个人信息"
              @click="$router.push('/profile')"
            />
            
            <v-list-item
              v-if="userStore.isAdmin"
              prepend-icon="mdi-cog"
              title="管理后台"
              @click="$router.push('/admin')"
            />
            
            <v-divider />
            
            <v-list-item
              prepend-icon="mdi-logout"
              title="退出登录"
              @click="handleLogout"
            />
          </v-list>
        </v-menu>
      </div>
    </v-app-bar>

    <!-- 主内容区域 -->
    <v-main class="user-content">
      <v-container fluid class="content-container">
        <router-view />
      </v-container>
    </v-main>
  </v-app>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useDisplay } from 'vuetify';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { useBorrowStore } from '@/stores/borrow';
import { getRoleDisplayName } from '@/utils/permission';
import type { MenuItem } from '@/types';
import UserAvatar from '@/components/UserAvatar.vue';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const borrowStore = useBorrowStore();
const { mobile } = useDisplay();

// 响应式状态
const drawer = ref(true);
const rail = ref(false);
const hasNotifications = ref(true);
const searchQuery = ref('');

// 计算属性
const drawerWidth = computed(() => rail.value ? 64 : 280);
const pageTitle = computed(() => route.meta.title as string || '图书管理系统');

// 用户菜单项配置
const userMenuItems = computed((): MenuItem[] => {
  return [
    {
      title: '仪表盘',
      icon: 'mdi-view-dashboard',
      to: '/dashboard',
    },
    {
      title: '图书浏览',
      icon: 'mdi-book-open-variant',
      to: '/books',
    },
    {
      title: '我的借阅',
      icon: 'mdi-book-clock',
      to: '/my-borrows',
      badge: borrowStore.hasOverdue ? '!' : undefined,
    },
    {
      title: '我的收藏',
      icon: 'mdi-heart',
      to: '/my-favorites',
    },
    {
      title: '个人信息',
      icon: 'mdi-account',
      to: '/profile',
    },
  ];
});

// 切换侧边栏状态
const toggleRail = () => {
  rail.value = !rail.value;
};

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      path: '/books',
      query: { keyword: searchQuery.value.trim() }
    });
  }
};

// 处理退出登录
const handleLogout = () => {
  userStore.logout();
  router.push('/login');
};

// 组件挂载时的处理
onMounted(async () => {
  // 在移动端默认收起抽屉
  if (mobile.value) {
    drawer.value = false;
  }
  
  // 获取借阅状态
  try {
    await borrowStore.checkOverdueBooks();
  } catch (error) {
    console.error('获取借阅状态失败:', error);
  }
});
</script>

<style scoped>
.user-drawer {
  border-right: 1px solid rgba(0, 0, 0, 0.12) !important;
}

.drawer-header {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 64px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logo-section:hover {
  transform: scale(1.05);
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  white-space: nowrap;
}

.user-info-card {
  margin-bottom: 8px;
}

.navigation-list {
  padding: 8px;
}

.menu-item {
  border-radius: 12px !important;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background: rgba(25, 118, 210, 0.08) !important;
}

.drawer-footer {
  padding: 16px;
  display: flex;
  justify-content: center;
}

.toggle-btn {
  border-radius: 8px !important;
}

.user-app-bar {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%) !important;
  color: white !important;
}

.page-title {
  font-weight: 600;
  font-size: 20px;
}

.search-section {
  margin: 0 16px;
  max-width: 300px;
}

.search-field {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

:deep(.search-field .v-field) {
  color: white;
}

:deep(.search-field .v-field__input) {
  color: white;
}

:deep(.search-field .v-field__input::placeholder) {
  color: rgba(255, 255, 255, 0.7);
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn,
.user-menu-btn {
  color: white !important;
}

.user-menu {
  min-width: 200px;
}

.user-content {
  background-color: #f5f5f5;
}

.content-container {
  padding: 24px;
  max-width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 16px;
  }
  
  .search-section {
    display: none;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .user-content {
    background-color: #121212;
  }
}
</style>
