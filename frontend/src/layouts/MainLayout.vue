<template>
  <v-app>
    <!-- 导航抽屉 -->
    <v-navigation-drawer
      v-model="drawer"
      :rail="rail"
      permanent
      class="main-drawer"
      :width="drawerWidth"
    >
      <!-- 抽屉头部 -->
      <div class="drawer-header">
        <div class="logo-section" @click="toggleRail">
          <v-icon size="32" color="primary">mdi-book-open-page-variant</v-icon>
          <v-fade-transition>
            <span v-show="!rail" class="logo-text gradient-text">图书管理</span>
          </v-fade-transition>
        </div>
      </div>

      <v-divider />

      <!-- 导航菜单 -->
      <v-list nav class="navigation-list">
        <template v-for="item in menuItems" :key="item.title">
          <!-- 有子菜单的项 -->
          <v-list-group v-if="item.children" :value="item.title">
            <template #activator="{ props }">
              <v-list-item
                v-bind="props"
                :prepend-icon="item.icon"
                :title="item.title"
                class="menu-item"
              />
            </template>
            
            <v-list-item
              v-for="child in item.children"
              :key="child.title"
              :to="child.to"
              :prepend-icon="child.icon"
              :title="child.title"
              class="menu-item child-item"
            />
          </v-list-group>

          <!-- 普通菜单项 -->
          <v-list-item
            v-else
            :to="item.to"
            :prepend-icon="item.icon"
            :title="item.title"
            class="menu-item"
          />
        </template>
      </v-list>

      <!-- 抽屉底部 -->
      <template #append>
        <div class="drawer-footer">
          <v-btn
            v-if="!rail"
            variant="text"
            size="small"
            @click="toggleRail"
            class="toggle-btn"
          >
            <v-icon>mdi-chevron-left</v-icon>
            收起
          </v-btn>
          <v-btn
            v-else
            variant="text"
            size="small"
            icon
            @click="toggleRail"
            class="toggle-btn"
          >
            <v-icon>mdi-chevron-right</v-icon>
          </v-btn>
        </div>
      </template>
    </v-navigation-drawer>

    <!-- 应用栏 -->
    <v-app-bar
      :order="-1"
      class="main-app-bar"
      elevation="1"
      height="64"
    >
      <!-- 移动端菜单按钮 -->
      <v-app-bar-nav-icon
        v-if="mobile"
        @click="drawer = !drawer"
        class="d-lg-none"
      />

      <!-- 页面标题 -->
      <v-app-bar-title class="page-title">
        {{ pageTitle }}
      </v-app-bar-title>

      <v-spacer />

      <!-- 用户信息区域 -->
      <div class="user-section">
        <!-- 通知按钮 -->
        <v-btn
          icon
          variant="text"
          class="notification-btn"
        >
          <v-badge
            v-if="hasNotifications"
            color="error"
            content="3"
          >
            <v-icon>mdi-bell</v-icon>
          </v-badge>
          <v-icon v-else>mdi-bell-outline</v-icon>
        </v-btn>

        <!-- 用户菜单 -->
        <v-menu offset-y>
          <template #activator="{ props }">
            <v-btn
              v-bind="props"
              variant="text"
              class="user-menu-btn"
            >
              <v-avatar size="32" class="me-2">
                <v-img
                  v-if="userStore.userInfo?.avatar"
                  :src="userStore.userInfo.avatar"
                  :alt="userStore.userInfo.username"
                />
                <v-icon v-else>mdi-account-circle</v-icon>
              </v-avatar>
              <span class="user-name">{{ userStore.userInfo?.realName || userStore.userInfo?.username }}</span>
              <v-icon>mdi-chevron-down</v-icon>
            </v-btn>
          </template>

          <v-list class="user-menu">
            <v-list-item>
              <v-list-item-title>{{ userStore.userInfo?.username }}</v-list-item-title>
              <v-list-item-subtitle>{{ getRoleDisplayName(userStore.userInfo?.role || '') }}</v-list-item-subtitle>
            </v-list-item>
            
            <v-divider />
            
            <v-list-item
              prepend-icon="mdi-account"
              title="个人信息"
              @click="$router.push('/profile')"
            />
            
            <v-list-item
              v-if="userStore.isAdmin"
              prepend-icon="mdi-cog"
              title="管理后台"
              @click="$router.push('/admin')"
            />
            
            <v-divider />
            
            <v-list-item
              prepend-icon="mdi-logout"
              title="退出登录"
              @click="handleLogout"
            />
          </v-list>
        </v-menu>
      </div>
    </v-app-bar>

    <!-- 主内容区域 -->
    <v-main class="main-content">
      <v-container fluid class="content-container">
        <router-view />
      </v-container>
    </v-main>
  </v-app>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useDisplay } from 'vuetify';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { getRoleDisplayName } from '@/utils/permission';
import type { MenuItem } from '@/types';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const { mobile } = useDisplay();

// 响应式状态
const drawer = ref(true);
const rail = ref(false);
const hasNotifications = ref(true);

// 计算属性
const drawerWidth = computed(() => rail.value ? 64 : 280);
const pageTitle = computed(() => route.meta.title as string || '图书管理系统');

// 菜单项配置
const menuItems = computed((): MenuItem[] => {
  const items: MenuItem[] = [
    {
      title: '仪表盘',
      icon: 'mdi-view-dashboard',
      to: '/dashboard',
    },
    {
      title: '图书浏览',
      icon: 'mdi-book-open-variant',
      to: '/books',
    },
    {
      title: '我的借阅',
      icon: 'mdi-book-clock',
      to: '/my-borrows',
      roles: ['USER', 'ADMIN', 'SUPER_ADMIN'],
    },
    {
      title: '我的收藏',
      icon: 'mdi-heart',
      to: '/my-favorites',
      roles: ['USER', 'ADMIN', 'SUPER_ADMIN'],
    },
  ];

  // 根据用户权限过滤菜单项
  return items.filter(item => {
    if (!item.roles) return true;
    return userStore.hasPermission(item.roles);
  });
});

// 切换侧边栏状态
const toggleRail = () => {
  rail.value = !rail.value;
};

// 处理退出登录
const handleLogout = () => {
  userStore.logout();
  router.push('/login');
};

// 组件挂载时的处理
onMounted(() => {
  // 在移动端默认收起抽屉
  if (mobile.value) {
    drawer.value = false;
  }
});
</script>

<style scoped>
.main-drawer {
  border-right: 1px solid rgba(0, 0, 0, 0.12) !important;
}

.drawer-header {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 64px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logo-section:hover {
  transform: scale(1.05);
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  white-space: nowrap;
}

.navigation-list {
  padding: 8px;
}

.menu-item {
  border-radius: 12px !important;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background: rgba(25, 118, 210, 0.08) !important;
}

.child-item {
  margin-left: 16px;
}

.drawer-footer {
  padding: 16px;
  display: flex;
  justify-content: center;
}

.toggle-btn {
  border-radius: 8px !important;
}

.main-app-bar {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%) !important;
  color: white !important;
}

.page-title {
  font-weight: 600;
  font-size: 20px;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-btn,
.user-menu-btn {
  color: white !important;
}

.user-name {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-menu {
  min-width: 200px;
}

.main-content {
  background-color: #f5f5f5;
}

.content-container {
  padding: 24px;
  max-width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 16px;
  }
  
  .user-name {
    display: none;
  }
}

/* 深色模式支持 */
:deep(.v-theme--dark) {
  .main-content {
    background-color: #121212;
  }
}
</style>
