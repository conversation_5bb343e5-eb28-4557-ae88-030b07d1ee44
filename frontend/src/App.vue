<template>
  <v-app>
    <router-view />

    <!-- 全局消息组件 -->
    <GlobalMessage />
  </v-app>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useUserStore } from '@/stores/user';
import GlobalMessage from '@/components/common/GlobalMessage.vue';
import { errorHandler } from '@/utils/errorHandler';
import { useMessageStore } from '@/stores/message';

const userStore = useUserStore();
const messageStore = useMessageStore();

// 应用初始化
onMounted(async () => {
  // 设置全局错误处理
  errorHandler.addErrorListener((error) => {
    messageStore.error(error.message);
  });

  // 监听未捕获的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    errorHandler.handleError(event.reason);
    event.preventDefault();
  });

  // 监听全局JavaScript错误
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    errorHandler.handleError(event.error);
  });

  // 检查token有效性
  if (userStore.checkTokenValid()) {
    try {
      // 尝试获取当前用户信息
      const result = await userStore.fetchCurrentUser();
      if (result) {
        console.log('应用初始化：用户信息加载成功');
      } else {
        console.log('应用初始化：用户信息加载失败，但不影响应用启动');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 不再调用errorHandler，避免显示错误消息
    }
  } else {
    console.log('应用初始化：没有有效的token');
  }
});
</script>

<style>
/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', sans-serif;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1565c0 0%, #1976d2 100%);
}

/* 渐变文字样式 */
.gradient-text {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 卡片阴影 */
.custom-card {
  border-radius: 16px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* 按钮样式 */
.gradient-btn {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%) !important;
  border-radius: 12px !important;
  text-transform: none !important;
  font-weight: 600 !important;
}

/* 响应式间距 */
.responsive-padding {
  padding: 16px;
}

@media (min-width: 768px) {
  .responsive-padding {
    padding: 24px;
  }
}

@media (min-width: 1200px) {
  .responsive-padding {
    padding: 32px;
  }
}
</style>
