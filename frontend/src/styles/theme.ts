import type { ThemeDefinition } from 'vuetify';

// 蓝色渐变主题配置
export const blueGradientTheme: ThemeDefinition = {
  dark: false,
  colors: {
    // 主色调 - 蓝色渐变
    primary: '#1976d2',
    'primary-lighten-1': '#42a5f5',
    'primary-lighten-2': '#64b5f6',
    'primary-lighten-3': '#90caf9',
    'primary-lighten-4': '#bbdefb',
    'primary-lighten-5': '#e3f2fd',
    'primary-darken-1': '#1565c0',
    'primary-darken-2': '#0d47a1',
    'primary-darken-3': '#0a3d91',
    'primary-darken-4': '#083481',

    // 次要色调
    secondary: '#42a5f5',
    'secondary-lighten-1': '#64b5f6',
    'secondary-lighten-2': '#90caf9',
    'secondary-darken-1': '#1e88e5',
    'secondary-darken-2': '#1976d2',

    // 强调色
    accent: '#82B1FF',
    'accent-lighten-1': '#b3d9ff',
    'accent-darken-1': '#5c8aff',

    // 状态色
    success: '#4CAF50',
    'success-lighten-1': '#81c784',
    'success-darken-1': '#388e3c',

    warning: '#FF9800',
    'warning-lighten-1': '#ffb74d',
    'warning-darken-1': '#f57c00',

    error: '#F44336',
    'error-lighten-1': '#ef5350',
    'error-darken-1': '#d32f2f',

    info: '#2196F3',
    'info-lighten-1': '#64b5f6',
    'info-darken-1': '#1976d2',

    // 中性色
    background: '#fafafa',
    surface: '#ffffff',
    'surface-variant': '#f5f5f5',
    'on-surface': '#1a1a1a',
    'on-surface-variant': '#424242',

    // 文字色
    'on-primary': '#ffffff',
    'on-secondary': '#ffffff',
    'on-success': '#ffffff',
    'on-warning': '#ffffff',
    'on-error': '#ffffff',
    'on-info': '#ffffff',
    'on-background': '#1a1a1a',

    // 边框色
    outline: '#e0e0e0',
    'outline-variant': '#f0f0f0',
  },
};

// 深色蓝色渐变主题配置
export const darkBlueGradientTheme: ThemeDefinition = {
  dark: true,
  colors: {
    // 主色调 - 蓝色渐变（深色模式）
    primary: '#42a5f5',
    'primary-lighten-1': '#64b5f6',
    'primary-lighten-2': '#90caf9',
    'primary-lighten-3': '#bbdefb',
    'primary-lighten-4': '#e3f2fd',
    'primary-darken-1': '#1976d2',
    'primary-darken-2': '#1565c0',
    'primary-darken-3': '#0d47a1',
    'primary-darken-4': '#083481',

    // 次要色调
    secondary: '#64b5f6',
    'secondary-lighten-1': '#90caf9',
    'secondary-lighten-2': '#bbdefb',
    'secondary-darken-1': '#42a5f5',
    'secondary-darken-2': '#1976d2',

    // 强调色
    accent: '#82B1FF',
    'accent-lighten-1': '#b3d9ff',
    'accent-darken-1': '#5c8aff',

    // 状态色
    success: '#66bb6a',
    'success-lighten-1': '#81c784',
    'success-darken-1': '#4caf50',

    warning: '#ffa726',
    'warning-lighten-1': '#ffb74d',
    'warning-darken-1': '#ff9800',

    error: '#ef5350',
    'error-lighten-1': '#f44336',
    'error-darken-1': '#d32f2f',

    info: '#42a5f5',
    'info-lighten-1': '#64b5f6',
    'info-darken-1': '#1976d2',

    // 中性色（深色模式）
    background: '#121212',
    surface: '#1e1e1e',
    'surface-variant': '#2a2a2a',
    'on-surface': '#ffffff',
    'on-surface-variant': '#e0e0e0',

    // 文字色
    'on-primary': '#ffffff',
    'on-secondary': '#ffffff',
    'on-success': '#ffffff',
    'on-warning': '#000000',
    'on-error': '#ffffff',
    'on-info': '#ffffff',
    'on-background': '#ffffff',

    // 边框色
    outline: '#424242',
    'outline-variant': '#2a2a2a',
  },
};

// CSS变量定义
export const cssVariables = {
  // 渐变色
  '--gradient-primary': 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
  '--gradient-primary-reverse': 'linear-gradient(135deg, #42a5f5 0%, #1976d2 100%)',
  '--gradient-secondary': 'linear-gradient(135deg, #42a5f5 0%, #64b5f6 100%)',
  '--gradient-accent': 'linear-gradient(135deg, #82B1FF 0%, #b3d9ff 100%)',
  
  // 阴影
  '--shadow-light': '0 2px 8px rgba(25, 118, 210, 0.1)',
  '--shadow-medium': '0 4px 16px rgba(25, 118, 210, 0.15)',
  '--shadow-heavy': '0 8px 32px rgba(25, 118, 210, 0.2)',
  
  // 圆角
  '--border-radius-small': '8px',
  '--border-radius-medium': '12px',
  '--border-radius-large': '16px',
  '--border-radius-xl': '24px',
  
  // 间距
  '--spacing-xs': '4px',
  '--spacing-sm': '8px',
  '--spacing-md': '16px',
  '--spacing-lg': '24px',
  '--spacing-xl': '32px',
  '--spacing-2xl': '48px',
  
  // 过渡
  '--transition-fast': '0.15s ease',
  '--transition-normal': '0.3s ease',
  '--transition-slow': '0.5s ease',
  
  // 字体
  '--font-weight-light': '300',
  '--font-weight-normal': '400',
  '--font-weight-medium': '500',
  '--font-weight-semibold': '600',
  '--font-weight-bold': '700',
};

// 应用CSS变量到根元素
export const applyCSSVariables = () => {
  const root = document.documentElement;
  Object.entries(cssVariables).forEach(([key, value]) => {
    root.style.setProperty(key, value);
  });
};

// 主题工具函数
export const themeUtils = {
  // 获取渐变背景样式
  getGradientBackground: (type: 'primary' | 'secondary' | 'accent' = 'primary') => {
    const gradients = {
      primary: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
      secondary: 'linear-gradient(135deg, #42a5f5 0%, #64b5f6 100%)',
      accent: 'linear-gradient(135deg, #82B1FF 0%, #b3d9ff 100%)',
    };
    return gradients[type];
  },

  // 获取阴影样式
  getShadow: (level: 'light' | 'medium' | 'heavy' = 'medium') => {
    const shadows = {
      light: '0 2px 8px rgba(25, 118, 210, 0.1)',
      medium: '0 4px 16px rgba(25, 118, 210, 0.15)',
      heavy: '0 8px 32px rgba(25, 118, 210, 0.2)',
    };
    return shadows[level];
  },

  // 获取圆角样式
  getBorderRadius: (size: 'small' | 'medium' | 'large' | 'xl' = 'medium') => {
    const radii = {
      small: '8px',
      medium: '12px',
      large: '16px',
      xl: '24px',
    };
    return radii[size];
  },

  // 获取间距
  getSpacing: (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' = 'md') => {
    const spacings = {
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px',
      '2xl': '48px',
    };
    return spacings[size];
  },
};

// 导出默认主题配置
export const defaultThemes = {
  light: blueGradientTheme,
  dark: darkBlueGradientTheme,
};
