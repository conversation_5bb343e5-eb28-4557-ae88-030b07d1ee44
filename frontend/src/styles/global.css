/* 全局样式 - 图书管理系统 */

/* CSS变量定义 */
:root {
  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  --gradient-primary-reverse: linear-gradient(135deg, #42a5f5 0%, #1976d2 100%);
  --gradient-secondary: linear-gradient(135deg, #42a5f5 0%, #64b5f6 100%);
  --gradient-accent: linear-gradient(135deg, #82B1FF 0%, #b3d9ff 100%);
  
  /* 阴影 */
  --shadow-light: 0 2px 8px rgba(25, 118, 210, 0.1);
  --shadow-medium: 0 4px 16px rgba(25, 118, 210, 0.15);
  --shadow-heavy: 0 8px 32px rgba(25, 118, 210, 0.2);
  
  /* 圆角 */
  --border-radius-small: 8px;
  --border-radius-medium: 12px;
  --border-radius-large: 16px;
  --border-radius-xl: 24px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  /* 过渡 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* 字体 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: var(--border-radius-small);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-small);
  transition: background var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-primary-reverse);
}

/* 深色模式滚动条 */
.v-theme--dark ::-webkit-scrollbar-track {
  background: #2a2a2a;
}

.v-theme--dark ::-webkit-scrollbar-thumb {
  background: var(--gradient-secondary);
}

/* 工具类 */

/* 渐变文字 */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-weight-semibold);
}

.gradient-text-reverse {
  background: var(--gradient-primary-reverse);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-weight-semibold);
}

/* 渐变背景 */
.gradient-bg {
  background: var(--gradient-primary);
}

.gradient-bg-reverse {
  background: var(--gradient-primary-reverse);
}

.gradient-bg-secondary {
  background: var(--gradient-secondary);
}

.gradient-bg-accent {
  background: var(--gradient-accent);
}

/* 卡片样式 */
.custom-card {
  border-radius: var(--border-radius-large) !important;
  box-shadow: var(--shadow-medium) !important;
  transition: all var(--transition-normal);
}

.custom-card:hover {
  box-shadow: var(--shadow-heavy) !important;
  transform: translateY(-2px);
}

.custom-card-flat {
  border-radius: var(--border-radius-medium) !important;
  box-shadow: var(--shadow-light) !important;
}

/* 按钮样式 */
.gradient-btn {
  background: var(--gradient-primary) !important;
  border-radius: var(--border-radius-medium) !important;
  text-transform: none !important;
  font-weight: var(--font-weight-semibold) !important;
  box-shadow: var(--shadow-light) !important;
  transition: all var(--transition-normal);
}

.gradient-btn:hover {
  background: var(--gradient-primary-reverse) !important;
  box-shadow: var(--shadow-medium) !important;
  transform: translateY(-1px);
}

.gradient-btn-secondary {
  background: var(--gradient-secondary) !important;
  border-radius: var(--border-radius-medium) !important;
  text-transform: none !important;
  font-weight: var(--font-weight-semibold) !important;
}

/* 输入框样式 */
.custom-input .v-field {
  border-radius: var(--border-radius-medium) !important;
  transition: all var(--transition-normal);
}

.custom-input .v-field--focused {
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
}

/* 响应式间距 */
.responsive-padding {
  padding: var(--spacing-md);
}

.responsive-margin {
  margin: var(--spacing-md);
}

@media (min-width: 768px) {
  .responsive-padding {
    padding: var(--spacing-lg);
  }
  
  .responsive-margin {
    margin: var(--spacing-lg);
  }
}

@media (min-width: 1200px) {
  .responsive-padding {
    padding: var(--spacing-xl);
  }
  
  .responsive-margin {
    margin: var(--spacing-xl);
  }
}

/* 动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all var(--transition-normal);
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.scale-enter-active,
.scale-leave-active {
  transition: all var(--transition-normal);
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: inherit;
}

.v-theme--dark .loading-overlay {
  background: rgba(0, 0, 0, 0.8);
}

/* 文字省略 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 图片样式 */
.custom-image {
  border-radius: var(--border-radius-medium);
  transition: all var(--transition-normal);
}

.custom-image:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-medium);
}

/* 分隔线 */
.custom-divider {
  background: linear-gradient(90deg, transparent, rgba(25, 118, 210, 0.3), transparent);
  height: 1px;
  border: none;
  margin: var(--spacing-lg) 0;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--spacing-sm);
}

.status-indicator.success {
  background: #4CAF50;
}

.status-indicator.warning {
  background: #FF9800;
}

.status-indicator.error {
  background: #F44336;
}

.status-indicator.info {
  background: #2196F3;
}

/* 徽章样式 */
.custom-badge {
  background: var(--gradient-primary);
  color: white;
  padding: 2px 8px;
  border-radius: var(--border-radius-large);
  font-size: 12px;
  font-weight: var(--font-weight-medium);
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  * {
    color: black !important;
    background: white !important;
  }
}
