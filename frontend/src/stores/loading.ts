import { defineStore } from 'pinia';
import { ref } from 'vue';

export interface LoadingOptions {
  message?: string;
  progress?: number | null;
  timeout?: number;
}

export const useLoadingStore = defineStore('loading', () => {
  // 状态
  const show = ref(false);
  const message = ref('加载中...');
  const progress = ref<number | null>(null);
  const timeoutId = ref<number | null>(null);

  // 显示加载
  const showLoading = (options: LoadingOptions = {}) => {
    message.value = options.message ?? '加载中...';
    progress.value = options.progress ?? null;
    show.value = true;

    // 设置超时自动隐藏
    if (options.timeout) {
      if (timeoutId.value) {
        clearTimeout(timeoutId.value);
      }
      timeoutId.value = window.setTimeout(() => {
        hide();
      }, options.timeout);
    }
  };

  // 隐藏加载
  const hide = () => {
    show.value = false;
    
    // 清理超时
    if (timeoutId.value) {
      clearTimeout(timeoutId.value);
      timeoutId.value = null;
    }

    // 清理状态
    setTimeout(() => {
      message.value = '加载中...';
      progress.value = null;
    }, 300);
  };

  // 更新进度
  const updateProgress = (value: number, newMessage?: string) => {
    progress.value = Math.max(0, Math.min(100, value));
    if (newMessage) {
      message.value = newMessage;
    }
  };

  // 便捷方法
  const showWithMessage = (msg: string, timeout?: number) => {
    showLoading({ message: msg, timeout });
  };

  const showWithProgress = (msg: string, initialProgress: number = 0) => {
    showLoading({ message: msg, progress: initialProgress });
  };

  return {
    // 状态
    show,
    message,
    progress,
    
    // 方法
    showLoading,
    hide,
    updateProgress,
    showWithMessage,
    showWithProgress,
  };
});
