import { defineStore } from 'pinia';
import { ref } from 'vue';

export type ConfirmType = 'success' | 'error' | 'warning' | 'info' | 'question';

export interface ConfirmOptions {
  title?: string;
  message: string;
  type?: ConfirmType;
  confirmText?: string;
  cancelText?: string;
  details?: string;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
}

export const useConfirmStore = defineStore('confirm', () => {
  // 状态
  const show = ref(false);
  const title = ref('确认操作');
  const message = ref('');
  const type = ref<ConfirmType>('question');
  const confirmText = ref('确认');
  const cancelText = ref('取消');
  const details = ref<string | null>(null);
  const loading = ref(false);
  const onConfirm = ref<(() => void | Promise<void>) | null>(null);
  const onCancel = ref<(() => void) | null>(null);

  // 显示确认对话框
  const confirm = (options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      title.value = options.title ?? '确认操作';
      message.value = options.message;
      type.value = options.type ?? 'question';
      confirmText.value = options.confirmText ?? '确认';
      cancelText.value = options.cancelText ?? '取消';
      details.value = options.details ?? null;
      loading.value = false;

      // 设置回调
      onConfirm.value = async () => {
        if (options.onConfirm) {
          await options.onConfirm();
        }
        resolve(true);
      };

      onCancel.value = () => {
        if (options.onCancel) {
          options.onCancel();
        }
        resolve(false);
      };

      show.value = true;
    });
  };

  // 隐藏确认对话框
  const hide = () => {
    show.value = false;
    loading.value = false;
    // 清理状态
    setTimeout(() => {
      title.value = '确认操作';
      message.value = '';
      type.value = 'question';
      confirmText.value = '确认';
      cancelText.value = '取消';
      details.value = null;
      onConfirm.value = null;
      onCancel.value = null;
    }, 300);
  };

  // 便捷方法
  const confirmDelete = (
    itemName: string,
    onConfirmDelete: () => void | Promise<void>
  ): Promise<boolean> => {
    return confirm({
      title: '确认删除',
      message: `确定要删除 ${itemName} 吗？此操作不可撤销。`,
      type: 'error',
      confirmText: '删除',
      cancelText: '取消',
      onConfirm: onConfirmDelete,
    });
  };

  const confirmAction = (
    actionName: string,
    description: string,
    onConfirmAction: () => void | Promise<void>
  ): Promise<boolean> => {
    return confirm({
      title: `确认${actionName}`,
      message: description,
      type: 'warning',
      confirmText: actionName,
      cancelText: '取消',
      onConfirm: onConfirmAction,
    });
  };

  const confirmInfo = (
    title: string,
    message: string,
    details?: string
  ): Promise<boolean> => {
    return confirm({
      title,
      message,
      type: 'info',
      confirmText: '确定',
      cancelText: '取消',
      details,
    });
  };

  return {
    // 状态
    show,
    title,
    message,
    type,
    confirmText,
    cancelText,
    details,
    loading,
    onConfirm,
    onCancel,
    
    // 方法
    confirm,
    hide,
    confirmDelete,
    confirmAction,
    confirmInfo,
  };
});
