import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { User, LoginForm, RegisterForm } from '@/types';
import { login, register } from '@/api/auth';
import { profileApi } from '@/api/profile';
import { getToken, setToken as saveToken, removeToken, isTokenValid, parseJWT } from '@/utils/token';

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(getToken() || '');
  const userInfo = ref<User | null>(null);
  const isLoading = ref(false);
  const isInitialized = ref(false); // 添加初始化状态标记

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value && isTokenValid());
  const userRole = computed(() => userInfo.value?.role || '');
  const isAdmin = computed(() => ['ADMIN', 'SUPER_ADMIN'].includes(userRole.value));
  const isSuperAdmin = computed(() => userRole.value === 'SUPER_ADMIN');

  // 头像URL处理
  const avatarUrl = computed(() => {
    if (!userInfo.value?.avatar) return '';

    const avatar = userInfo.value.avatar;

    // 如果已经是完整URL，直接返回
    if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
      return avatar;
    }

    // 如果是相对路径，添加基础URL
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

    // 如果路径以/开头，直接拼接（后端返回的格式：/api/files/avatars/filename）
    if (avatar.startsWith('/')) {
      return `${baseUrl}${avatar}`;
    }

    // 如果只是文件名，添加完整路径前缀
    return `${baseUrl}/api/files/avatars/${avatar}`;
  });

  // 默认头像URL
  const defaultAvatarUrl = computed(() => {
    const name = userInfo.value?.realName || userInfo.value?.username || 'User';
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&size=200&background=1976d2&color=ffffff&bold=true`;
  });

  // 显示用的头像URL（优先使用用户头像，否则使用默认头像）
  const displayAvatarUrl = computed(() => {
    return avatarUrl.value || defaultAvatarUrl.value;
  });

  // 设置token
  const setToken = (newToken: string) => {
    token.value = newToken;
    // 调用token工具类的setToken，会自动解析并保存过期时间
    saveToken(newToken);

    // 确保token_expire字段被正确设置
    const payload = parseJWT(newToken);
    if (payload && payload.exp) {
      const expireTime = payload.exp * 1000; // 转换为毫秒
      localStorage.setItem('token_expire', expireTime.toString());
      console.log('Token过期时间已设置:', new Date(expireTime).toLocaleString());
    }
  };

  // 设置用户信息
  const setUserInfo = (user: User) => {
    userInfo.value = user;
  };

  // 清除用户信息
  const clearUserInfo = () => {
    token.value = '';
    userInfo.value = null;
    removeToken();
    // 注意：不在这里重置初始化状态，由调用方决定
  };

  // 登录
  const loginAction = async (loginForm: LoginForm) => {
    try {
      isLoading.value = true;
      const response = await login(loginForm);

      // 添加调试日志
      console.log('登录响应:', response);

      // 检查响应数据结构
      if (!response || !response.data) {
        throw new Error('登录响应数据格式错误');
      }

      const responseData = response.data;

      // 根据实际的响应格式处理数据
      let newToken: string;
      let user: User;

      if (responseData.token && responseData.user) {
        // 标准格式：{ token, user }
        newToken = responseData.token;
        user = responseData.user;
      } else if (responseData.data && responseData.data.token && responseData.data.user) {
        // 嵌套格式：{ data: { token, user } }
        newToken = responseData.data.token;
        user = responseData.data.user;
      } else {
        console.error('无法解析登录响应数据:', responseData);
        throw new Error('登录响应数据格式不正确');
      }

      setToken(newToken);
      setUserInfo(user);

      // 登录成功后标记为已初始化
      isInitialized.value = true;
      console.log('登录成功，认证状态已初始化');

      return response;
    } catch (error) {
      console.error('登录处理错误:', error);
      clearUserInfo();
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 注册
  const registerAction = async (registerForm: RegisterForm) => {
    try {
      isLoading.value = true;
      const response = await register(registerForm);
      return response;
    } catch (error) {
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 初始化用户状态（从本地存储恢复）
  const initializeAuth = async () => {
    try {
      if (isInitialized.value) {
        console.log('认证状态已初始化，跳过');
        return;
      }

      console.log('开始初始化认证状态...');

      if (!token.value) {
        console.log('没有token，初始化完成');
        return;
      }

      // 检查token是否有效
      if (!isTokenValid()) {
        console.log('Token已过期，清除本地数据');
        clearUserInfo();
        isInitialized.value = true; // 标记初始化完成
        return;
      }

      // 尝试获取用户信息
      console.log('Token有效，开始获取用户信息...');
      await fetchCurrentUser();
      console.log('认证状态初始化成功');
    } catch (error: any) {
      console.error('认证状态初始化失败:', error);
      // 初始化失败时清除无效数据
      if (error?.response?.status === 401) {
        console.log('401错误，清除认证数据');
        clearUserInfo();
        // 401错误时已清除数据，标记初始化完成
      } else {
        console.log('其他错误，保留token但标记初始化完成');
      }
    } finally {
      // 无论成功还是失败，都标记为已初始化
      isInitialized.value = true;
      console.log('认证状态初始化流程完成');
    }
  };

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    try {
      if (!token.value) {
        console.log('没有token，跳过获取用户信息');
        return null;
      }

      // 检查token是否有效
      if (!isTokenValid()) {
        console.log('Token已过期，清除本地数据');
        clearUserInfo();
        isInitialized.value = true; // 标记初始化完成
        return null;
      }

      console.log('开始获取用户信息...');
      const response = await profileApi.getCurrentUserProfile();

      // 转换个人信息为用户信息格式
      const userInfo: User = {
        id: response.data.id,
        username: response.data.username,
        realName: response.data.realName || '',
        email: response.data.email,
        phone: response.data.phone || '',
        avatar: response.data.avatar || '',
        role: response.data.role,
        status: response.data.status,
        createdTime: response.data.createdTime,
        updatedTime: response.data.createdTime, // 使用createdTime作为默认值
        lastLoginTime: response.data.lastLoginTime || '',
      };

      setUserInfo(userInfo);
      console.log('成功获取用户信息:', userInfo);
      return response;
    } catch (error: any) {
      console.log('获取用户信息失败:', error);

      // 如果是401错误，说明token无效
      if (error?.response?.status === 401) {
        console.log('Token无效，清除本地数据');
        clearUserInfo();
        isInitialized.value = true; // 标记初始化完成
      }

      // 抛出错误，让调用方处理
      throw error;
    }
  };

  // 退出登录
  const logout = () => {
    console.log('开始退出登录...');
    clearUserInfo();
    // 退出登录后立即标记为已初始化，避免路由守卫等待
    isInitialized.value = true;
    console.log('退出登录完成，认证状态已重置');
  };

  // 检查权限
  const hasRole = (roles: string | string[]) => {
    if (!userInfo.value) return false;
    
    const userRole = userInfo.value.role;
    if (Array.isArray(roles)) {
      return roles.includes(userRole);
    }
    return userRole === roles;
  };

  // 检查是否有权限访问
  const hasPermission = (requiredRoles?: string[]) => {
    if (!requiredRoles || requiredRoles.length === 0) return true;
    return hasRole(requiredRoles);
  };

  // 检查token是否有效
  const checkTokenValid = () => {
    if (!token.value || !isTokenValid()) {
      clearUserInfo();
      isInitialized.value = true; // 标记初始化完成
      return false;
    }
    return true;
  };

  // 等待认证状态初始化完成
  const waitForInitialization = async (timeout = 10000) => {
    if (isInitialized.value) {
      console.log('认证状态已初始化，无需等待');
      return;
    }

    console.log('开始等待认证状态初始化完成...');

    return new Promise<void>((resolve, reject) => {
      const startTime = Date.now();

      const timer = setTimeout(() => {
        console.error(`认证状态初始化超时 (${timeout}ms)`);
        reject(new Error(`认证状态初始化超时 (${timeout}ms)`));
      }, timeout);

      const checkInitialized = () => {
        if (isInitialized.value) {
          clearTimeout(timer);
          const elapsed = Date.now() - startTime;
          console.log(`认证状态初始化完成，耗时: ${elapsed}ms`);
          resolve();
        } else {
          // 每100ms检查一次，并输出进度
          const elapsed = Date.now() - startTime;
          if (elapsed % 1000 < 100) { // 每秒输出一次进度
            console.log(`等待认证状态初始化... (${elapsed}ms)`);
          }
          setTimeout(checkInitialized, 100);
        }
      };

      checkInitialized();
    });
  };

  return {
    // 状态
    token,
    userInfo,
    isLoading,
    isInitialized,

    // 计算属性
    isLoggedIn,
    userRole,
    isAdmin,
    isSuperAdmin,
    avatarUrl,
    defaultAvatarUrl,
    displayAvatarUrl,

    // 方法
    setToken,
    setUserInfo,
    clearUserInfo,
    loginAction,
    registerAction,
    initializeAuth,
    fetchCurrentUser,
    logout,
    hasRole,
    hasPermission,
    checkTokenValid,
    waitForInitialization,
  };
});
