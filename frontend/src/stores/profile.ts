import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { 
  UserProfile, 
  UserProfileUpdateForm, 
  ChangePasswordForm, 
  BorrowStats 
} from '@/api/profile';
import { profileApi } from '@/api/profile';

export const useProfileStore = defineStore('profile', () => {
  // 状态
  const profile = ref<UserProfile | null>(null);
  const isLoading = ref(false);
  const isUpdating = ref(false);
  const isUploadingAvatar = ref(false);
  const isChangingPassword = ref(false);

  // 计算属性
  const hasProfile = computed(() => profile.value !== null);
  const userRole = computed(() => profile.value?.role || '');
  const isAdmin = computed(() => ['ADMIN', 'SUPER_ADMIN'].includes(userRole.value));
  const avatarUrl = computed(() => {
    if (!profile.value?.avatar) return '';

    const avatar = profile.value.avatar;

    // 如果已经是完整URL，直接返回
    if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
      return avatar;
    }

    // 获取基础URL，确保不包含/api后缀
    let baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

    // 如果baseUrl以/api结尾，移除它
    if (baseUrl.endsWith('/api')) {
      baseUrl = baseUrl.slice(0, -4);
    }

    // 如果路径以/开头，直接拼接（后端返回的格式：/api/files/avatars/filename）
    if (avatar.startsWith('/')) {
      return `${baseUrl}${avatar}`;
    }

    // 如果只是文件名，添加完整路径前缀
    return `${baseUrl}/api/files/avatars/${avatar}`;
  });

  const defaultAvatarUrl = computed(() => {
    const name = profile.value?.realName || profile.value?.username || 'User';
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&size=200&background=1976d2&color=ffffff&bold=true`;
  });

  const displayAvatarUrl = computed(() => {
    return avatarUrl.value || defaultAvatarUrl.value;
  });

  // 获取当前用户个人信息
  const fetchProfile = async () => {
    try {
      isLoading.value = true;
      const response = await profileApi.getCurrentUserProfile();
      profile.value = response.data;
      return response;
    } catch (error) {
      console.error('获取个人信息失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 更新个人信息
  const updateProfile = async (updateData: UserProfileUpdateForm) => {
    try {
      isUpdating.value = true;
      const response = await profileApi.updateProfile(updateData);
      profile.value = response.data;
      return response;
    } catch (error) {
      console.error('更新个人信息失败:', error);
      throw error;
    } finally {
      isUpdating.value = false;
    }
  };

  // 修改密码
  const changePassword = async (passwordData: ChangePasswordForm) => {
    try {
      isChangingPassword.value = true;
      const response = await profileApi.changePassword(passwordData);
      return response;
    } catch (error) {
      console.error('修改密码失败:', error);
      throw error;
    } finally {
      isChangingPassword.value = false;
    }
  };

  // 上传头像
  const uploadAvatar = async (file: File) => {
    try {
      isUploadingAvatar.value = true;
      const response = await profileApi.uploadAvatar(file);
      
      // 更新本地头像URL
      if (profile.value) {
        profile.value.avatar = response.data.url;
      }
      
      return response;
    } catch (error) {
      console.error('上传头像失败:', error);
      throw error;
    } finally {
      isUploadingAvatar.value = false;
    }
  };

  // 刷新统计信息
  const refreshStats = async () => {
    try {
      const response = await profileApi.getUserStats();
      if (profile.value) {
        profile.value.borrowStats = response.data;
      }
      return response;
    } catch (error) {
      console.error('刷新统计信息失败:', error);
      throw error;
    }
  };

  // 重置状态
  const resetProfile = () => {
    profile.value = null;
    isLoading.value = false;
    isUpdating.value = false;
    isUploadingAvatar.value = false;
    isChangingPassword.value = false;
  };

  // 更新头像URL（用于头像上传后的即时更新）
  const updateAvatarUrl = (url: string) => {
    if (profile.value) {
      profile.value.avatar = url;
    }
  };

  // 验证文件类型和大小
  const validateAvatarFile = (file: File): { valid: boolean; message?: string } => {
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        message: '只支持 JPG、PNG、GIF 格式的图片文件'
      };
    }

    // 检查文件大小 (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        message: '图片文件大小不能超过 5MB'
      };
    }

    return { valid: true };
  };

  // 格式化用户角色显示
  const formatRole = (role: string): string => {
    const roleMap: Record<string, string> = {
      'USER': '普通用户',
      'ADMIN': '管理员',
      'SUPER_ADMIN': '超级管理员'
    };
    return roleMap[role] || role;
  };

  // 格式化账户状态
  const formatStatus = (status: number): { text: string; color: string } => {
    switch (status) {
      case 1:
        return { text: '正常', color: 'success' };
      case 0:
        return { text: '禁用', color: 'error' };
      default:
        return { text: '未知', color: 'warning' };
    }
  };

  return {
    // 状态
    profile,
    isLoading,
    isUpdating,
    isUploadingAvatar,
    isChangingPassword,

    // 计算属性
    hasProfile,
    userRole,
    isAdmin,
    avatarUrl,
    defaultAvatarUrl,
    displayAvatarUrl,

    // 方法
    fetchProfile,
    updateProfile,
    changePassword,
    uploadAvatar,
    refreshStats,
    resetProfile,
    updateAvatarUrl,
    validateAvatarFile,
    formatRole,
    formatStatus,
  };
});
