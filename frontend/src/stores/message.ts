import { defineStore } from 'pinia';
import { ref } from 'vue';

export type MessageType = 'success' | 'error' | 'warning' | 'info';
export type MessagePosition = 'top' | 'bottom' | 'left' | 'right' | 'center';

export interface MessageAction {
  text: string;
  handler: () => void;
}

export const useMessageStore = defineStore('message', () => {
  // 状态
  const show = ref(false);
  const message = ref('');
  const type = ref<MessageType>('info');
  const timeout = ref(3000);
  const position = ref<MessagePosition>('top');
  const multiLine = ref(false);
  const vertical = ref(false);
  const action = ref<MessageAction | null>(null);

  // 显示消息
  const showMessage = (
    text: string,
    messageType: MessageType = 'info',
    options: {
      timeout?: number;
      position?: MessagePosition;
      multiLine?: boolean;
      vertical?: boolean;
      action?: MessageAction;
    } = {}
  ) => {
    message.value = text;
    type.value = messageType;
    timeout.value = options.timeout ?? 3000;
    position.value = options.position ?? 'top';
    multiLine.value = options.multiLine ?? false;
    vertical.value = options.vertical ?? false;
    action.value = options.action ?? null;
    show.value = true;
  };

  // 隐藏消息
  const hide = () => {
    show.value = false;
    // 清理状态
    setTimeout(() => {
      message.value = '';
      action.value = null;
    }, 300);
  };

  // 便捷方法
  const success = (text: string, options?: Parameters<typeof showMessage>[2]) => {
    showMessage(text, 'success', options);
  };

  const error = (text: string, options?: Parameters<typeof showMessage>[2]) => {
    showMessage(text, 'error', { timeout: 5000, ...options });
  };

  const warning = (text: string, options?: Parameters<typeof showMessage>[2]) => {
    showMessage(text, 'warning', { timeout: 4000, ...options });
  };

  const info = (text: string, options?: Parameters<typeof showMessage>[2]) => {
    showMessage(text, 'info', options);
  };

  return {
    // 状态
    show,
    message,
    type,
    timeout,
    position,
    multiLine,
    vertical,
    action,
    
    // 方法
    showMessage,
    hide,
    success,
    error,
    warning,
    info,
  };
});
