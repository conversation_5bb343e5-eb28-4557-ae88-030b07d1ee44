import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { UserFavorite, FavoriteForm, PageResult, QueryParams } from '@/types';
import { 
  addFavorite, 
  removeFavorite, 
  toggleFavorite, 
  checkFavorite, 
  getFavoriteList,
  getUserFavoriteStats,
  batchCheckFavoriteStatus
} from '@/api/favorite';

export const useFavoriteStore = defineStore('favorite', () => {
  // 状态
  const favorites = ref<UserFavorite[]>([]);
  const favoriteBookIds = ref<Set<number>>(new Set());
  const pagination = ref({
    current: 1,
    size: 12,
    total: 0,
    pages: 0,
  });
  const isLoading = ref(false);
  const stats = ref({
    totalCount: 0,
  });

  // 计算属性
  const hasFavorites = computed(() => favorites.value.length > 0);
  const totalFavorites = computed(() => stats.value.totalCount);

  // 检查图书是否已收藏
  const isFavorited = (bookId: number): boolean => {
    return favoriteBookIds.value.has(bookId);
  };

  // 获取用户收藏列表
  const fetchFavoriteList = async (params?: Partial<QueryParams>) => {
    try {
      isLoading.value = true;
      const queryParams = {
        current: pagination.value.current,
        size: pagination.value.size,
        ...params,
      };

      const response = await getFavoriteList(queryParams);
      
      favorites.value = response.data.records;
      pagination.value = {
        current: response.data.current,
        size: response.data.size,
        total: response.data.total,
        pages: response.data.pages,
      };

      // 更新收藏图书ID集合
      const bookIds = favorites.value.map(fav => fav.bookId);
      favoriteBookIds.value = new Set(bookIds);

      return response;
    } catch (error) {
      console.error('获取收藏列表失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 添加收藏
  const addFavoriteAction = async (bookId: number) => {
    try {
      const data: FavoriteForm = { bookId };
      const response = await addFavorite(data);
      
      // 更新本地状态
      favoriteBookIds.value.add(bookId);
      stats.value.totalCount += 1;
      
      return response;
    } catch (error) {
      console.error('添加收藏失败:', error);
      throw error;
    }
  };

  // 取消收藏
  const removeFavoriteAction = async (bookId: number) => {
    try {
      const data: FavoriteForm = { bookId };
      const response = await removeFavorite(data);
      
      // 更新本地状态
      favoriteBookIds.value.delete(bookId);
      stats.value.totalCount = Math.max(0, stats.value.totalCount - 1);
      
      // 如果当前在收藏列表页面，移除该项
      favorites.value = favorites.value.filter(fav => fav.bookId !== bookId);
      
      return response;
    } catch (error) {
      console.error('取消收藏失败:', error);
      throw error;
    }
  };

  // 切换收藏状态
  const toggleFavoriteAction = async (bookId: number) => {
    try {
      const data: FavoriteForm = { bookId };
      const response = await toggleFavorite(data);
      
      // 更新本地状态
      if (response.data.isFavorited) {
        favoriteBookIds.value.add(bookId);
        stats.value.totalCount += 1;
      } else {
        favoriteBookIds.value.delete(bookId);
        stats.value.totalCount = Math.max(0, stats.value.totalCount - 1);
        // 如果当前在收藏列表页面，移除该项
        favorites.value = favorites.value.filter(fav => fav.bookId !== bookId);
      }
      
      return response;
    } catch (error) {
      console.error('切换收藏状态失败:', error);
      throw error;
    }
  };

  // 检查单个图书收藏状态
  const checkFavoriteStatus = async (bookId: number) => {
    try {
      const response = await checkFavorite(bookId);
      
      // 更新本地状态
      if (response.data) {
        favoriteBookIds.value.add(bookId);
      } else {
        favoriteBookIds.value.delete(bookId);
      }
      
      return response.data;
    } catch (error) {
      console.error('检查收藏状态失败:', error);
      return false;
    }
  };

  // 批量检查图书收藏状态
  const batchCheckFavoriteStatusAction = async (bookIds: number[]) => {
    try {
      if (!bookIds.length) return {};
      
      const response = await batchCheckFavoriteStatus(bookIds);
      
      // 更新本地状态
      Object.entries(response.data).forEach(([bookId, isFavorited]) => {
        const id = Number(bookId);
        if (isFavorited) {
          favoriteBookIds.value.add(id);
        } else {
          favoriteBookIds.value.delete(id);
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('批量检查收藏状态失败:', error);
      return {};
    }
  };

  // 获取用户收藏统计信息
  const fetchFavoriteStats = async () => {
    try {
      const response = await getUserFavoriteStats();
      stats.value = response.data;
      return response;
    } catch (error) {
      console.error('获取收藏统计失败:', error);
      throw error;
    }
  };

  // 重置状态
  const resetState = () => {
    favorites.value = [];
    favoriteBookIds.value.clear();
    pagination.value = {
      current: 1,
      size: 12,
      total: 0,
      pages: 0,
    };
    stats.value = {
      totalCount: 0,
    };
  };

  // 设置分页参数
  const setPagination = (params: Partial<typeof pagination.value>) => {
    Object.assign(pagination.value, params);
  };

  return {
    // 状态
    favorites,
    favoriteBookIds,
    pagination,
    isLoading,
    stats,
    
    // 计算属性
    hasFavorites,
    totalFavorites,
    
    // 方法
    isFavorited,
    fetchFavoriteList,
    addFavoriteAction,
    removeFavoriteAction,
    toggleFavoriteAction,
    checkFavoriteStatus,
    batchCheckFavoriteStatusAction,
    fetchFavoriteStats,
    resetState,
    setPagination,
  };
});
