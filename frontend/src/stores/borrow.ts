import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { BorrowRecord, BorrowForm, PageResult, BorrowQueryParams, BorrowRequest } from '@/types';
import { borrowApi, adminBorrowApi } from '@/api/borrow';

export const useBorrowStore = defineStore('borrow', () => {
  // 状态
  const borrowRecords = ref<BorrowRecord[]>([]);
  const myBorrowRecords = ref<BorrowRecord[]>([]);
  const currentBorrowRecord = ref<BorrowRecord | null>(null);
  const currentBorrowCount = ref(0);
  const hasOverdue = ref(false);
  const pagination = ref({
    current: 1,
    size: 10,
    total: 0,
    pages: 0,
  });
  const isLoading = ref(false);
  const searchParams = ref<BorrowQueryParams>({
    current: 1,
    size: 10,
    keyword: '',
  });

  // 借书
  const borrowBookAction = async (data: BorrowRequest) => {
    try {
      isLoading.value = true;
      const response = await borrowApi.borrowBook(data);
      // 借书成功后刷新相关数据
      await Promise.all([
        fetchBorrowList(),
        fetchCurrentBorrowCount(),
      ]);
      return response;
    } catch (error) {
      console.error('借书失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 归还图书
  const returnBookAction = async (id: number) => {
    try {
      isLoading.value = true;
      const response = await borrowApi.returnBook(id);
      // 归还成功后刷新相关数据
      await Promise.all([
        fetchBorrowList(),
        fetchCurrentBorrowCount(),
        checkOverdueBooks(),
      ]);
      return response;
    } catch (error) {
      console.error('归还图书失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 续借图书
  const renewBookAction = async (id: number) => {
    try {
      isLoading.value = true;
      const response = await borrowApi.renewBook(id);
      // 续借成功后刷新列表
      await fetchBorrowList();
      return response;
    } catch (error) {
      console.error('续借图书失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 获取借阅记录列表（管理员）
  const fetchBorrowList = async (params?: Partial<BorrowQueryParams>) => {
    try {
      isLoading.value = true;

      if (params) {
        Object.assign(searchParams.value, params);
      }

      const response = await adminBorrowApi.getBorrowList(searchParams.value);
      const { records, current, size, total, pages } = response.data;

      borrowRecords.value = records;
      pagination.value = { current, size, total, pages };

      return response;
    } catch (error) {
      console.error('获取借阅记录失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 获取我的借阅记录
  const fetchMyBorrowList = async (params?: Partial<BorrowQueryParams>) => {
    try {
      isLoading.value = true;

      const queryParams = {
        current: params?.current || 1,
        size: params?.size || 10,
        keyword: params?.keyword,
        status: params?.status,
      };

      const response = await borrowApi.getMyBorrowList(queryParams);
      const { records, current, size, total, pages } = response.data;

      myBorrowRecords.value = records;
      pagination.value = { current, size, total, pages };

      return response;
    } catch (error) {
      console.error('获取我的借阅记录失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 获取借阅记录详情
  const fetchBorrowById = async (id: number) => {
    try {
      isLoading.value = true;
      const response = await borrowApi.getBorrowById(id);
      currentBorrowRecord.value = response.data;
      return response;
    } catch (error) {
      console.error('获取借阅记录详情失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 获取当前借阅数量
  const fetchCurrentBorrowCount = async () => {
    try {
      const response = await borrowApi.getCurrentBorrowCount();
      currentBorrowCount.value = response.data;
      return response;
    } catch (error) {
      console.error('获取当前借阅数量失败:', error);
      throw error;
    }
  };

  // 检查是否有逾期图书
  const checkOverdueBooks = async () => {
    try {
      const response = await borrowApi.hasOverdueBooks();
      hasOverdue.value = response.data;
      return response;
    } catch (error) {
      console.error('检查逾期图书失败:', error);
      throw error;
    }
  };

  // 处理逾期图书（管理员功能）
  const processOverdueBooksAction = async () => {
    try {
      isLoading.value = true;
      const response = await adminBorrowApi.processOverdue();
      // 处理完成后刷新列表
      await fetchBorrowList();
      return response;
    } catch (error) {
      console.error('处理逾期图书失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 重置搜索参数
  const resetSearchParams = () => {
    searchParams.value = {
      current: 1,
      size: 10,
      keyword: '',
    };
  };

  // 设置搜索参数
  const setSearchParams = (params: Partial<typeof searchParams.value>) => {
    Object.assign(searchParams.value, params);
  };

  return {
    // 状态
    borrowRecords,
    myBorrowRecords,
    currentBorrowRecord,
    currentBorrowCount,
    hasOverdue,
    pagination,
    isLoading,
    searchParams,

    // 方法
    borrowBookAction,
    returnBookAction,
    renewBookAction,
    fetchBorrowList,
    fetchMyBorrowList,
    fetchBorrowById,
    fetchCurrentBorrowCount,
    checkOverdueBooks,
    processOverdueBooksAction,
    resetSearchParams,
    setSearchParams,
  };
});
