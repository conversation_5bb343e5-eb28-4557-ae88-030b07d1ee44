import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { User, RegisterForm, PageResult, QueryParams } from '@/types';
import { 
  getUserList, 
  getUserById, 
  createUser, 
  updateUser, 
  deleteUser, 
  toggleUserStatus 
} from '@/api/user';

export const useUserManagementStore = defineStore('userManagement', () => {
  // 状态
  const users = ref<User[]>([]);
  const currentUser = ref<User | null>(null);
  const pagination = ref({
    current: 1,
    size: 10,
    total: 0,
    pages: 0,
  });
  const isLoading = ref(false);
  const searchParams = ref<QueryParams & { 
    role?: string; 
    status?: number; 
  }>({
    current: 1,
    size: 10,
    keyword: '',
  });

  // 获取用户列表
  const fetchUserList = async (params?: Partial<typeof searchParams.value>) => {
    try {
      isLoading.value = true;
      
      if (params) {
        Object.assign(searchParams.value, params);
      }
      
      const response = await getUserList(searchParams.value);
      const { records, current, size, total, pages } = response.data;
      
      users.value = records;
      pagination.value = { current, size, total, pages };
      
      return response;
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 获取用户详情
  const fetchUserById = async (id: number) => {
    try {
      isLoading.value = true;
      const response = await getUserById(id);
      currentUser.value = response.data;
      return response;
    } catch (error) {
      console.error('获取用户详情失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 创建用户
  const createUserAction = async (userForm: RegisterForm, role: string) => {
    try {
      isLoading.value = true;
      const response = await createUser(userForm, role);
      // 创建成功后刷新列表
      await fetchUserList();
      return response;
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 更新用户
  const updateUserAction = async (id: number, userForm: Partial<User>) => {
    try {
      isLoading.value = true;
      const response = await updateUser(id, userForm);
      // 更新成功后刷新列表
      await fetchUserList();
      return response;
    } catch (error) {
      console.error('更新用户失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 删除用户
  const deleteUserAction = async (id: number) => {
    try {
      isLoading.value = true;
      const response = await deleteUser(id);
      // 删除成功后刷新列表
      await fetchUserList();
      return response;
    } catch (error) {
      console.error('删除用户失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 切换用户状态
  const toggleUserStatusAction = async (id: number) => {
    try {
      isLoading.value = true;
      const response = await toggleUserStatus(id);
      // 状态切换成功后刷新列表
      await fetchUserList();
      return response;
    } catch (error) {
      console.error('切换用户状态失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 重置搜索参数
  const resetSearchParams = () => {
    searchParams.value = {
      current: 1,
      size: 10,
      keyword: '',
    };
  };

  // 设置搜索参数
  const setSearchParams = (params: Partial<typeof searchParams.value>) => {
    Object.assign(searchParams.value, params);
  };

  return {
    // 状态
    users,
    currentUser,
    pagination,
    isLoading,
    searchParams,
    
    // 方法
    fetchUserList,
    fetchUserById,
    createUserAction,
    updateUserAction,
    deleteUserAction,
    toggleUserStatusAction,
    resetSearchParams,
    setSearchParams,
  };
});
