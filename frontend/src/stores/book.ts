import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { Book, BookForm, BookCategory, PageResult, QueryParams } from '@/types';
import { 
  getBookList, 
  getBookById, 
  createBook, 
  updateBook, 
  deleteBook, 
  toggleBookStatus,
  getCategoryTree,
  getAllCategories 
} from '@/api/book';

export const useBookStore = defineStore('book', () => {
  // 状态
  const books = ref<Book[]>([]);
  const currentBook = ref<Book | null>(null);
  const categories = ref<BookCategory[]>([]);
  const categoryTree = ref<BookCategory[]>([]);
  const pagination = ref({
    current: 1,
    size: 10,
    total: 0,
    pages: 0,
  });
  const isLoading = ref(false);
  const searchParams = ref<QueryParams & { categoryId?: number; status?: number }>({
    current: 1,
    size: 10,
    keyword: '',
  });

  // 获取图书列表
  const fetchBookList = async (params?: Partial<typeof searchParams.value>) => {
    try {
      isLoading.value = true;
      
      if (params) {
        Object.assign(searchParams.value, params);
      }
      
      const response = await getBookList(searchParams.value);
      const { records, current, size, total, pages } = response.data;
      
      books.value = records;
      pagination.value = { current, size, total, pages };
      
      return response;
    } catch (error) {
      console.error('获取图书列表失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 获取图书详情
  const fetchBookById = async (id: number) => {
    try {
      isLoading.value = true;
      const response = await getBookById(id);
      currentBook.value = response.data;
      return response;
    } catch (error) {
      console.error('获取图书详情失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 创建图书
  const createBookAction = async (bookForm: BookForm) => {
    try {
      isLoading.value = true;
      const response = await createBook(bookForm);
      // 创建成功后刷新列表
      await fetchBookList();
      return response;
    } catch (error) {
      console.error('创建图书失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 更新图书
  const updateBookAction = async (id: number, bookForm: BookForm) => {
    try {
      isLoading.value = true;
      const response = await updateBook(id, bookForm);
      // 更新成功后刷新列表
      await fetchBookList();
      return response;
    } catch (error) {
      console.error('更新图书失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 删除图书
  const deleteBookAction = async (id: number) => {
    try {
      isLoading.value = true;
      const response = await deleteBook(id);
      // 删除成功后刷新列表
      await fetchBookList();
      return response;
    } catch (error) {
      console.error('删除图书失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 切换图书状态
  const toggleBookStatusAction = async (id: number) => {
    try {
      isLoading.value = true;
      const response = await toggleBookStatus(id);
      // 状态切换成功后刷新列表
      await fetchBookList();
      return response;
    } catch (error) {
      console.error('切换图书状态失败:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  // 获取分类树
  const fetchCategoryTree = async () => {
    try {
      const response = await getCategoryTree();
      categoryTree.value = response.data;
      return response;
    } catch (error) {
      console.error('获取分类树失败:', error);
      throw error;
    }
  };

  // 获取所有分类
  const fetchAllCategories = async () => {
    try {
      const response = await getAllCategories();
      categories.value = response.data;
      return response;
    } catch (error) {
      console.error('获取分类列表失败:', error);
      throw error;
    }
  };

  // 重置搜索参数
  const resetSearchParams = () => {
    searchParams.value = {
      current: 1,
      size: 10,
      keyword: '',
    };
  };

  // 设置搜索参数
  const setSearchParams = (params: Partial<typeof searchParams.value>) => {
    Object.assign(searchParams.value, params);
  };

  return {
    // 状态
    books,
    currentBook,
    categories,
    categoryTree,
    pagination,
    isLoading,
    searchParams,
    
    // 方法
    fetchBookList,
    fetchBookById,
    createBookAction,
    updateBookAction,
    deleteBookAction,
    toggleBookStatusAction,
    fetchCategoryTree,
    fetchAllCategories,
    resetSearchParams,
    setSearchParams,
  };
});
